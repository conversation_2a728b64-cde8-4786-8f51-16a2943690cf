- super up app is separated from v chat that means its not included in v chat package
- To run the project first activate melos
```
dart pub global activate melos
```
then run this commend in the out root
```
melos bs
```
this code will run pub get for you

### 1- Super up is one of apps using v chat  you can check the demo from here
### check v chat sdk from here
<a href="https://github.com/hatemragab/v_chat_sdk">V_CHAT_SDK</a>


### Android Test

<a href="https://play.google.com/store/apps/details?id=com.superup.online" target="_blank"> <img src="https://user-images.githubusercontent.com/37384769/145644981-17ec8f75-be19-4cea-9322-52f1b31a15da.png" width ="300" /></a> <br />

### Web test

<a href="https://web.superupdev.online" target="_blank"> <img src="https://user-images.githubusercontent.com/37384769/221687646-1e2fa089-1a03-4fc7-9f6a-491e9bd5aae2.png" width ="120" /></a> <br />

### IOS

<a href="https://apps.apple.com/us/app/super-up/id6445877902" target="_blank"> <img src="https://user-images.githubusercontent.com/37384769/221687355-e7d19dd2-dc9a-4cf9-bf93-a8fb9e80379f.png" width ="120" /></a> <br />


### Mac version
<a href="https://apps.apple.com/us/app/super-up/id6445877902?mt=12" target="_blank"> <img src="https://user-images.githubusercontent.com/37384769/222607065-1bfd6d69-3102-46ce-870d-1ebefce0d599.jpeg" width ="120" /></a> <br />

### Postman endpoints documentation
<a href="https://documenter.getpostman.com/view/24524392/2s93Jox6Dq" target="_blank"> <img src="https://user-images.githubusercontent.com/37384769/223219009-1f863fc6-a90e-4cf8-acf4-7379027054ab.jpg" width ="120" /></a> <br />

### Admin panel test
<a href="http://admin.superupdev.online" target="_blank"> <img src="https://user-images.githubusercontent.com/37384769/225919154-bbd9db31-f151-4437-b02e-ee9acedf0e63.png" width ="120" /></a> <br />

### Windows version coming soon
<a href="https://apps.microsoft.com/detail/9PMDVDSG91F1?hl=en-eg" Windows </a> <br/>

-------------

## Check Our Full documentation
<a href="https://hatemragab.github.io/VChatSdk-Documentation/"  target="_blank"> VCHAT DOCS</a>  <br />


