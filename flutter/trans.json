["loginNowAllowedNowPleaseTryAgainLater", "dashboard", "notification", "total", "blocked", "deleted", "accepted", "notAccepted", "web", "android", "macOs", "windows", "other", "totalVisits", "totalMessages", "textMessages", "imageMessages", "videoMessages", "voiceMessages", "fileMessages", "infoMessages", "voiceCallMessages", "videoCallMessages", "locationMessages", "directChat", "group", "broadcast", "messageCounter", "roomCounter", "countries", "devices", "notificationTitle", "notificationDescription", "notificationsPage", "updateFeedBackEmail", "setMaxMessageForwardAndShare", "setNewPrivacyPolicyUrl", "forgetPasswordExpireTime", "callTimeoutInSeconds", "setMaxGroupMembers", "setMaxBroadcastMembers", "allowCalls", "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed", "allowAds", "allowMobileLogin", "allowWebLogin", "messages", "appleStoreAppUrl", "googlePlayAppUrl", "privacyUrl", "feedBackEmail", "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked", "allowSendMedia", "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked", "allowCreateBroadcast", "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked", "allowCreateGroups", "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked", "allowDesktopLogin", "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked", "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly", "ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats", "userProfile", "userInfo", "fullName", "bio", "noBio", "verifiedAt", "country", "registerStatus", "registerMethod", "banTo", "deletedAt", "createdAt", "updatedAt", "reports", "clickToSeeAllUserDevicesDetails", "allDeletedMessages", "voiceCallMessage", "totalRooms", "directRooms", "userAction", "status", "joinedAt", "saveLogin", "passwordIsRequired", "dashboard", "verified", "pending", "ios", "descriptionIsRequired", "seconds", "clickToSeeAllUserInformations", "clickToSeeAllUserCountries", "clickToSeeAllUserMessagesDetails", "clickToSeeAllUserRoomsDetails", "clickToSeeAllUserReports", "banAt", "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion"]