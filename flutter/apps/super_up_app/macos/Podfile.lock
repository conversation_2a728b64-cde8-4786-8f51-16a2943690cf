PODS:
  - agora_rtc_engine (0.0.1):
    - AgoraIrisRTC_macOS (= 4.3.2-build.1)
    - AgoraRtcEngine_macOS (= 4.3.2)
    - FlutterMacOS
  - AgoraIrisRTC_macOS (4.3.2-build.1)
  - AgoraRtcEngine_macOS (4.3.2):
    - AgoraRtcEngine_macOS/AIAEC (= 4.3.2)
    - AgoraRtcEngine_macOS/AINS (= 4.3.2)
    - AgoraRtcEngine_macOS/AudioBeauty (= 4.3.2)
    - AgoraRtcEngine_macOS/ClearVision (= 4.3.2)
    - AgoraRtcEngine_macOS/ContentInspect (= 4.3.2)
    - AgoraRtcEngine_macOS/FaceCapture (= 4.3.2)
    - AgoraRtcEngine_macOS/FaceDetection (= 4.3.2)
    - AgoraRtcEngine_macOS/LipSync (= 4.3.2)
    - AgoraRtcEngine_macOS/RtcBasic (= 4.3.2)
    - AgoraRtcEngine_macOS/ScreenCapture (= 4.3.2)
    - AgoraRtcEngine_macOS/SpatialAudio (= 4.3.2)
    - AgoraRtcEngine_macOS/VideoAv1CodecDec (= 4.3.2)
    - AgoraRtcEngine_macOS/VideoAv1CodecEnc (= 4.3.2)
    - AgoraRtcEngine_macOS/VideoCodecDec (= 4.3.2)
    - AgoraRtcEngine_macOS/VideoCodecEnc (= 4.3.2)
    - AgoraRtcEngine_macOS/VirtualBackground (= 4.3.2)
    - AgoraRtcEngine_macOS/VQA (= 4.3.2)
  - AgoraRtcEngine_macOS/AIAEC (4.3.2)
  - AgoraRtcEngine_macOS/AINS (4.3.2)
  - AgoraRtcEngine_macOS/AudioBeauty (4.3.2)
  - AgoraRtcEngine_macOS/ClearVision (4.3.2)
  - AgoraRtcEngine_macOS/ContentInspect (4.3.2)
  - AgoraRtcEngine_macOS/FaceCapture (4.3.2)
  - AgoraRtcEngine_macOS/FaceDetection (4.3.2)
  - AgoraRtcEngine_macOS/LipSync (4.3.2)
  - AgoraRtcEngine_macOS/RtcBasic (4.3.2)
  - AgoraRtcEngine_macOS/ScreenCapture (4.3.2)
  - AgoraRtcEngine_macOS/SpatialAudio (4.3.2)
  - AgoraRtcEngine_macOS/VideoAv1CodecDec (4.3.2)
  - AgoraRtcEngine_macOS/VideoAv1CodecEnc (4.3.2)
  - AgoraRtcEngine_macOS/VideoCodecDec (4.3.2)
  - AgoraRtcEngine_macOS/VideoCodecEnc (4.3.2)
  - AgoraRtcEngine_macOS/VirtualBackground (4.3.2)
  - AgoraRtcEngine_macOS/VQA (4.3.2)
  - appkit_ui_element_colors (1.0.0):
    - FlutterMacOS
  - audio_session (0.0.1):
    - FlutterMacOS
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - dynamic_color (0.0.2):
    - FlutterMacOS
  - emoji_picker_flutter (0.0.1):
    - FlutterMacOS
  - fc_native_video_thumbnail (0.0.1):
    - Flutter
    - FlutterMacOS
  - file_saver (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - Firebase/CoreOnly (11.2.0):
    - FirebaseCore (= 11.2.0)
  - Firebase/Messaging (11.2.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.2.0)
  - firebase_core (3.6.0):
    - Firebase/CoreOnly (~> 11.2.0)
    - FlutterMacOS
  - firebase_messaging (15.1.3):
    - Firebase/CoreOnly (~> 11.2.0)
    - Firebase/Messaging (~> 11.2.0)
    - firebase_core
    - FlutterMacOS
  - FirebaseCore (11.2.0):
    - FirebaseCoreInternal (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.4.2):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.4.0):
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.2.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - flutter_image_compress_macos (1.0.0):
    - FlutterMacOS
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - gal (1.0.0):
    - Flutter
    - FlutterMacOS
  - geolocator_apple (1.2.0):
    - FlutterMacOS
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - iris_method_channel (0.0.1):
    - FlutterMacOS
  - just_audio (0.0.1):
    - FlutterMacOS
  - macos_ui (0.1.0):
    - FlutterMacOS
  - macos_window_utils (1.0.0):
    - FlutterMacOS
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - pasteboard (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - record_darwin (1.0.0):
    - FlutterMacOS
  - screen_retriever_macos (0.0.1):
    - FlutterMacOS
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.47.0):
    - sqlite3/common (= 3.47.0)
  - sqlite3/common (3.47.0)
  - sqlite3/dbstatvtab (3.47.0):
    - sqlite3/common
  - sqlite3/fts5 (3.47.0):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.47.0):
    - sqlite3/common
  - sqlite3/rtree (3.47.0):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - FlutterMacOS
    - sqlite3 (~> 3.47.0)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - window_manager (0.2.0):
    - FlutterMacOS

DEPENDENCIES:
  - agora_rtc_engine (from `Flutter/ephemeral/.symlinks/plugins/agora_rtc_engine/macos`)
  - appkit_ui_element_colors (from `Flutter/ephemeral/.symlinks/plugins/appkit_ui_element_colors/macos`)
  - audio_session (from `Flutter/ephemeral/.symlinks/plugins/audio_session/macos`)
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/darwin`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - dynamic_color (from `Flutter/ephemeral/.symlinks/plugins/dynamic_color/macos`)
  - emoji_picker_flutter (from `Flutter/ephemeral/.symlinks/plugins/emoji_picker_flutter/macos`)
  - fc_native_video_thumbnail (from `Flutter/ephemeral/.symlinks/plugins/fc_native_video_thumbnail/darwin`)
  - file_saver (from `Flutter/ephemeral/.symlinks/plugins/file_saver/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - firebase_messaging (from `Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos`)
  - flutter_image_compress_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_image_compress_macos/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - gal (from `Flutter/ephemeral/.symlinks/plugins/gal/darwin`)
  - geolocator_apple (from `Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos`)
  - iris_method_channel (from `Flutter/ephemeral/.symlinks/plugins/iris_method_channel/macos`)
  - just_audio (from `Flutter/ephemeral/.symlinks/plugins/just_audio/macos`)
  - macos_ui (from `Flutter/ephemeral/.symlinks/plugins/macos_ui/macos`)
  - macos_window_utils (from `Flutter/ephemeral/.symlinks/plugins/macos_window_utils/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - pasteboard (from `Flutter/ephemeral/.symlinks/plugins/pasteboard/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - photo_manager (from `Flutter/ephemeral/.symlinks/plugins/photo_manager/macos`)
  - record_darwin (from `Flutter/ephemeral/.symlinks/plugins/record_darwin/macos`)
  - screen_retriever_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin`)
  - sqlite3_flutter_libs (from `Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - video_player_avfoundation (from `Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)
  - webview_flutter_wkwebview (from `Flutter/ephemeral/.symlinks/plugins/webview_flutter_wkwebview/darwin`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)

SPEC REPOS:
  trunk:
    - AgoraIrisRTC_macOS
    - AgoraRtcEngine_macOS
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - sqlite3

EXTERNAL SOURCES:
  agora_rtc_engine:
    :path: Flutter/ephemeral/.symlinks/plugins/agora_rtc_engine/macos
  appkit_ui_element_colors:
    :path: Flutter/ephemeral/.symlinks/plugins/appkit_ui_element_colors/macos
  audio_session:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_session/macos
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/darwin
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  dynamic_color:
    :path: Flutter/ephemeral/.symlinks/plugins/dynamic_color/macos
  emoji_picker_flutter:
    :path: Flutter/ephemeral/.symlinks/plugins/emoji_picker_flutter/macos
  fc_native_video_thumbnail:
    :path: Flutter/ephemeral/.symlinks/plugins/fc_native_video_thumbnail/darwin
  file_saver:
    :path: Flutter/ephemeral/.symlinks/plugins/file_saver/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  firebase_messaging:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos
  flutter_image_compress_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_image_compress_macos/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  gal:
    :path: Flutter/ephemeral/.symlinks/plugins/gal/darwin
  geolocator_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos
  iris_method_channel:
    :path: Flutter/ephemeral/.symlinks/plugins/iris_method_channel/macos
  just_audio:
    :path: Flutter/ephemeral/.symlinks/plugins/just_audio/macos
  macos_ui:
    :path: Flutter/ephemeral/.symlinks/plugins/macos_ui/macos
  macos_window_utils:
    :path: Flutter/ephemeral/.symlinks/plugins/macos_window_utils/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  pasteboard:
    :path: Flutter/ephemeral/.symlinks/plugins/pasteboard/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  photo_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/photo_manager/macos
  record_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/record_darwin/macos
  screen_retriever_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin
  sqlite3_flutter_libs:
    :path: Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  video_player_avfoundation:
    :path: Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos
  webview_flutter_wkwebview:
    :path: Flutter/ephemeral/.symlinks/plugins/webview_flutter_wkwebview/darwin
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos

SPEC CHECKSUMS:
  agora_rtc_engine: 4a1ade5822e2e2756d02e9f85d96c7fcf2cfce3d
  AgoraIrisRTC_macOS: b5997a1d17bedcea8871b3c57b3768a45e940507
  AgoraRtcEngine_macOS: 418a85d7e4a32f6314d9ced51228a330b71f8015
  appkit_ui_element_colors: 39bb2d80be3f19b152ccf4c70d5bbe6cba43d74a
  audio_session: dea1f41890dbf1718f04a56f1d6150fd50039b72
  connectivity_plus: 4c41c08fc6d7c91f63bc7aec70ffe3730b04f563
  device_info_plus: 74e614483d05c89290d30a4c8feae15d555f7427
  dynamic_color: 2eaa27267de1ca20d879fbd6e01259773fb1670f
  emoji_picker_flutter: 533634326b1c5de9a181ba14b9758e6dfe967a20
  fc_native_video_thumbnail: 927d4dcfd4c7e9f2cc1a20bb52dfee83de3792c2
  file_saver: 44e6fbf666677faf097302460e214e977fdd977b
  file_selector_macos: cc3858c981fe6889f364731200d6232dac1d812d
  Firebase: 98e6bf5278170668a7983e12971a66b2cd57fc8c
  firebase_core: e88f946a4601cb1854178cb07da241bba5a6508e
  firebase_messaging: e1b1c1504659e13d66131f62ec22919293cd0d11
  FirebaseCore: a282032ae9295c795714ded2ec9c522fc237f8da
  FirebaseCoreInternal: 35731192cab10797b88411be84940d2beb33a238
  FirebaseInstallations: 6ef4a1c7eb2a61ee1f74727d7f6ce2e72acf1414
  FirebaseMessaging: c9ec7b90c399c7a6100297e9d16f8a27fc7f7152
  flutter_image_compress_macos: c26c3c13ea0f28ae6dea4e139b3292e7729f99f1
  flutter_local_notifications: 7062189aabf7f50938a7b8b6614ffa97656eb0bf
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  gal: 61e868295d28fe67ffa297fae6dacebf56fd53e1
  geolocator_apple: 72a78ae3f3e4ec0db62117bd93e34523f5011d58
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  iris_method_channel: fba932d668df5b2a085f19bcd0b1f23d667a83cd
  just_audio: 9b67ca7b97c61cfc9784ea23cd8cc55eb226d489
  macos_ui: 6229a8922cd97bafb7d9636c8eb8dfb0744183ca
  macos_window_utils: 933f91f64805e2eb91a5bd057cf97cd097276663
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: f5790acc797bf17c3e959e9d6cf162cc68ff7523
  pasteboard: 9b69dba6fedbb04866be632205d532fe2f6b1d99
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  photo_manager: ff695c7a1dd5bc379974953a2b5c0a293f7c4c8a
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  record_darwin: a0d515a0ef78c440c123ea3ac76184c9927a94d6
  screen_retriever_macos: 776e0fa5d42c6163d2bf772d22478df4b302b161
  share_plus: fd717ef89a2801d3491e737630112b80c310640e
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: a553b1fd6fe66f53bbb0fe5b4f5bab93f08d7a13
  sqlite3: 0aa20658a9b238a3b1ff7175eb7bdd863b0ab4fd
  sqlite3_flutter_libs: f0b7a85544d8bac7b8bac12eac7d05bcfdd786d0
  url_launcher_macos: c82c93949963e55b228a30115bd219499a6fe404
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  wakelock_plus: 4783562c9a43d209c458cb9b30692134af456269
  webview_flutter_wkwebview: 0982481e3d9c78fd5c6f62a002fcd24fc791f1e4
  window_manager: 3a1844359a6295ab1e47659b1a777e36773cd6e8

PODFILE CHECKSUM: 8d40c19d3cbdb380d870685c3a564c989f1efa52

COCOAPODS: 1.15.2
