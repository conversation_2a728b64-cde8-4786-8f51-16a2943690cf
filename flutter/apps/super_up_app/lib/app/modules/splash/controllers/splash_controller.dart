// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:developer';

import 'package:background_downloader/background_downloader.dart';
import 'package:flutter/cupertino.dart';
import 'package:get_it/get_it.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:super_up/app/modules/auth/register/views/register_view.dart';
import 'package:super_up/app/modules/home/<USER>/calls_tab/controllers/calls_tab_controller.dart';
import 'package:super_up/app/modules/home/<USER>/rooms_tab/controllers/rooms_tab_controller.dart';
import 'package:super_up/app/modules/home/<USER>/story_tab/controllers/story_tab_controller.dart';
import 'package:super_up/app/modules/home/<USER>/users_tab/controllers/users_tab_controller.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_platform/v_platform.dart';

import '../../../../main.dart';
import '../../../core/app_config/app_config_controller.dart';
import '../../../core/services/balance_service.dart';
import '../../../core/services/claimed_gifts_service.dart';
import '../../auth/waiting_list/views/waiting_list_page.dart';
import '../../home/<USER>/views/home_view.dart';
import '../views/splash_view.dart';

bool isShow450Error = false;

class SplashController extends SLoadingController<String> {
  String get version => data;

  SplashController() : super(SLoadingState(""));

  BuildContext get context => navigatorKey.currentState!.context;
  final appConfigController = GetIt.I.get<VAppConfigController>();

  @override
  void onInit() {
    getAppVersion();
    startNavigate();
    _init450Listener();
    // checkUpdates();
  }

  void _init450Listener() async {
    // await Future.delayed(const Duration(milliseconds: 100));
    try {
      unAuthStream450Error.stream.listen((event) async {
        if (isShow450Error == true) return;
        isShow450Error = true;
        await Future.delayed(const Duration(seconds: 1));
        await VChatController.I.profileApi.logout();
        AppAuth.setProfileNull();
        await VAppPref.clear();
        await VAppAlert.showOkAlertDialog(
          context: context,
          title: S.of(context).loginAgain,
          content: S.of(context).yourSessionIsEndedPleaseLoginAgain,
        );

        // Clean up GetIt controllers before navigation
        _cleanupGetItControllers();

        VChatController.I.navigatorKey.currentContext!.toPage(
          const SplashView(),
          withAnimation: false,
          removeAll: true,
        );
        AppAuth.setProfileNull();
      });
    } catch (err) {
      //
    }
  }

  Future<void> getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final version = packageInfo.version;
      final buildNumber = packageInfo.buildNumber;
      value.data = "$version+$buildNumber";
      setStateSuccess();
      await appConfigController.refreshAppConfig();
      final c = VAppConfigController.appConfig;
      VChatController.I.updateConfig(VChatController.I.vChatConfig.copyWith(
        maxForward: c.maxForward,
        maxBroadcastMembers: c.maxBroadcastMembers,
        maxGroupMembers: c.maxGroupMembers,
      ));
    } catch (err) {
      log(err.toString());
    }
  }

  void _homeNav() {
    context.toPageAndRemoveAllWithOutAnimation(const HomeView());
  }

  void startNavigate() async {
    if (VPlatforms.isDeskTop) {
      await _setDesktopAutoUpdater();
    }
    if (VPlatforms.isMobile) {
      final request = RequestConfiguration(
        testDeviceIds: [],
      );
      await MobileAds.instance.initialize();
      await MobileAds.instance.updateRequestConfiguration(request);
    }
    if (VPlatforms.isMobile) {
      await VFileUtils.refreshAppPath();
      await FileDownloader().trackTasks();
      FileDownloader().configureNotificationForGroup(
        "files",
        running:
            const TaskNotification(SConstants.appName, 'File 📁 : {filename}'),
        progressBar: true,
        tapOpensFile: true,
      );
    }

    await Future.delayed(const Duration(milliseconds: 650));

    // Initialize multi-account manager
    await MultiAccountManager.instance.initialize();

    // Initialize balance service for the current account
    await BalanceService.instance.init();

    // Initialize claimed gifts service
    ClaimedGiftsService.instance.init();

    // Check if we have any accounts
    final currentAccount = MultiAccountManager.instance.currentAccount;
    if (currentAccount != null) {
      if (currentAccount.profile.registerStatus == RegisterStatus.accepted) {
        _homeNav();
      } else {
        context.toPage(
          WaitingListPage(
            profile: currentAccount.profile,
          ),
          withAnimation: true,
          removeAll: true,
        );
      }
      return;
    }

    // Fallback to legacy login check for migration
    final isLogin = VAppPref.getBool(SStorageKeys.isLogin.name);
    if (isLogin) {
      final map = VAppPref.getMap(SStorageKeys.myProfile.name);
      if (map != null) {
        final myProfile = SMyProfile.fromMap(map);
        final accessToken =
            VAppPref.getHashedString(key: SStorageKeys.vAccessToken.name);

        // Migrate to multi-account system
        await MultiAccountManager.instance.addAccount(
          email: myProfile.email,
          accessToken: accessToken ?? '',
          profile: myProfile,
        );

        final accountId = AccountSession.createAccountId(
            myProfile.email, myProfile.baseUser.id);
        await MultiAccountManager.instance.switchToAccount(accountId);

        if (myProfile.registerStatus == RegisterStatus.accepted) {
          _homeNav();
        } else {
          context.toPage(
            WaitingListPage(
              profile: myProfile,
            ),
            withAnimation: true,
            removeAll: true,
          );
        }
        return;
      }
    }

    // No accounts found, go to registration
    context.toPage(
      const RegisterView(),
      withAnimation: true,
      removeAll: true,
    );
  }

  @override
  void onClose() {}

  Future _setDesktopAutoUpdater() async {}

// void checkUpdates() async {
//   if (VPlatforms.isMobile) {
//     final newVersionPlus = NewVersionPlus();
//     try {
//       await newVersionPlus.showAlertIfNecessary(
//           context: navigatorKey.currentState!.context);
//     } catch (err) {
//       if (kDebugMode) print(err);
//     }
//   }
// }

  void _cleanupGetItControllers() {
    // Safely close and unregister controllers to prevent disposed controller errors
    if (GetIt.I.isRegistered<RoomsTabController>()) {
      try {
        GetIt.I.get<RoomsTabController>().onClose();
        GetIt.I.unregister<RoomsTabController>();
      } catch (e) {
        // Ignore errors during cleanup
      }
    }

    if (GetIt.I.isRegistered<CallsTabController>()) {
      try {
        GetIt.I.get<CallsTabController>().onClose();
        GetIt.I.unregister<CallsTabController>();
      } catch (e) {
        // Ignore errors during cleanup
      }
    }

    if (GetIt.I.isRegistered<UsersTabController>()) {
      try {
        GetIt.I.get<UsersTabController>().onClose();
        GetIt.I.unregister<UsersTabController>();
      } catch (e) {
        // Ignore errors during cleanup
      }
    }

    if (GetIt.I.isRegistered<StoryTabController>()) {
      try {
        GetIt.I.unregister<StoryTabController>();
      } catch (e) {
        // Ignore errors during cleanup
      }
    }
  }
}
