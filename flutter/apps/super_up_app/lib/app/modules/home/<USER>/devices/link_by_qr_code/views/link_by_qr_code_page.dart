// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';
import '../controllers/link_by_qr_code_controller.dart';
import '../states/link_by_qr_code_state.dart';

class LinkByQrCodePage extends StatefulWidget {
  const LinkByQrCodePage({super.key});

  @override
  State<LinkByQrCodePage> createState() => _LinkByQrCodePageState();
}

class _LinkByQrCodePageState extends State<LinkByQrCodePage> {
  late final LinkByQrCodeController controller;

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text(S.of(context).linkByQrCode),
      ),
      child: <PERSON><PERSON><PERSON>(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            children: [
              ValueListenableBuilder<SLoadingState<LinkByQrCodeState?>>(
                valueListenable: controller,
                builder: (_, value, ___) => VAsyncWidgetsBuilder(
                  loadingState: value.loadingState,
                  onRefresh: controller.getData,
                  successWidget: () {
                    return Text(S.of(context).success);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    controller = LinkByQrCodeController();
    controller.onInit();
  }

  @override
  void dispose() {
    controller.onClose();
    super.dispose();
  }
}
