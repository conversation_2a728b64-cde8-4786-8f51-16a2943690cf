// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_message_page/v_chat_message_page.dart';
import 'package:v_chat_receive_share/v_chat_receive_share.dart';
// import 'package:v_chat_receive_share/v_chat_receive_share.dart';

// import 'package:v_chat_receive_share/v_chat_receive_share.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_platform/v_platform.dart';

import '../../../../core/api_service/profile/profile_api_service.dart';
import '../../../../core/controllers/version_checker_controller.dart';
import '../../mobile/calls_tab/controllers/calls_tab_controller.dart';
import '../../mobile/rooms_tab/controllers/rooms_tab_controller.dart';
import '../../mobile/story_tab/controllers/story_tab_controller.dart';
import '../../mobile/users_tab/controllers/users_tab_controller.dart';
import '../../../live_stream/controllers/go_live_controller.dart';
import '../../../live_stream/controllers/watch_live_controller.dart';
import '../../../live_stream/controllers/live_stream_controller.dart';
import '../../../live_stream/controllers/live_stream_chat_controller.dart';

class HomeController extends SLoadingController<int> {
  int totalChatUnRead = 0;
  final versionCheckerController = GetIt.I.get<VersionCheckerController>();
  int get tabIndex => data;
  final ProfileApiService profileApiService;
  final BuildContext context;
  IconData fabIcon = Icons.message;

  HomeController(this.profileApiService, this.context)
      : super(SLoadingState(0));

  late final StreamSubscription _unReadStream;

  @override
  void onInit() {
    _registerLazySingletons();
    _connectToVChatSdk();
    _checkVersion();
    _updateProfile();
    _unReadStream = VChatController
        .I.nativeApi.streams.totalUnreadRoomsCountStream
        .listen((event) {
      totalChatUnRead = event.count;
      notifyListeners();
    });
  }

  @override
  void onClose() {
    _unregister();
    _unReadStream.cancel();
  }

  void _connectToVChatSdk() async {
    await VChatController.I.profileApi.connect();
    vInitReceiveShareHandler();
    _setVisit();
    if (VPlatforms.isMobile) {
      vInitCallListener(context);
      CallKeepHandler.I.checkLastCall();
      _setVoipKey();
    }
  }

  void _setVisit() async {
    vSafeApiCall(
      request: () async {
        return profileApiService.setVisit();
      },
      onSuccess: (response) {},
      ignoreTimeoutAndNoInternet: true,
    );
  }

  void _checkVersion() async {
    await versionCheckerController.checkForUpdates(context, false);
  }

  void _registerLazySingletons() {
    // Check if controllers are already registered to avoid conflicts
    if (!GetIt.I.isRegistered<CallsTabController>()) {
      GetIt.I.registerLazySingleton<CallsTabController>(
        () => CallsTabController(),
      );
    }

    if (!GetIt.I.isRegistered<UsersTabController>()) {
      GetIt.I.registerLazySingleton<UsersTabController>(
        () => UsersTabController(GetIt.I.get<ProfileApiService>()),
      );
    }

    if (!GetIt.I.isRegistered<StoryTabController>()) {
      GetIt.I.registerLazySingleton<StoryTabController>(
        () => StoryTabController(),
      );
    }

    if (!GetIt.I.isRegistered<RoomsTabController>()) {
      GetIt.I.registerLazySingleton<RoomsTabController>(
        () => RoomsTabController(),
      );
    }

    // Live Stream Controllers
    if (!GetIt.I.isRegistered<GoLiveController>()) {
      GetIt.I.registerLazySingleton<GoLiveController>(
        () => GoLiveController(),
      );
    }

    if (!GetIt.I.isRegistered<WatchLiveController>()) {
      GetIt.I.registerLazySingleton<WatchLiveController>(
        () => WatchLiveController(),
      );
    }

    if (!GetIt.I.isRegistered<LiveStreamController>()) {
      GetIt.I.registerLazySingleton<LiveStreamController>(
        () => LiveStreamController(),
      );
    }

    if (!GetIt.I.isRegistered<LiveStreamChatController>()) {
      GetIt.I.registerLazySingleton<LiveStreamChatController>(
        () => LiveStreamChatController(),
      );
    }
  }

  void _unregister() {
    // Safely close and unregister controllers
    if (GetIt.I.isRegistered<RoomsTabController>()) {
      GetIt.I.get<RoomsTabController>().onClose();
      GetIt.I.unregister<RoomsTabController>();
    }

    if (GetIt.I.isRegistered<CallsTabController>()) {
      GetIt.I.get<CallsTabController>().onClose();
      GetIt.I.unregister<CallsTabController>();
    }

    if (GetIt.I.isRegistered<UsersTabController>()) {
      GetIt.I.get<UsersTabController>().onClose();
      GetIt.I.unregister<UsersTabController>();
    }

    if (GetIt.I.isRegistered<StoryTabController>()) {
      GetIt.I.unregister<StoryTabController>();
    }
  }

  void _updateProfile() async {
    final newProfile = await profileApiService.getMyProfile();
    await VAppPref.setMap(SStorageKeys.myProfile.name, newProfile.toMap());
    AppAuth.setProfileNull();
  }

  void _setVoipKey() async {
    if (VPlatforms.isIOS) {
      final token = await CallKeepHandler.I.getVoipIos();
      print("----------------------------------------------------------");
      print(token);
      print("----------------------------------------------------------");
      await VChatController.I.nativeApi.remote.profile
          .addPushKey(fcm: null, voipKey: token);
    }
  }
}
