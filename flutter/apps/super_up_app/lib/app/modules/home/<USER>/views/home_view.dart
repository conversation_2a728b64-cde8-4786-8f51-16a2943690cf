// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up/app/modules/home/<USER>/calls_tab/views/calls_tab_view.dart';
import 'package:super_up/app/modules/home/<USER>/rooms_tab/views/rooms_tab_view.dart';
import 'package:super_up/app/modules/home/<USER>/users_tab/views/users_tab_view.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';
import '../../../../core/api_service/profile/profile_api_service.dart';
import '../../home_wide_modules/home/<USER>/home_wide_view.dart';
import '../../mobile/settings_tab/views/settings_tab_view.dart';
import '../../mobile/story_tab/views/story_tab_view.dart';
import '../controllers/home_controller.dart';
import '../widgets/chat_un_read_counter.dart';

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> with TickerProviderStateMixin {
  late final HomeController controller;
  final sizer = GetIt.I.get<AppSizeHelper>();

  @override
  void initState() {
    super.initState();
    controller = HomeController(
      GetIt.I.get<ProfileApiService>(),
      context,
    );
    controller.onInit();
  }

  @override
  void dispose() {
    controller.onClose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (sizer.isWide(context)) {
      return const HomeWideView();
    }
    return ValueListenableBuilder<SLoadingState<int>>(
      valueListenable: controller,
      builder: (_, value, __) {
        return CupertinoPageScaffold(
          child: Stack(
            children: [
              // Tab content
              Padding(
                padding:
                    const EdgeInsets.only(bottom: 50), // Space for bottom nav
                child: IndexedStack(
                  index: value.data,
                  children: const [
                    RoomsTabView(),
                    StoryTabView(),
                    CallsTabView(),
                    UsersTabView(),
                    SettingsTabView(),
                  ],
                ),
              ),
              // Bottom navigation bar
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Container(
                  decoration: const BoxDecoration(
                    color: CupertinoColors.systemBackground,
                    border: Border(
                      top: BorderSide(
                        color: CupertinoColors.separator,
                        width: 0.5,
                      ),
                    ),
                  ),
                  child: SafeArea(
                    top: false,
                    child: SizedBox(
                      height: 50,
                      child: Row(
                        children: [
                          _buildTabItem(
                            context: context,
                            index: 0,
                            currentIndex: value.data,
                            icon: ValueListenableBuilder<SLoadingState<int>>(
                              valueListenable: controller,
                              builder: (context, value, child) {
                                return Stack(
                                  children: [
                                    const Icon(CupertinoIcons.chat_bubble_2),
                                    PositionedDirectional(
                                      end: 0,
                                      child: ChatUnReadWidget(
                                        unReadCount: controller.totalChatUnRead,
                                        width: 15,
                                        height: 15,
                                      ),
                                    )
                                  ],
                                );
                              },
                            ),
                            label: S.of(context).chats,
                            onTap: () {
                              controller.value.data = 0;
                              controller.update();
                            },
                          ),
                          _buildTabItem(
                            context: context,
                            index: 1,
                            currentIndex: value.data,
                            icon: const Icon(CupertinoIcons.play_circle),
                            label: S.of(context).stories,
                            onTap: () {
                              controller.value.data = 1;
                              controller.update();
                            },
                          ),
                          _buildTabItem(
                            context: context,
                            index: 2,
                            currentIndex: value.data,
                            icon: const Icon(CupertinoIcons.phone),
                            label: S.of(context).phone,
                            onTap: () {
                              controller.value.data = 2;
                              controller.update();
                            },
                          ),
                          _buildTabItem(
                            context: context,
                            index: 3,
                            currentIndex: value.data,
                            icon: const Icon(CupertinoIcons.person_2),
                            label: S.of(context).users,
                            onTap: () {
                              controller.value.data = 3;
                              controller.update();
                            },
                          ),
                          _buildTabItem(
                            context: context,
                            index: 4,
                            currentIndex: value.data,
                            icon: ValueListenableBuilder<SVersion>(
                              valueListenable:
                                  controller.versionCheckerController,
                              builder: (context, value, child) {
                                return Stack(
                                  children: [
                                    const Icon(CupertinoIcons.settings),
                                    PositionedDirectional(
                                      end: 0,
                                      child: ChatUnReadWidget(
                                        unReadCount:
                                            value.isNeedUpdates ? 1 : 0,
                                        width: 15,
                                        height: 15,
                                      ),
                                    )
                                  ],
                                );
                              },
                            ),
                            label: S.of(context).settings,
                            onTap: () {
                              controller.value.data = 4;
                              controller.update();
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTabItem({
    required BuildContext context,
    required int index,
    required int currentIndex,
    required Widget icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final isSelected = index == currentIndex;
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        behavior: HitTestBehavior.opaque,
        child: SizedBox(
          height: 50,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconTheme(
                data: IconThemeData(
                  color: isSelected
                      ? CupertinoTheme.of(context).primaryColor
                      : CupertinoColors.inactiveGray,
                  size: 24,
                ),
                child: icon,
              ),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: isSelected
                      ? CupertinoTheme.of(context).primaryColor
                      : CupertinoColors.inactiveGray,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
