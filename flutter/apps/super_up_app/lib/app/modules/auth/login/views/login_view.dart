// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';

import '../../../../core/api_service/auth/auth_api_service.dart';
import '../../../../core/api_service/profile/profile_api_service.dart';
import '../../../../core/widgets/wide_constraints.dart';
import '../../forget_password_otp/views/forget_password_page.dart';
import '../../widgets/auth_header.dart';
import '../controllers/login_controller.dart';

class LoginView extends StatefulWidget {
  final bool showBackButton;

  const LoginView({
    super.key,
    this.showBackButton = false,
  });

  @override
  State<LoginView> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> {
  late final LoginController controller;
  bool _isPasswordVisible = false;

  @override
  void initState() {
    super.initState();
    controller = LoginController(
      GetIt.I.get<AuthApiService>(),
      GetIt.I.get<ProfileApiService>(),
      isAddingAccount: widget.showBackButton,
    );
    controller.onInit();
  }

  @override
  void dispose() {
    controller.onClose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, sizingInformation) => WideConstraints(
        enable: sizingInformation.isDesktop,
        child: CupertinoPageScaffold(
          navigationBar: widget.showBackButton
              ? CupertinoNavigationBar(
                  leading: CupertinoNavigationBarBackButton(
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  middle: Text(S.of(context).addAccount),
                  backgroundColor:
                      CupertinoColors.systemBackground.resolveFrom(context),
                )
              : null,
          child: SafeArea(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  if (!widget.showBackButton) const AuthHeader(),
                  if (widget.showBackButton)
                    const SizedBox(
                        height:
                            20), // Add some spacing when back button is shown
                  SizedBox(
                    height: context.height * .02,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        STextFiled(
                          controller: controller.emailController,
                          textHint: S.of(context).email,
                          prefix: const Icon(Icons.email_outlined),
                          autocorrect: false,
                          inputType: TextInputType.emailAddress,
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        STextFiled(
                          autocorrect: false,
                          controller: controller.passwordController,
                          textHint: S.of(context).password,
                          prefix: const Icon(CupertinoIcons.lock_fill),
                          obscureText: !_isPasswordVisible,
                          suffix: IconButton(
                            icon: Icon(
                              _isPasswordVisible
                                  ? Icons.visibility
                                  : Icons.visibility_off,
                            ),
                            onPressed: () {
                              setState(() {
                                _isPasswordVisible = !_isPasswordVisible;
                              });
                            },
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        GestureDetector(
                            onTap: () {
                              context.toPage(const ForgetPasswordPage());
                            },
                            child: S
                                .of(context)
                                .forgetPassword
                                .text
                                .color(Colors.blue)
                                .black),
                        const SizedBox(
                          height: 40,
                        ),
                        SElevatedButton(
                          title: S.of(context).login,
                          onPress: () => controller.login(context),
                        ),
                        const SizedBox(
                          height: 30,
                        ),
                        const SizedBox(
                          height: 15,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            S.of(context).needNewAccount.text,
                            const SizedBox(
                              width: 5,
                            ),
                            GestureDetector(
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              child: S
                                  .of(context)
                                  .register
                                  .text
                                  .color(Colors.blue)
                                  .black,
                            )
                          ],
                        )
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
