// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:dart_openai/dart_openai.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class OpenAIService {
  static final OpenAIService _instance = OpenAIService._internal();
  factory OpenAIService() => _instance;
  OpenAIService._internal();

  static const String _apiKey =
      "********************************************************************************************************************************************************************";

  bool _isInitialized = false;
  bool _isWebSearchEnabled = false;

  void initialize() {
    if (!_isInitialized) {
      OpenAI.apiKey = _apiKey;
      _isInitialized = true;
      if (kDebugMode) {
        print('OpenAI Service initialized');
      }
    }
  }

  // Web search toggle methods
  void enableWebSearch() {
    _isWebSearchEnabled = true;
    if (kDebugMode) {
      print('Web search enabled');
    }
  }

  void disableWebSearch() {
    _isWebSearchEnabled = false;
    if (kDebugMode) {
      print('Web search disabled');
    }
  }

  bool get isWebSearchEnabled => _isWebSearchEnabled;

  Future<String> sendMessage(String message) async {
    try {
      if (kDebugMode) {
        print('OpenAI sendMessage called with: "$message"');
      }

      if (!_isInitialized) {
        initialize();
      }

      // Check if API key is set
      if (_apiKey == "YOUR_OPENAI_API_KEY_HERE") {
        return "Please configure your OpenAI API key in the OpenAIService class.";
      }

      // Check if the message requires web search and web search is enabled
      if (_isWebSearchEnabled && _shouldPerformWebSearch(message)) {
        if (kDebugMode) {
          print('Detected web search request: $message');
        }

        // Extract search query from the message
        final searchQuery = _extractSearchQuery(message);

        // Perform web search
        final searchResults = await performWebSearch(searchQuery);

        // Create a system message for processing search results
        final systemMessage = OpenAIChatCompletionChoiceMessageModel(
          content: [
            OpenAIChatCompletionChoiceMessageContentItemModel.text(
                "You are a helpful AI assistant. The user asked for web search results and here are the results. Provide a natural, helpful response based on these search results. Be conversational and helpful."),
          ],
          role: OpenAIChatMessageRole.system,
        );

        final userMessage = OpenAIChatCompletionChoiceMessageModel(
          content: [
            OpenAIChatCompletionChoiceMessageContentItemModel.text(
                "User's original request: $message\n\nSearch results:\n$searchResults\n\nPlease provide a helpful response based on these search results."),
          ],
          role: OpenAIChatMessageRole.user,
        );

        final chatCompletion = await OpenAI.instance.chat.create(
          model: "gpt-4",
          messages: [systemMessage, userMessage],
          maxTokens: 400,
          temperature: 0.7,
        );

        final response =
            chatCompletion.choices.first.message.content?.first.text ??
                searchResults;

        return response;
      }

      // Regular chat without web search
      final systemMessage = OpenAIChatCompletionChoiceMessageModel(
        content: [
          OpenAIChatCompletionChoiceMessageContentItemModel.text(
              "You are a helpful, intelligent AI assistant. Provide natural, conversational responses. Always give specific, accurate information - never use placeholder text or templates. When asked about dates, times, or current information, use the context provided. Be friendly, concise, and genuinely helpful. Respond as if you're having a real conversation with a friend."),
        ],
        role: OpenAIChatMessageRole.system,
      );

      // Add current date context to the message
      final now = DateTime.now();
      final currentDate = "${now.day}/${now.month}/${now.year}";
      final currentDay = _getDayName(now.weekday);

      final contextualMessage =
          "Current date: $currentDate ($currentDay). User message: $message";

      final userMessage = OpenAIChatCompletionChoiceMessageModel(
        content: [
          OpenAIChatCompletionChoiceMessageContentItemModel.text(
              contextualMessage),
        ],
        role: OpenAIChatMessageRole.user,
      );

      final chatCompletion = await OpenAI.instance.chat.create(
        model: "gpt-4",
        messages: [systemMessage, userMessage],
        maxTokens: 150,
        temperature: 0.7,
      );

      final response =
          chatCompletion.choices.first.message.content?.first.text ??
              "Sorry, I couldn't generate a response.";

      return response;
    } catch (e) {
      if (kDebugMode) {
        print('OpenAI Error: $e');
      }

      // Return a friendly error message
      if (e.toString().contains('API key')) {
        return "Please configure your OpenAI API key to use the AI assistant.";
      } else if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        return "Sorry, I'm having trouble connecting. Please check your internet connection.";
      } else {
        return "Sorry, I'm experiencing some technical difficulties. Please try again later.";
      }
    }
  }

  // Method to simulate typing delay for better UX
  Future<void> simulateTyping() async {
    await Future.delayed(const Duration(milliseconds: 1500));
  }

  // Generate image using GPT-4.1 with image generation tool
  Future<String?> generateImage(String prompt) async {
    try {
      if (kDebugMode) {
        print('Generating image with GPT-4.1 and prompt: $prompt');
      }

      // Use the new Responses API with GPT-4.1 and image generation tool
      final requestBody = {
        "model": "gpt-4.1-mini",
        "input": "Generate an image: $prompt",
        "tools": [
          {"type": "image_generation"}
        ],
        "tool_choice": {"type": "image_generation"}, // Force image generation
      };

      final response = await http.post(
        Uri.parse('https://api.openai.com/v1/responses'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final outputs = responseData['output'] as List;

        // Find image generation call in outputs
        for (final output in outputs) {
          if (output['type'] == 'image_generation_call' &&
              output['status'] == 'completed') {
            final imageBase64 = output['result'] as String;

            if (kDebugMode) {
              print('Generated image with GPT-4.1 successfully');
              if (output['revised_prompt'] != null) {
                print('Revised prompt: ${output['revised_prompt']}');
              }
            }

            return imageBase64; // Return base64 encoded image
          }
        }
      } else {
        if (kDebugMode) {
          print('Image generation failed with status: ${response.statusCode}');
          print('Response body: ${response.body}');
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Image generation error: $e');
      }
      return null;
    }
  }

  // Convert speech to text using Whisper API
  Future<String?> speechToText(File audioFile) async {
    try {
      if (kDebugMode) {
        print('Converting speech to text with Whisper API');
      }

      final audioTranscription =
          await OpenAI.instance.audio.createTranscription(
        file: audioFile,
        model: "whisper-1",
        responseFormat: OpenAIAudioResponseFormat.json,
      );

      final transcribedText = audioTranscription.text;

      if (kDebugMode) {
        print('Speech to text result: $transcribedText');
      }

      return transcribedText;
    } catch (e) {
      if (kDebugMode) {
        print('Speech to text error: $e');
      }
      return null;
    }
  }

  // Analyze image using OpenAI Vision API with direct HTTP call
  Future<String?> analyzeImage(File imageFile, String? userPrompt) async {
    try {
      if (kDebugMode) {
        print('Analyzing image with OpenAI Vision API');
      }

      // Read image file as bytes
      final imageBytes = await imageFile.readAsBytes();

      // Convert to base64
      final base64Image = base64Encode(imageBytes);

      // Prepare the text content
      String textContent = userPrompt != null && userPrompt.trim().isNotEmpty
          ? "User's question about this image: ${userPrompt.trim()}"
          : "Please analyze this image and describe what you see in detail.";

      // Create the request body with proper format
      final requestBody = {
        "model": "gpt-4o",
        "messages": [
          {
            "role": "system",
            "content":
                "You are a helpful AI assistant that can analyze images. Describe what you see in the image in detail. Be specific and accurate about objects, people, text, colors, and any other relevant details you observe."
          },
          {
            "role": "user",
            "content": [
              {"type": "text", "text": textContent},
              {
                "type": "image_url",
                "image_url": {"url": "data:image/jpeg;base64,$base64Image"}
              }
            ]
          }
        ],
        "max_tokens": 300,
        "temperature": 0.7
      };

      // Make the HTTP request
      final response = await http.post(
        Uri.parse('https://api.openai.com/v1/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final content = responseData['choices'][0]['message']['content'];

        if (kDebugMode) {
          print('Image analysis successful: $content');
        }

        return content;
      } else {
        if (kDebugMode) {
          print('Image analysis failed with status: ${response.statusCode}');
          print('Response body: ${response.body}');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Image analysis error: $e');
      }
      return null;
    }
  }

  // Check if the message requires web search
  bool _shouldPerformWebSearch(String message) {
    final lowerMessage = message.toLowerCase();

    if (kDebugMode) {
      print('Checking web search for message: "$message"');
      print('Lowercase message: "$lowerMessage"');
    }

    // Keywords that indicate web search request
    final webSearchKeywords = [
      'website',
      'websites',
      'link',
      'links',
      'url',
      'find me',
      'search for',
      'look up',
      'where can i',
      'where to',
      'where is',
      'where are',
      'location of',
      'address of',
      'directions to',
      'how to get to',
      'buy this',
      'purchase',
      'shop',
      'shopping',
      'online store',
      'website for',
      'site for',
      'find website',
      'find sites',
      'web search',
      'google',
      'search',
      'browse',
      'internet',
      'online',
      'web',
    ];

    // Check if any keyword is present
    for (final keyword in webSearchKeywords) {
      if (lowerMessage.contains(keyword)) {
        if (kDebugMode) {
          print('Found web search keyword: "$keyword"');
        }
        return true;
      }
    }

    // Check for question patterns that typically need web search
    final searchPatterns = [
      'where can i buy',
      'where to buy',
      'how to buy',
      'where is',
      'where are',
      'location of',
      'address of',
      'directions to',
      'how to get to',
      'find me a',
      'show me',
      'give me links',
      'provide links',
      'recommend websites',
      'suggest sites',
    ];

    for (final pattern in searchPatterns) {
      if (lowerMessage.contains(pattern)) {
        if (kDebugMode) {
          print('Found web search pattern: "$pattern"');
        }
        return true;
      }
    }

    if (kDebugMode) {
      print('No web search triggers found');
    }
    return false;
  }

  // Extract search query from the message
  String _extractSearchQuery(String message) {
    final lowerMessage = message.toLowerCase();

    // Remove common prefixes to get the actual search query
    final prefixesToRemove = [
      'can you provide website links from where i can buy',
      'where can i buy',
      'where to buy',
      'where is',
      'where are',
      'location of',
      'address of',
      'directions to',
      'how to get to',
      'find me',
      'search for',
      'look up',
      'find website for',
      'find sites for',
      'website for',
      'links for',
      'show me',
      'give me links for',
      'provide links for',
      'recommend websites for',
      'suggest sites for',
    ];

    String query = message;

    for (final prefix in prefixesToRemove) {
      if (lowerMessage.startsWith(prefix)) {
        query = message.substring(prefix.length).trim();
        break;
      }
    }

    // If no prefix matched, use the whole message as query
    if (query == message) {
      // Try to extract the main subject from the message
      query = message
          .replaceAll(
              RegExp(
                  r'\b(website|websites|link|links|url|find|search|where|can|i|buy|this|that|the|a|an)\b',
                  caseSensitive: false),
              '')
          .trim();
    }

    // Clean up the query
    query = query.replaceAll(RegExp(r'\s+'), ' ').trim();

    // If query is empty or too short, use the original message
    if (query.isEmpty || query.length < 3) {
      query = message;
    }

    return query;
  }

  // Perform web search using a search API
  Future<String> performWebSearch(String query) async {
    try {
      if (kDebugMode) {
        print('Performing web search for: $query');
      }

      // Use Google Custom Search API or similar service
      // For now, I'll use a mock implementation that returns formatted results
      // You can replace this with your preferred search API
      final searchResults = await _performActualWebSearch(query);

      return searchResults;
    } catch (e) {
      if (kDebugMode) {
        print('Web search error: $e');
      }
      return "Sorry, I couldn't perform the web search at the moment. Please try again later.";
    }
  }

  // Actual web search implementation using HTTP requests
  Future<String> _performActualWebSearch(String query) async {
    try {
      // Use Google Custom Search API
      const apiKey =
          "AIzaSyBqJF8QqQqQqQqQqQqQqQqQqQqQqQqQqQq"; // Replace with your Google API key
      const searchEngineId =
          "your_search_engine_id"; // Replace with your Custom Search Engine ID

      final encodedQuery = Uri.encodeComponent(query);
      final url =
          "https://www.googleapis.com/customsearch/v1?key=$apiKey&cx=$searchEngineId&q=$encodedQuery&num=5";

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final items = data['items'] as List?;

        if (items == null || items.isEmpty) {
          return "No search results found for '$query'.";
        }

        String formattedResults =
            "🔍 **Web Search Results for \"$query\":**\n\n";

        for (int i = 0; i < items.length && i < 5; i++) {
          final item = items[i];
          final title = item['title'] ?? 'No title';
          final link = item['link'] ?? '';
          final snippet = item['snippet'] ?? 'No description available';

          formattedResults += "**${i + 1}. $title**\n";
          formattedResults += "   🔗 $link\n";
          formattedResults += "   📝 $snippet\n\n";
        }

        return formattedResults;
      } else {
        // Fallback to DuckDuckGo instant answer API
        return await _performDuckDuckGoSearch(query);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Google search failed: $e');
      }
      // Fallback to DuckDuckGo
      return await _performDuckDuckGoSearch(query);
    }
  }

  // Fallback search using DuckDuckGo Instant Answer API
  Future<String> _performDuckDuckGoSearch(String query) async {
    try {
      final encodedQuery = Uri.encodeComponent(query);
      final url =
          "https://api.duckduckgo.com/?q=$encodedQuery&format=json&no_html=1&skip_disambig=1";

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        String result = "🔍 **Search Results for \"$query\":**\n\n";

        // Check for instant answer
        final abstract = data['Abstract'] as String?;
        final abstractUrl = data['AbstractURL'] as String?;

        if (abstract != null && abstract.isNotEmpty) {
          result += "**Summary:**\n";
          result += "📝 $abstract\n";
          if (abstractUrl != null && abstractUrl.isNotEmpty) {
            result += "🔗 $abstractUrl\n\n";
          }
        }

        // Add related topics
        final relatedTopics = data['RelatedTopics'] as List?;
        if (relatedTopics != null && relatedTopics.isNotEmpty) {
          result += "**Related Links:**\n";
          for (int i = 0; i < relatedTopics.length && i < 3; i++) {
            final topic = relatedTopics[i];
            final text = topic['Text'] as String?;
            final firstUrl = topic['FirstURL'] as String?;

            if (text != null && firstUrl != null) {
              result += "• $text\n";
              result += "  🔗 $firstUrl\n\n";
            }
          }
        }

        if (result.length <= 100) {
          // If no good results, provide a generic shopping search suggestion
          result = "🔍 **Search Suggestions for \"$query\":**\n\n";
          result += "**1. Amazon**\n";
          result += "   🔗 https://www.amazon.com/s?k=$encodedQuery\n";
          result += "   📝 Search for products on Amazon\n\n";
          result += "**2. Google Shopping**\n";
          result +=
              "   🔗 https://www.google.com/search?tbm=shop&q=$encodedQuery\n";
          result += "   📝 Compare prices across multiple retailers\n\n";
          result += "**3. eBay**\n";
          result +=
              "   🔗 https://www.ebay.com/sch/i.html?_nkw=$encodedQuery\n";
          result += "   📝 Find new and used items on eBay\n\n";
        }

        return result;
      }
    } catch (e) {
      if (kDebugMode) {
        print('DuckDuckGo search failed: $e');
      }
    }

    // Final fallback - return shopping links
    final encodedQuery = Uri.encodeComponent(query);
    return """🔍 **Shopping Links for "$query":**

**1. Amazon**
   🔗 https://www.amazon.com/s?k=$encodedQuery
   📝 Search for products on Amazon

**2. Google Shopping**
   🔗 https://www.google.com/search?tbm=shop&q=$encodedQuery
   📝 Compare prices across multiple retailers

**3. eBay**
   🔗 https://www.ebay.com/sch/i.html?_nkw=$encodedQuery
   📝 Find new and used items on eBay""";
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return 'Unknown';
    }
  }
}
