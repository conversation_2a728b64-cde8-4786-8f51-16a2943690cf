## 0.3.0-dev.0

- Move to `quick.flutter` mono repo
- Refactor Linux implementation to `desktop_notifications`
- Fetch nuget when unavailable on Windows
- Refactor notify to asynchronous
- Fix permission when authorizationStatus is notDetermined

## 0.2.1

- Add `title` to `notify` method

## 0.2.0

- Support Web
- Add `hasPermission` & `requestPermission`

## 0.1.0+2

- Fix nullable typo

## 0.1.0+1

- Update constraint to Dart-2.12.0+ and Flutter-2.0.0+

## 0.1.0

- Add `notify(content:)`