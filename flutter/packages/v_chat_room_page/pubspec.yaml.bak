name: v_chat_room_page
description: v chat rooms page ui package this package is part of v chat sdk and cant be used alone.
version: 1.1.0
homepage: https://v-chat-sdk.github.io/vchat-v2-docs/docs/intro
issue_tracker: https://github.com/hatemragab/v_chat_sdk/issues
repository: https://github.com/hatemragab/v_chat_sdk/tree/master/v_ui_packages/v_chat_web_rtc
publish_to: none
environment:
  sdk: '>=2.17.0 <4.0.0'
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  v_chat_sdk_core:
    path: ../v_chat_sdk_core
  s_translation:
    path: ../s_translation
  super_up_core:
    path: ../super_up_core
  cached_network_image: ^3.4.1
  flutter_advanced_avatar: ^1.5.0
  map_launcher: ^3.5.0
  flutter_cache_manager: ^3.4.1
  loadmore: ^2.1.0
  meta: ^1.15.0
  badges: ^3.1.2
  v_platform: ^2.1.4
  timeago: ^3.7.0
  textless:
    git:
      url: https://github.com/hatemragab/textless.git
  collection: ^1.18.0
  intl: ^0.19.0
  adaptive_dialog: ^2.2.1+2

dev_dependencies:
  flutter_lints: ^5.0.0
  lints: ^5.0.0
flutter:

platforms:
  android:
  ios:
  web:
  windows:
  macos:
