// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'local_messages.dart';

final fakeApiRooms = [
  {
    "_id": "rid1",
    "uId": "638a78804eada01ff9a7033f",
    "rId": "rid1",
    "lSMId": "63a9d86c5b1dd26b917b37bd",
    "rT": "s",
    "t": "user2",
    "tEn": "user2",
    "nTitle": null,
    "img": "default_user_chat_image.png",
    "isD": false,
    "isA": false,
    "isM": false,
    "pId": "638a78884eada01ff9a70348",
    "pIdentifier": "<EMAIL>",
    "orderId": null,
    "bId": null,
    "createdAt": "2022-12-26T17:22:52.063Z",
    "updatedAt": "2022-12-26T17:23:29.304Z",
    "messages": [fakeLocalMessages[1]],
    "uC": 0
  }
];
