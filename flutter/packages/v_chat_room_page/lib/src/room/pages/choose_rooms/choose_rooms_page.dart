// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:s_translation/generated/l10n.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

import '../../../../v_chat_room_page.dart';
import 'choose_room_controller.dart';

/// A stateful widget that displays a list of available rooms and allows the user to select one. /// /// The [currentRoomId] parameter is used to highlight the
class VChooseRoomsPage extends StatefulWidget {
  final String? currentRoomId;

  const VChooseRoomsPage({
    super.key,
    required this.currentRoomId,
  });

  @override
  State<VChooseRoomsPage> createState() => _VChooseRoomsPageState();
}

class _VChooseRoomsPageState extends State<VChooseRoomsPage> {
  late final ChooseRoomsController controller;

  @override
  void initState() {
    super.initState();
    controller = ChooseRoomsController(widget.currentRoomId);
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<List<VRoom>>(
      valueListenable: controller,
      builder: (_, value, __) {
        return CupertinoPageScaffold(
          navigationBar: CupertinoNavigationBar(
            middle: Text(S.of(context).chooseRoom),
            previousPageTitle: S.of(context).back,
            trailing: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: !controller.isThereSelection
                  ? () => controller.onDone(context)
                  : null,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(S.of(context).send),
                  const SizedBox(
                    width: 5,
                  ),
                  Text(
                    "(${controller.selectedCount}/${controller.maxForward})",
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(10),
              child: _buildContent(context, value),
            ),
          ),
        );
      },
    );
  }

  Widget _buildContent(BuildContext context, List<VRoom> rooms) {
    switch (controller.state) {
      case ChooseRoomsState.loading:
        return const Center(
          child: CupertinoActivityIndicator(
            radius: 20,
          ),
        );
      case ChooseRoomsState.error:
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                CupertinoIcons.exclamationmark_triangle,
                size: 48,
                color: CupertinoColors.systemRed,
              ),
              const SizedBox(height: 16),
              const Text(
                'Something went wrong',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                controller.errorMessage ?? 'Please try again',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 14,
                  color: CupertinoColors.systemGrey,
                ),
              ),
              const SizedBox(height: 24),
              CupertinoButton.filled(
                onPressed: () => controller.retryLoadRooms(),
                child: const Text('Try Again'),
              ),
            ],
          ),
        );
      case ChooseRoomsState.loaded:
        if (rooms.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  CupertinoIcons.chat_bubble_2,
                  size: 48,
                  color: CupertinoColors.systemGrey,
                ),
                const SizedBox(height: 16),
                const Text(
                  'No chats found',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Start a conversation to see it here',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: CupertinoColors.systemGrey,
                  ),
                ),
              ],
            ),
          );
        }
        return ListView.builder(
          cacheExtent: 300,
          itemBuilder: (context, index) {
            return VRoomItem(
              room: rooms[index],
              isIconOnly: false,
              onRoomItemLongPress: (room) =>
                  controller.onRoomItemPress(room, context),
              onRoomItemPress: (room) =>
                  controller.onRoomItemPress(room, context),
            );
          },
          itemCount: rooms.length,
        );
    }
  }

  @override
  void dispose() {
    super.dispose();
    controller.close();
  }
}
