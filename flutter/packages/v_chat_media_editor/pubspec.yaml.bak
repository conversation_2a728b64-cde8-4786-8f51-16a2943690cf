name: v_chat_media_editor
description: v chat media editor package from v chat sdk this package is used to edit images and videos
version: 1.2.2
homepage: https://v-chat-sdk.github.io/vchat-v2-docs/docs/intro
issue_tracker: https://github.com/v-chat-sdk/v_chat_media_editor/issues
repository: https://github.com/v-chat-sdk/v_chat_media_editor
publish_to: none

environment:
  sdk: '>=2.17.0 <4.0.0'
  flutter: ">=1.17.0"


dependencies:
  flutter:
    sdk: flutter
  flutter_painter_v2: ^2.0.1
  path: ^1.9.0
  path_provider: ^2.1.5
  meta: ^1.15.0
  v_platform: ^2.1.4
  fc_native_video_thumbnail: ^0.16.1
  flutter_cache_manager: ^3.4.1
  flutter_image_compress: ^2.3.0
  image_cropper: ^8.0.2
  phosphor_flutter: ^2.1.0
  file_picker: ^8.1.3
  chewie: ^1.8.5
  video_player: ^2.9.2
  cached_network_image: ^3.4.1
  blurhash:
    git:
      url: https://github.com/himanshu64/blurhash.git
  pro_image_editor: ^6.1.1


dependency_overrides:


dev_dependencies:
  flutter_lints: ^5.0.0
  lints: ^5.0.0

flutter:

platforms:
  android:
  ios:
  web:
  windows:
  macos:
