// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:super_up_core/src/models/model.dart';
//
// class SWaitingPage extends StatelessWidget {
//   final SMyProfile profile;
//
//   const SWaitingPage({Key? key, required this.profile}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return CupertinoPageScaffold(
//       navigationBar: const CupertinoNavigationBar(
//         middle: Text('Forget password'),
//       ),
//       child: SafeArea(
//         child: SingleChildScrollView(
//           child: Column(
//             children: [
//               Text(profile.toMap().toString()),
//               TextButton(onPressed: () {}, child: const Text("Retry"))
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
