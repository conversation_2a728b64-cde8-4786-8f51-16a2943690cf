// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

// // Made for FlexColorScheme version 7.0.0-dev.2 (beta). Make sure
// // you use same or higher version, but still same major version. If
// // you use a lower version, some properties may not be supported. In
// // that case you can also remove them after copying the theme to your app.
// import 'package:flex_color_scheme/flex_color_scheme.dart';
// import 'package:flutter/material.dart';
//
// final sLightTheme = FlexThemeData.light(
//   scheme: FlexScheme.yellowM3,
//   surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
//   blendLevel: 9,
//   extensions: [
//     // VMessageTheme.dark().copyWith(
//     //   scaffoldDecoration: sMessageBackground(isDark: false),
//     // )
//   ],
//   appBarBackground: Colors.yellow.shade400,
//   tabBarStyle: null,
//   subThemesData: const FlexSubThemesData(
//     useM2StyleDividerInM3: true,
//     elevatedButtonRadius: 31.0,
//     outlinedButtonRadius: 39.0,
//     unselectedToggleIsColored: true,
//     sliderTrackHeight: 4,
//     inputDecoratorFillColor: Colors.white,
//     inputDecoratorBorderWidth: 0,
//     cardRadius: 4.0,
//     dialogBackgroundSchemeColor: SchemeColor.secondaryContainer,
//     dialogRadius: 18.0,
//     timePickerDialogRadius: 18.0,
//     dialogElevation: 20.0,
//     appBarBackgroundSchemeColor: SchemeColor.primaryContainer,
//     bottomSheetRadius: 21.0,
//     bottomSheetElevation: 10.0,
//     bottomSheetModalElevation: 3.0,
//     navigationBarIndicatorOpacity: 0.44,
//   ),
//   keyColors: const FlexKeyColors(
//     useSecondary: true,
//     useTertiary: true,
//   ),
//   visualDensity: FlexColorScheme.comfortablePlatformDensity,
//   useMaterial3: true,
//   swapLegacyOnMaterial3: true,
//   // To use the playground font, add GoogleFonts package and uncomment
//   // fontFamily: GoogleFonts.notoSans().fontFamily,
// );
// final sDarkTheme = FlexThemeData.dark(
//   scheme: FlexScheme.yellowM3,
//   surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
//   blendLevel: 15,
//   tabBarStyle: null,
//   subThemesData: const FlexSubThemesData(
//     useM2StyleDividerInM3: true,
//     elevatedButtonRadius: 31.0,
//     outlinedButtonRadius: 39.0,
//     unselectedToggleIsColored: true,
//     sliderTrackHeight: 4,
//     inputDecoratorBorderWidth: 0.5,
//     cardRadius: 4.0,
//     dialogBackgroundSchemeColor: SchemeColor.secondaryContainer,
//     dialogRadius: 18.0,
//     timePickerDialogRadius: 18.0,
//     dialogElevation: 20.0,
//     bottomSheetRadius: 21.0,
//     bottomSheetElevation: 10.0,
//     bottomSheetModalElevation: 3.0,
//     navigationBarIndicatorOpacity: 0.44,
//   ),
//   keyColors: const FlexKeyColors(
//     useSecondary: true,
//     useTertiary: true,
//   ),
//   visualDensity: FlexColorScheme.comfortablePlatformDensity,
//   useMaterial3: true,
//   swapLegacyOnMaterial3: true,
// // To use the Playground font, add GoogleFonts package and uncomment
// // fontFamily: GoogleFonts.notoSans().fontFamily,
// );
// // If you do not have a themeMode switch, uncomment this line
// // to let the device system mode control the theme mode:
// // themeMode: ThemeMode.system,
