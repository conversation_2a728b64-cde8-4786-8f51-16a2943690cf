name: super_up_core
description: A new Flutter package project.
version: 0.0.1
publish_to: none
homepage:

environment:
  sdk: '>=3.0.2 <4.0.0'
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  s_translation:
    path: ../../packages/s_translation
  v_chat_sdk_core:
    path: ../v_chat_sdk_core
  path: ^1.9.0
  http: ^1.2.2
  chopper: ^8.0.3
  http_parser: ^4.0.2
  touchable_opacity: ^1.2.0
  version: ^3.0.2
  from_css_color: ^2.0.0
  intl: ^0.19.0
  photo_view: ^0.15.0
  percent_indicator: ^4.2.3
  timeago: ^3.7.0
  v_platform: ^2.1.4
  cached_network_image: ^3.4.1
  flutter_smart_dialog: ^4.9.8+3
  video_player: ^2.9.2
  chewie: ^1.8.5
  flutter_cache_manager: ^3.4.1
  flutter_advanced_avatar: ^1.5.0
  phosphor_flutter: ^2.1.0
  url_launcher: ^6.3.1
  map_launcher: ^3.5.0
  file_picker: ^8.1.3
  image_cropper: ^8.0.2
  wechat_camera_picker: ^4.3.6
  textless:
    git:
      url: https://github.com/hatemragab/textless.git
  collection: ^1.18.0
  device_info_plus: ^11.1.0
  uuid: ^4.5.1
  adaptive_dialog: ^2.2.1+2
  file_saver: ^0.2.14
  path_provider: ^2.1.5
  oktoast: ^3.4.0
  shared_preferences: ^2.3.3
  encrypt: 5.0.1
  image_picker: ^1.1.2
  mime: ^2.0.0
  enum_to_string: ^2.0.1
  universal_html: ^2.2.4
  google_mobile_ads: ^5.2.0
  gal: ^2.3.0
  badges: ^3.1.2
dev_dependencies:
  lint: ^2.3.0
  build_runner: ^2.4.13
  chopper_generator: ^8.0.3
  lints: ^5.0.0
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
