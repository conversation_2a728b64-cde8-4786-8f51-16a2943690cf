// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Turkish (`tr`).
class AppLocalizationsTr extends AppLocalizations {
  AppLocalizationsTr([String locale = 'tr']) : super(locale);

  @override
  String get done => 'Hecho';

  @override
  String get loading => 'Cargando...';

  @override
  String get messageHasBeenDeleted => 'Mensaje eliminado';

  @override
  String get mute => 'Silenciar';

  @override
  String get cancel => 'Cancelar';

  @override
  String get typing => 'Escribiendo...';

  @override
  String get ok => 'Aceptar';

  @override
  String get recording => 'Grabando...';

  @override
  String get connecting => 'Conectando...';

  @override
  String get deleteYouCopy => 'Eliminar tu copia';

  @override
  String get unMute => 'Activar sonido';

  @override
  String get delete => 'Eliminar';

  @override
  String get report => 'Reportar';

  @override
  String get leaveGroup => 'Salir del grupo';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      '¿Estás seguro de permitir tu copia? Esta acción no se puede deshacer';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      '¿Estás seguro de abandonar este grupo? Esta acción no se puede deshacer';

  @override
  String get leaveGroupAndDeleteYourMessageCopy =>
      'Salir del grupo y eliminar tu copia del mensaje';

  @override
  String get vMessageInfoTrans => 'Información del mensaje';

  @override
  String get updateTitleTo => 'Actualizar título a';

  @override
  String get updateImage => 'Actualizar imagen';

  @override
  String get joinedBy => 'Unido por';

  @override
  String get promotedToAdminBy => 'Promovido a administrador por';

  @override
  String get dismissedToMemberBy => 'Relegado a miembro por';

  @override
  String get leftTheGroup => 'Abandonó el grupo';

  @override
  String get you => 'Tú';

  @override
  String get kickedBy => 'Expulsado por';

  @override
  String get groupCreatedBy => 'Grupo creado por';

  @override
  String get groupDeletedBy => 'Group deleted by';

  @override
  String get addedYouToNewBroadcast => 'Te agregó a una nueva difusión';

  @override
  String get download => 'Descargar';

  @override
  String get copy => 'Copiar';

  @override
  String get info => 'Información';

  @override
  String get share => 'Compartir';

  @override
  String get forward => 'Reenviar';

  @override
  String get reply => 'Responder';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => 'Eliminar de todos';

  @override
  String get deleteFromMe => 'Eliminar de mí';

  @override
  String get downloading => 'Descargando...';

  @override
  String get fileHasBeenSavedTo => 'El archivo se ha guardado en';

  @override
  String get online => 'En línea';

  @override
  String get youDontHaveAccess => 'No tienes acceso';

  @override
  String get replyToYourSelf => 'Responder a ti mismo';

  @override
  String get repliedToYourSelf => 'Respondiste a ti mismo';

  @override
  String get audioCall => 'Llamada de audio';

  @override
  String get ring => 'Timbre';

  @override
  String get canceled => 'Cancelado';

  @override
  String get timeout => 'Tiempo agotado';

  @override
  String get rejected => 'Rechazada';

  @override
  String get finished => 'Finalizada';

  @override
  String get inCall => 'En llamada';

  @override
  String get sessionEnd => 'Fin de la sesión';

  @override
  String get yesterday => 'Ayer';

  @override
  String get today => 'Hoy';

  @override
  String get textFieldHint => 'Escribe un mensaje...';

  @override
  String get files => 'Archivos';

  @override
  String get location => 'Ubicación';

  @override
  String get shareMediaAndLocation => 'Compartir multimedia y ubicación';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize =>
      'Hay un video cuyo tamaño es mayor al permitido';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize =>
      'Hay un archivo cuyo tamaño es mayor al permitido';

  @override
  String get makeCall => 'Realizar llamada';

  @override
  String get areYouWantToMakeVideoCall => '¿Deseas hacer una videollamada?';

  @override
  String get areYouWantToMakeVoiceCall => '¿Deseas hacer una llamada de voz?';

  @override
  String get vMessagesInfoTrans => 'Información de mensajes';

  @override
  String get star => 'Destacar';

  @override
  String get minutes => 'Minutos';

  @override
  String get sendMessage => 'Enviar mensaje';

  @override
  String get deleteUser => 'Eliminar usuario';

  @override
  String get actions => 'Acciones';

  @override
  String get youAreAboutToDeleteThisUserFromYourList =>
      'Estás a punto de eliminar a este usuario de tu lista';

  @override
  String get updateBroadcastTitle => 'Actualizar título de la difusión';

  @override
  String get usersAddedSuccessfully => 'Usuarios agregados exitosamente';

  @override
  String get broadcastSettings => 'Configuración de la difusión';

  @override
  String get addParticipants => 'Agregar participantes';

  @override
  String get broadcastParticipants => 'Participantes de la difusión';

  @override
  String get updateGroupDescription => 'Actualizar descripción del grupo';

  @override
  String get updateGroupTitle => 'Actualizar título del grupo';

  @override
  String get groupSettings => 'Configuración del grupo';

  @override
  String get description => 'Descripción';

  @override
  String get muteNotifications => 'Silenciar notificaciones';

  @override
  String get groupParticipants => 'Participantes del grupo';

  @override
  String get blockUser => 'Bloquear usuario';

  @override
  String get areYouSureToBlock => '¿Estás seguro de bloquear a';

  @override
  String get userPage => 'Página del usuario';

  @override
  String get starMessage => 'Destacar mensaje';

  @override
  String get showMedia => 'Mostrar medios';

  @override
  String get reportUser => 'Reportar usuario';

  @override
  String get groupName => 'Nombre del grupo';

  @override
  String get changeSubject => 'Cambiar asunto';

  @override
  String get titleIsRequired => 'El título es obligatorio';

  @override
  String get createBroadcast => 'Crear difusión';

  @override
  String get broadcastName => 'Nombre de la difusión';

  @override
  String get createGroup => 'Crear grupo';

  @override
  String get forgetPassword => 'Olvidé la contraseña';

  @override
  String get globalSearch => 'Búsqueda global';

  @override
  String get dismissesToMember => 'Relegar a miembro';

  @override
  String get setToAdmin => 'Establecer como administrador';

  @override
  String get kickMember => 'Expulsar miembro';

  @override
  String get youAreAboutToDismissesToMember =>
      'Estás a punto de relegar a miembro';

  @override
  String get youAreAboutToKick => 'Estás a punto de expulsar';

  @override
  String get groupMembers => 'Miembros del grupo';

  @override
  String get tapForPhoto => 'Toca para la foto';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      'We high recommend to download this update';

  @override
  String get newGroup => 'Nuevo grupo';

  @override
  String get newBroadcast => 'Nueva difusión';

  @override
  String get starredMessage => 'Mensaje destacado';

  @override
  String get settings => 'Ajustes';

  @override
  String get chats => 'CHATS';

  @override
  String get recentUpdates => 'Actualizaciones recientes';

  @override
  String get startChat => 'Iniciar chat';

  @override
  String get newUpdateIsAvailable => 'Nueva actualización disponible';

  @override
  String get emailNotValid => 'Correo electrónico no válido';

  @override
  String get passwordMustHaveValue => 'La contraseña debe tener un valor';

  @override
  String get error => 'Error';

  @override
  String get password => 'Contraseña';

  @override
  String get login => 'Iniciar sesión';

  @override
  String get needNewAccount => '¿Necesitas una cuenta nueva?';

  @override
  String get register => 'Registrarse';

  @override
  String get nameMustHaveValue => 'El nombre debe tener un valor';

  @override
  String get passwordNotMatch => 'Las contraseñas no coinciden';

  @override
  String get name => 'Nombre';

  @override
  String get email => 'Correo electrónico';

  @override
  String get confirmPassword => 'Confirmar contraseña';

  @override
  String get alreadyHaveAnAccount => '¿Ya tienes una cuenta?';

  @override
  String get logOut => 'Cerrar sesión';

  @override
  String get back => 'Atrás';

  @override
  String get sendCodeToMyEmail => 'Enviar código a mi correo electrónico';

  @override
  String get invalidLoginData => 'Datos de inicio de sesión no válidos';

  @override
  String get userEmailNotFound => 'Correo electrónico de usuario no encontrado';

  @override
  String get yourAccountBlocked => 'Tu cuenta ha sido bloqueada';

  @override
  String get yourAccountDeleted => 'Tu cuenta ha sido eliminada';

  @override
  String get userAlreadyRegister => 'Usuario ya registrado';

  @override
  String get codeHasBeenExpired => 'El código ha caducado';

  @override
  String get invalidCode => 'Código no válido';

  @override
  String get whileAuthCanFindYou => 'While authentication cannot find you';

  @override
  String get userRegisterStatusNotAcceptedYet =>
      'El estado de registro del usuario aún no ha sido aceptado';

  @override
  String get deviceHasBeenLogoutFromAllDevices =>
      'El dispositivo ha cerrado la sesión en todos los dispositivos';

  @override
  String get userDeviceSessionEndDeviceDeleted =>
      'La sesión del dispositivo del usuario ha finalizado y el dispositivo se ha eliminado';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      'No code has been send to you to verify your email';

  @override
  String get roomAlreadyInCall => 'Sala en llamada';

  @override
  String get peerUserInCallNow => 'Usuario en llamada ahora';

  @override
  String get callNotAllowed => 'Llamada no permitida';

  @override
  String get peerUserDeviceOffline =>
      'Dispositivo del usuario de destino fuera de línea';

  @override
  String get emailMustBeValid => 'El correo electrónico debe ser válido';

  @override
  String get wait2MinutesToSendMail => 'Espera 2 minutos para enviar el correo';

  @override
  String get codeMustEqualToSixNumbers => 'Code must equal to six numbers';

  @override
  String get newPasswordMustHaveValue =>
      'La nueva contraseña debe tener un valor';

  @override
  String get confirmPasswordMustHaveValue =>
      'La confirmación de la contraseña debe tener un valor';

  @override
  String get congregationsYourAccountHasBeenAccepted =>
      'Tebrikler, hesabınız kabul edildi';

  @override
  String get yourAccountIsUnderReview => 'Hesabınız inceleniyor';

  @override
  String get waitingList => 'Bekleme Listesi';

  @override
  String get welcome => 'Hoş geldiniz';

  @override
  String get retry => 'Tekrar Dene';

  @override
  String get deleteMember => 'Üyeyi Sil';

  @override
  String get profile => 'Profil';

  @override
  String get broadcastInfo => 'Yayın Bilgisi';

  @override
  String get updateTitle => 'Başlığı Güncelle';

  @override
  String get members => 'Üyeler';

  @override
  String get addMembers => 'Üye Ekle';

  @override
  String get success => 'Başarılı';

  @override
  String get media => 'Medya';

  @override
  String get docs => 'Belgeler';

  @override
  String get links => 'Bağlantılar';

  @override
  String get soon => 'Yakında';

  @override
  String get unStar => 'Yıldızı Kaldır';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      'Grup açıklamasını güncellemek tüm grup üyelerini güncelleyecektir';

  @override
  String get updateNickname => 'Takma Adı Güncelle';

  @override
  String get groupInfo => 'Grup Bilgisi';

  @override
  String get youNotParticipantInThisGroup => 'Bu grupta üye değilsiniz';

  @override
  String get search => 'Ara';

  @override
  String get mediaLinksAndDocs => 'Medya, Bağlantılar ve Belgeler';

  @override
  String get starredMessages => 'Yıldızlı Mesajlar';

  @override
  String get nickname => 'Takma Ad';

  @override
  String get none => 'Hiçbiri';

  @override
  String get yes => 'Evet';

  @override
  String get no => 'Hayır';

  @override
  String get exitGroup => 'Grubu Terk Et';

  @override
  String get clickToAddGroupDescription =>
      'Grup açıklaması eklemek için tıklayın';

  @override
  String get unBlockUser => 'Kullanıcının Engelini Kaldır';

  @override
  String get areYouSureToUnBlock =>
      'Engeli kaldırmak istediğinizden emin misiniz?';

  @override
  String get contactInfo => 'İletişim Bilgileri';

  @override
  String get audio => 'Ses';

  @override
  String get video => 'Video';

  @override
  String get hiIamUse => 'Merhaba, ben kullanıcıyım';

  @override
  String get on => 'Açık';

  @override
  String get off => 'Kapalı';

  @override
  String get unBlock => 'Engeli Kaldır';

  @override
  String get block => 'Engelle';

  @override
  String get chooseAtLestOneMember => 'En az bir üye seçin';

  @override
  String get close => 'Kapat';

  @override
  String get next => 'İleri';

  @override
  String get appMembers => 'Uygulama Üyeleri';

  @override
  String get create => 'Oluştur';

  @override
  String get upgradeToAdmin => 'Yöneticiye Yükselt';

  @override
  String get update => 'Güncelle';

  @override
  String get deleteChat => 'Sohbeti Sil';

  @override
  String get clearChat => 'Sohbeti Temizle';

  @override
  String get showHistory => 'Geçmişi Göster';

  @override
  String get groupIcon => 'Grup İkonu';

  @override
  String get tapToSelectAnIcon => 'İkon seçmek için dokunun';

  @override
  String get groupDescription => 'Grup Açıklaması';

  @override
  String get more => 'Daha Fazla';

  @override
  String get messageInfo => 'Mesaj Bilgisi';

  @override
  String get successfullyDownloadedIn => 'Başarıyla indirildi:';

  @override
  String get delivered => 'Teslim Edildi';

  @override
  String get read => 'Okundu';

  @override
  String get orLoginWith => 'veya ile giriş yap';

  @override
  String get resetPassword => 'Şifre Sıfırlama';

  @override
  String get otpCode => 'OTP Kodu';

  @override
  String get newPassword => 'Yeni Şifre';

  @override
  String get areYouSure => 'Emin misiniz?';

  @override
  String get broadcastMembers => 'Yayın Üyeleri';

  @override
  String get phone => 'Telefon';

  @override
  String get users => 'Kullanıcılar';

  @override
  String get calls => 'Aramalar';

  @override
  String get yourAreAboutToLogoutFromThisAccount =>
      'Bu hesaptan çıkıyorsunuz, emin misiniz?';

  @override
  String get noUpdatesAvailableNow => 'Şu anda güncelleme yok';

  @override
  String get dataPrivacy => 'Veri Gizliliği';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      'Tüm veriler yedeklendi, verileri kendiniz yönetmeye veya kaydetmeye gerek yok! Çıkış yaparsanız ve tekrar giriş yaparsanız, tüm sohbetleri göreceksiniz, web sürümüyle aynı';

  @override
  String get account => 'Hesap';

  @override
  String get linkedDevices => 'Bağlı Cihazlar';

  @override
  String get storageAndData => 'Depolama ve Veri';

  @override
  String get tellAFriend => 'Bir Arkadaşa Söyle';

  @override
  String get help => 'Yardım';

  @override
  String get blockedUsers => 'Engellenen Kullanıcılar';

  @override
  String get inAppAlerts => 'Uygulama İçi Bildirimler';

  @override
  String get language => 'Dil';

  @override
  String get adminNotification => 'Yönetici Bildirimi';

  @override
  String get checkForUpdates => 'Güncellemeleri Kontrol Et';

  @override
  String get linkByQrCode => 'QR Kodu ile Bağlantı';

  @override
  String get deviceStatus => 'Cihaz Durumu';

  @override
  String get desktopAndOtherDevices => 'Masaüstü ve Diğer Cihazlar';

  @override
  String get linkADeviceSoon => 'Cihazı Bağla (Yakında)';

  @override
  String get lastActiveFrom => 'Son Etkinlik Tarihi:';

  @override
  String get tapADeviceToEditOrLogOut =>
      'Düzenlemek veya oturumu kapatmak için bir cihaza dokunun';

  @override
  String get contactUs => 'Bize Ulaşın';

  @override
  String get supportChatSoon => 'Destek Sohbeti (Yakında)';

  @override
  String get updateYourName => 'Adınızı Güncelleyin';

  @override
  String get updateYourBio => 'Biyografinizi Güncelleyin';

  @override
  String get edit => 'Düzenle';

  @override
  String get about => 'Hakkında';

  @override
  String get oldPassword => 'Eski Şifre';

  @override
  String get deleteMyAccount => 'Hesabımı Sil';

  @override
  String get passwordHasBeenChanged => 'Şifre değiştirildi';

  @override
  String get logoutFromAllDevices => 'Tüm cihazlardan çıkış yapılsın mı?';

  @override
  String get updateYourPassword => 'Şifrenizi Güncelleyin';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      'Adınızı girin ve isteğe bağlı bir profil resmi ekleyin';

  @override
  String get privacyPolicy => 'Gizlilik Politikası';

  @override
  String get chat => 'Sohbet';

  @override
  String get send => 'Gönder';

  @override
  String get reportHasBeenSubmitted => 'Raporunuz gönderildi';

  @override
  String get offline => 'Çevrimdışı';

  @override
  String get harassmentOrBullyingDescription =>
      'Taciz veya Zorbalık: Bu seçenek, kullanıcıların kendilerine veya başkalarına taciz eden mesajlar, tehditler veya diğer zorbalık biçimleri gönderen kişileri bildirmelerine olanak tanır.';

  @override
  String get spamOrScamDescription =>
      'Spam veya Dolandırıcılık: Bu seçenek, kullanıcıların spam mesajları, istenmeyen reklamları gönderen hesapları veya diğerleri tarafından dolandırılmaya çalışılan hesapları bildirmeleri için kullanılabilir.';

  @override
  String get areYouSureToReportUserToAdmin =>
      'Bu kullanıcı hakkında yöneticiye rapor göndermek istediğinizden emin misiniz?';

  @override
  String get groupWith => 'Şununla Grup';

  @override
  String get inappropriateContentDescription =>
      'Uygunsuz İçerik: Kullanıcılar, cinsel içerik, nefret söylemi veya topluluk standartlarını ihlal eden diğer içerikleri bildirmek için bu seçeneği seçebilirler.';

  @override
  String get otherCategoryDescription =>
      'Diğer: Bu genel kategori, yukarıdaki kategorilere kolayca uymayan ihlaller için kullanılabilir. Kullanıcıların ek bilgiler sağlamak için metin kutusu eklemeleri faydalı olabilir.';

  @override
  String get explainWhatHappens => 'Burada ne olduğunu açıklayın';

  @override
  String get loginAgain => 'Yeniden giriş yap!';

  @override
  String get yourSessionIsEndedPleaseLoginAgain =>
      'Oturumunuz sona erdi, lütfen yeniden giriş yapın';

  @override
  String get aboutToBlockUserWithConsequences =>
      'Bu kullanıcıyı engellemek üzeresiniz. Bu kullanıcıya mesaj gönderemez ve gruplara veya yayınlara ekleyemezsiniz!';

  @override
  String get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
      'Hesabınızı silmek üzeresiniz. Hesabınız kullanıcı listesinde artık görünmeyecektir';

  @override
  String get admin => 'Yönetici';

  @override
  String get member => 'Üye';

  @override
  String get creator => 'Oluşturan';

  @override
  String get currentDevice => 'Mevcut Cihaz';

  @override
  String get visits => 'Ziyaretler';

  @override
  String get chooseRoom => 'Oda Seç';

  @override
  String get deleteThisDeviceDesc =>
      'Bu cihazı silmek, bu cihazdan hemen çıkış yapmak anlamına gelir';

  @override
  String get youAreAboutToUpgradeToAdmin =>
      'Estás a punto de ascender a administrador';

  @override
  String get microphonePermissionMustBeAccepted =>
      'Microphone permission must be accepted';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'Microphone and camera permission must be accepted';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      'Şu anda girişe izin verilmiyor. Lütfen daha sonra tekrar deneyin.';

  @override
  String get dashboard => 'Kontrol Paneli';

  @override
  String get notification => 'Bildirim';

  @override
  String get total => 'Toplam';

  @override
  String get blocked => 'Engellendi';

  @override
  String get deleted => 'Silindi';

  @override
  String get accepted => 'Kabul edildi';

  @override
  String get notAccepted => 'Kabul edilmedi';

  @override
  String get web => 'Web';

  @override
  String get android => 'Android';

  @override
  String get macOs => 'macOS';

  @override
  String get windows => 'Windows';

  @override
  String get other => 'Diğer';

  @override
  String get totalVisits => 'Toplam Ziyaretler';

  @override
  String get totalMessages => 'Toplam Mesajlar';

  @override
  String get textMessages => 'Metin Mesajları';

  @override
  String get imageMessages => 'Resim Mesajları';

  @override
  String get videoMessages => 'Video Mesajları';

  @override
  String get voiceMessages => 'Sesli Mesajlar';

  @override
  String get fileMessages => 'Dosya Mesajları';

  @override
  String get infoMessages => 'Bilgi Mesajları';

  @override
  String get voiceCallMessages => 'Sesli Çağrı Mesajları';

  @override
  String get videoCallMessages => 'Video Çağrı Mesajları';

  @override
  String get locationMessages => 'Konum Mesajları';

  @override
  String get directChat => 'Doğrudan Sohbet';

  @override
  String get group => 'Grup';

  @override
  String get broadcast => 'Yayın';

  @override
  String get messageCounter => 'Mesaj Sayacı';

  @override
  String get roomCounter => 'Oda Sayacı';

  @override
  String get countries => 'Ülkeler';

  @override
  String get devices => 'Cihazlar';

  @override
  String get notificationTitle => 'Bildirim Başlığı';

  @override
  String get notificationDescription => 'Bildirim Açıklaması';

  @override
  String get notificationsPage => 'Bildirimler Sayfası';

  @override
  String get updateFeedBackEmail => 'Geribildirim E-postasını Güncelle';

  @override
  String get setMaxMessageForwardAndShare =>
      'Maksimum Mesaj İleriye ve Paylaşıma Ayarla';

  @override
  String get setNewPrivacyPolicyUrl =>
      'Yeni Gizlilik Politikası URL\'sini Belirle';

  @override
  String get forgetPasswordExpireTime => 'Parola Sıfırlama Süresi';

  @override
  String get chatsAndCallsAreEndToEndEncrypted =>
      'Chats and calls are end-to-end encrypted. Only people in this chat can read, listen to, or share them.';

  @override
  String get clickToLearnMore => 'Click to learn more.';

  @override
  String get callTimeoutInSeconds => 'Arama Süresi Sınırlaması (saniye)';

  @override
  String get setMaxGroupMembers => 'Maksimum Grup Üye Sayısını Belirle';

  @override
  String get setMaxBroadcastMembers => 'Maksimum Yayın Üye Sayısını Belirle';

  @override
  String get allowCalls => 'Aramalara İzin Ver';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      'Bu seçenek etkinleştirildiğinde video ve sesli aramalara izin verilir';

  @override
  String get allowAds => 'Reklamlara İzin Ver';

  @override
  String get allowMobileLogin => 'Mobil Girişi İzin Ver';

  @override
  String get allowWebLogin => 'Web Girişi İzin Ver';

  @override
  String get messages => 'Mesajlar';

  @override
  String get appleStoreAppUrl => 'Apple App Store URL\'si';

  @override
  String get googlePlayAppUrl => 'Google Play Store URL\'si';

  @override
  String get privacyUrl => 'Gizlilik Politikası URL\'si';

  @override
  String get feedBackEmail => 'Geribildirim E-postası';

  @override
  String get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
      'Bu seçenek devre dışı bırakıldığında sohbet dosyalarının, resimlerin, videoların ve konum bilgilerinin gönderimi engellenecektir';

  @override
  String get allowSendMedia => 'Medya Gönderimine İzin Ver';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      'Bu seçenek devre dışı bırakıldığında sohbet yayını oluşturma engellenecektir';

  @override
  String get allowCreateBroadcast => 'Yayın Oluşturmaya İzin Ver';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      'Bu seçenek devre dışı bırakıldığında sohbet grupları oluşturma engellenecektir';

  @override
  String get allowCreateGroups => 'Grup Oluşturmaya İzin Ver';

  @override
  String get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
      'Bu seçenek devre dışı bırakıldığında masaüstü (Windows ve macOS) giriş veya kayıt engellenecektir';

  @override
  String get allowDesktopLogin => 'Masaüstü Girişine İzin Ver';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      'Bu seçenek devre dışı bırakıldığında web girişi veya kayıt engellenecektir';

  @override
  String get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
      'Bu seçenek etkinleştirildiğinde Google Ads bannerları sohbetlerde görüntülenir';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'If this option is enabled, the Google Ads banner will appear in chats.';

  @override
  String get userProfile => 'Kullanıcı Profili';

  @override
  String get userInfo => 'Kullanıcı Bilgisi';

  @override
  String get fullName => 'Tam Ad';

  @override
  String get bio => 'Hakkımda';

  @override
  String get noBio => 'Hakkımda Yok';

  @override
  String get verifiedAt => 'Doğrulandı';

  @override
  String get country => 'Ülke';

  @override
  String get registerStatus => 'Kayıt Durumu';

  @override
  String get registerMethod => 'Kayıt Yöntemi';

  @override
  String get banTo => 'Şu tarihe kadar engelle';

  @override
  String get deletedAt => 'Şu tarihte silindi';

  @override
  String get createdAt => 'Oluşturulma Tarihi';

  @override
  String get updatedAt => 'Güncelleme Tarihi';

  @override
  String get reports => 'Raporlar';

  @override
  String get clickToSeeAllUserDevicesDetails =>
      'Tüm Kullanıcı Cihaz Detaylarını Görmek İçin Tıklayın';

  @override
  String get allDeletedMessages => 'Tüm Silinmiş Mesajlar';

  @override
  String get voiceCallMessage => 'Sesli Arama Mesajı';

  @override
  String get totalRooms => 'Toplam Odalar';

  @override
  String get directRooms => 'Doğrudan Odalar';

  @override
  String get userAction => 'Kullanıcı Eylemi';

  @override
  String get status => 'Durum';

  @override
  String get joinedAt => 'Katılım Tarihi';

  @override
  String get saveLogin => 'Girişi Kaydet';

  @override
  String get passwordIsRequired => 'Şifre Gereklidir';

  @override
  String get verified => 'Doğrulandı';

  @override
  String get pending => 'Bekliyor';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => 'Açıklama Gereklidir';

  @override
  String get seconds => 'saniye';

  @override
  String get clickToSeeAllUserInformations =>
      'Tüm Kullanıcı Bilgilerini Görmek İçin Tıklayın';

  @override
  String get clickToSeeAllUserCountries =>
      'Tüm Kullanıcı Ülkelerini Görmek İçin Tıklayın';

  @override
  String get clickToSeeAllUserMessagesDetails =>
      'Tüm Kullanıcı Mesaj Detaylarını Görmek İçin Tıklayın';

  @override
  String get clickToSeeAllUserRoomsDetails =>
      'Tüm Kullanıcı Oda Detaylarını Görmek İçin Tıklayın';

  @override
  String get clickToSeeAllUserReports =>
      'Tüm Kullanıcı Raporlarını Görmek İçin Tıklayın';

  @override
  String get banAt => 'Şu tarihte engellendi';

  @override
  String get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
      'Şu anda yalnızca okuma yetkili bir yönetici olarak giriş yaptınız. Bu bir test sürümü olduğundan yapılan tüm düzenlemeler uygulanmayacaktır.';

  @override
  String get createStory => 'Hikaye oluştur';

  @override
  String get writeACaption => 'Altyazı yazın...';

  @override
  String get storyCreatedSuccessfully => 'Hikaye başarıyla oluşturuldu';

  @override
  String get stories => 'Hikayeler';

  @override
  String get clear => 'Temizle';

  @override
  String get clearCallsConfirm => 'Aramaları temizlemeyi onayla';

  @override
  String get chooseHowAutomaticDownloadWorks =>
      'Otomatik indirmenin nasıl çalışacağını seçin';

  @override
  String get whenUsingMobileData => 'Mobil veri kullanırken';

  @override
  String get whenUsingWifi => 'Wi-Fi kullanırken';

  @override
  String get image => 'Resim';

  @override
  String get myPrivacy => 'Gizliliğim';

  @override
  String get createTextStory => 'Metin hikayesi oluştur';

  @override
  String get createMediaStory => 'Medya hikayesi oluştur';

  @override
  String get camera => 'Kamera';

  @override
  String get gallery => 'Galeri';

  @override
  String get recentUpdate => 'Son güncelleme';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => 'Yeni hikaye ekle';

  @override
  String get updateYourProfile => 'Profilinizi güncelleyin';

  @override
  String get configureYourAccountPrivacy => 'Hesap gizliliğinizi yapılandırın';

  @override
  String get youInPublicSearch => 'Genel aramada siz';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      'Profiliniz genel aramada ve gruplara ekleme için görünür';

  @override
  String get yourLastSeen => 'Son görülme';

  @override
  String get yourLastSeenInChats => 'Sohbetlerde son görülme';

  @override
  String get startNewChatWithYou => 'Sizle yeni bir sohbet başlatın';

  @override
  String get yourStory => 'Hikayeniz';

  @override
  String get forRequest => 'İstek için';

  @override
  String get public => 'Genel';

  @override
  String get createYourStory => 'Hikayenizi oluşturun';

  @override
  String get shareYourStatus => 'Durumunuzu paylaşın';

  @override
  String get oneSeenMessage => 'Bir kez görülen mesaj';

  @override
  String get messageHasBeenViewed => 'Mesaj görüldü';

  @override
  String get clickToSee => 'Görmek için tıklayın';

  @override
  String get images => 'Resimler';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String get areYouSureRemoveAccount =>
      'Are you sure you want to remove this account?';

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';

  @override
  String get accountAddedSuccessfully => 'Account added successfully';

  @override
  String get addProfilePicture => 'Add Profile Picture';

  @override
  String get addProfilePictureSubtitle =>
      'Add a profile picture to help others recognize you';

  @override
  String get pleaseSelectProfilePicture => 'Please select a profile picture';

  @override
  String get uploading => 'Uploading...';

  @override
  String get continueText => 'Continue';

  @override
  String get profilePictureRequired =>
      'A profile picture is required to continue. Please select an image to upload.';

  @override
  String get shareProfile => 'Share Profile';
}
