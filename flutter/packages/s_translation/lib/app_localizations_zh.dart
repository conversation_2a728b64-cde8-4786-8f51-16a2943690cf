// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get done => '完成';

  @override
  String get loading => '加载中...';

  @override
  String get messageHasBeenDeleted => '消息已被删除';

  @override
  String get mute => '静音';

  @override
  String get cancel => '取消';

  @override
  String get typing => '正在输入...';

  @override
  String get ok => '确定';

  @override
  String get recording => '录音中...';

  @override
  String get connecting => '连接中...';

  @override
  String get deleteYouCopy => '删除你的副本';

  @override
  String get unMute => '取消静音';

  @override
  String get delete => '删除';

  @override
  String get report => '举报';

  @override
  String get leaveGroup => '退出群组';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      '您确定要允许复制吗？此操作无法撤销';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      '您确定要离开此群组吗？此操作无法撤销';

  @override
  String get leaveGroupAndDeleteYourMessageCopy => '退出群组并删除您的消息副本';

  @override
  String get vMessageInfoTrans => '消息信息';

  @override
  String get updateTitleTo => '更新标题为';

  @override
  String get updateImage => '更新图片';

  @override
  String get joinedBy => '由以下成员加入：';

  @override
  String get promotedToAdminBy => '由以下成员提升为管理员：';

  @override
  String get dismissedToMemberBy => '由以下成员撤销管理员权限：';

  @override
  String get leftTheGroup => '退出了群组';

  @override
  String get you => '您';

  @override
  String get kickedBy => '被以下成员踢出群组：';

  @override
  String get groupCreatedBy => '群组由以下成员创建：';

  @override
  String get groupDeletedBy => 'Group deleted by';

  @override
  String get addedYouToNewBroadcast => '将您添加到新广播';

  @override
  String get download => '下载';

  @override
  String get copy => '复制';

  @override
  String get info => '信息';

  @override
  String get share => '分享';

  @override
  String get forward => '转发';

  @override
  String get reply => '回复';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => '从所有人中删除';

  @override
  String get deleteFromMe => '从我这里删除';

  @override
  String get downloading => '下载中...';

  @override
  String get fileHasBeenSavedTo => '文件已保存至';

  @override
  String get online => '在线';

  @override
  String get youDontHaveAccess => '您无权访问';

  @override
  String get replyToYourSelf => '回复自己';

  @override
  String get repliedToYourSelf => '已回复自己';

  @override
  String get audioCall => '音频通话';

  @override
  String get ring => '响铃';

  @override
  String get canceled => '已取消';

  @override
  String get timeout => '超时';

  @override
  String get rejected => '已拒绝';

  @override
  String get finished => '已结束';

  @override
  String get inCall => '通话中';

  @override
  String get sessionEnd => '会话结束';

  @override
  String get yesterday => '昨天';

  @override
  String get today => '今天';

  @override
  String get textFieldHint => '输入消息...';

  @override
  String get files => '文件';

  @override
  String get location => '位置';

  @override
  String get shareMediaAndLocation => '分享媒体和位置';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize => '存在大小超出允许的视频';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize => '存在大小超出允许的文件';

  @override
  String get makeCall => '发起通话';

  @override
  String get areYouWantToMakeVideoCall => '您想进行视频通话吗？';

  @override
  String get areYouWantToMakeVoiceCall => '您想进行语音通话吗？';

  @override
  String get vMessagesInfoTrans => '消息信息';

  @override
  String get star => '加星标';

  @override
  String get minutes => '分钟';

  @override
  String get sendMessage => '发送消息';

  @override
  String get deleteUser => '删除用户';

  @override
  String get actions => '操作';

  @override
  String get youAreAboutToDeleteThisUserFromYourList => '您即将从列表中删除此用户';

  @override
  String get updateBroadcastTitle => '更新广播标题';

  @override
  String get usersAddedSuccessfully => '成功添加用户';

  @override
  String get broadcastSettings => '广播设置';

  @override
  String get addParticipants => '添加参与者';

  @override
  String get broadcastParticipants => '广播参与者';

  @override
  String get updateGroupDescription => '更新群组描述';

  @override
  String get updateGroupTitle => '更新群组标题';

  @override
  String get groupSettings => '群组设置';

  @override
  String get description => '描述';

  @override
  String get muteNotifications => '静音通知';

  @override
  String get groupParticipants => '群组参与者';

  @override
  String get blockUser => '屏蔽用户';

  @override
  String get areYouSureToBlock => '您确定要屏蔽';

  @override
  String get userPage => '用户页面';

  @override
  String get starMessage => '加星标消息';

  @override
  String get showMedia => '显示媒体';

  @override
  String get reportUser => '举报用户';

  @override
  String get groupName => '群组名称';

  @override
  String get changeSubject => '更改主题';

  @override
  String get titleIsRequired => '需要标题';

  @override
  String get createBroadcast => '创建广播';

  @override
  String get broadcastName => '广播名称';

  @override
  String get createGroup => '创建群组';

  @override
  String get forgetPassword => '忘记密码';

  @override
  String get globalSearch => '全局搜索';

  @override
  String get dismissesToMember => '撤销为成员';

  @override
  String get setToAdmin => '设为管理员';

  @override
  String get kickMember => '踢出成员';

  @override
  String get youAreAboutToDismissesToMember => '您即将撤销为成员';

  @override
  String get youAreAboutToKick => '您即将踢出';

  @override
  String get groupMembers => '群组成员';

  @override
  String get tapForPhoto => '点击以查看照片';

  @override
  String get weHighRecommendToDownloadThisUpdate => '我们强烈建议您下载此更新';

  @override
  String get newGroup => '新群组';

  @override
  String get newBroadcast => '新广播';

  @override
  String get starredMessage => '已加星标消息';

  @override
  String get settings => '设置';

  @override
  String get chats => '聊天';

  @override
  String get recentUpdates => '最近更新';

  @override
  String get startChat => '开始聊天';

  @override
  String get newUpdateIsAvailable => '有新更新可用';

  @override
  String get emailNotValid => '电子邮件无效';

  @override
  String get passwordMustHaveValue => '密码必须填写';

  @override
  String get error => '错误';

  @override
  String get password => '密码';

  @override
  String get login => '登录';

  @override
  String get needNewAccount => '需要新账号？';

  @override
  String get register => '注册';

  @override
  String get nameMustHaveValue => '名称必须填写';

  @override
  String get passwordNotMatch => '密码不匹配';

  @override
  String get name => '名称';

  @override
  String get email => '电子邮件';

  @override
  String get confirmPassword => '确认密码';

  @override
  String get alreadyHaveAnAccount => '已经有账号？';

  @override
  String get logOut => '注销';

  @override
  String get back => '返回';

  @override
  String get sendCodeToMyEmail => '发送验证码到我的电子邮件';

  @override
  String get invalidLoginData => '无效的登录数据';

  @override
  String get userEmailNotFound => '未找到用户电子邮件';

  @override
  String get yourAccountBlocked => '您的帐号已被封禁';

  @override
  String get yourAccountDeleted => '您的帐号已被删除';

  @override
  String get userAlreadyRegister => '用户已注册';

  @override
  String get codeHasBeenExpired => '验证码已过期';

  @override
  String get invalidCode => '无效的验证码';

  @override
  String get whileAuthCanFindYou => '在认证期间找不到您';

  @override
  String get userRegisterStatusNotAcceptedYet => '用户注册状态尚未被接受';

  @override
  String get deviceHasBeenLogoutFromAllDevices => '设备已从所有设备注销';

  @override
  String get userDeviceSessionEndDeviceDeleted => '用户设备会话结束设备已删除';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      '尚未发送验证码到您的电子邮件以验证您的电子邮件';

  @override
  String get roomAlreadyInCall => '房间已在通话中';

  @override
  String get peerUserInCallNow => '对方用户正在通话中';

  @override
  String get callNotAllowed => '不允许通话';

  @override
  String get peerUserDeviceOffline => '对方用户设备离线';

  @override
  String get emailMustBeValid => '电子邮件必须有效';

  @override
  String get wait2MinutesToSendMail => '等待2分钟发送邮件';

  @override
  String get codeMustEqualToSixNumbers => '验证码必须是六位数字';

  @override
  String get newPasswordMustHaveValue => '新密码必须填写';

  @override
  String get confirmPasswordMustHaveValue => '确认密码必须填写';

  @override
  String get congregationsYourAccountHasBeenAccepted => '恭喜，您的帐号已被接受';

  @override
  String get yourAccountIsUnderReview => '您的帐号正在审核中';

  @override
  String get waitingList => '等待名单';

  @override
  String get welcome => '欢迎';

  @override
  String get retry => '重试';

  @override
  String get deleteMember => '删除成员';

  @override
  String get profile => '个人资料';

  @override
  String get broadcastInfo => '广播信息';

  @override
  String get updateTitle => '更新标题';

  @override
  String get members => '成员';

  @override
  String get addMembers => '添加成员';

  @override
  String get success => '成功';

  @override
  String get media => '媒体';

  @override
  String get docs => '文档';

  @override
  String get links => '链接';

  @override
  String get soon => '即将';

  @override
  String get unStar => '取消加星标';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      '更新群组描述将更新所有群组成员';

  @override
  String get updateNickname => '更新昵称';

  @override
  String get groupInfo => '群组信息';

  @override
  String get youNotParticipantInThisGroup => '您不是该群组的成员';

  @override
  String get search => '搜索';

  @override
  String get mediaLinksAndDocs => '媒体、链接和文档';

  @override
  String get starredMessages => '加星标的消息';

  @override
  String get nickname => '昵称';

  @override
  String get none => '无';

  @override
  String get yes => '是';

  @override
  String get no => '否';

  @override
  String get exitGroup => '退出群组';

  @override
  String get clickToAddGroupDescription => '点击添加群组描述';

  @override
  String get unBlockUser => '取消屏蔽用户';

  @override
  String get areYouSureToUnBlock => '您确定要取消屏蔽吗';

  @override
  String get contactInfo => '联系信息';

  @override
  String get audio => '音频';

  @override
  String get video => '视频';

  @override
  String get hiIamUse => '您好，我在使用';

  @override
  String get on => '开';

  @override
  String get off => '关';

  @override
  String get unBlock => '取消屏蔽';

  @override
  String get block => '屏蔽';

  @override
  String get chooseAtLestOneMember => '至少选择一个成员';

  @override
  String get close => '关闭';

  @override
  String get next => '下一步';

  @override
  String get appMembers => '应用程序成员';

  @override
  String get create => '创建';

  @override
  String get upgradeToAdmin => '升级为管理员';

  @override
  String get update => '更新';

  @override
  String get deleteChat => '删除聊天';

  @override
  String get clearChat => '清除聊天';

  @override
  String get showHistory => '显示历史记录';

  @override
  String get groupIcon => '群组图标';

  @override
  String get tapToSelectAnIcon => '点击选择图标';

  @override
  String get groupDescription => '群组描述';

  @override
  String get more => '更多';

  @override
  String get messageInfo => '消息信息';

  @override
  String get successfullyDownloadedIn => '成功下载至';

  @override
  String get delivered => '已送达';

  @override
  String get read => '已阅读';

  @override
  String get orLoginWith => '或使用以下方式登录';

  @override
  String get resetPassword => '重置密码';

  @override
  String get otpCode => 'OTP验证码';

  @override
  String get newPassword => '新密码';

  @override
  String get areYouSure => '您确定吗';

  @override
  String get broadcastMembers => '广播成员';

  @override
  String get phone => '电话';

  @override
  String get users => '用户';

  @override
  String get calls => '通话';

  @override
  String get yourAreAboutToLogoutFromThisAccount => '您即将从此帐号注销';

  @override
  String get noUpdatesAvailableNow => '目前没有可用更新';

  @override
  String get dataPrivacy => '数据隐私';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      '所有数据已备份，您无需自行管理数据保存！如果您注销然后重新登录，您将看到与Web版本相同的所有聊天记录';

  @override
  String get account => '帐号';

  @override
  String get linkedDevices => '已链接设备';

  @override
  String get storageAndData => '存储和数据';

  @override
  String get tellAFriend => '告诉朋友';

  @override
  String get help => '帮助';

  @override
  String get blockedUsers => '已屏蔽用户';

  @override
  String get inAppAlerts => '应用内警报';

  @override
  String get language => '语言';

  @override
  String get adminNotification => '管理员通知';

  @override
  String get checkForUpdates => '检查更新';

  @override
  String get linkByQrCode => '通过QR码链接';

  @override
  String get deviceStatus => '设备状态';

  @override
  String get desktopAndOtherDevices => '桌面和其他设备';

  @override
  String get linkADeviceSoon => '链接设备（即将推出）';

  @override
  String get lastActiveFrom => '上次活动时间：';

  @override
  String get tapADeviceToEditOrLogOut => '点击设备以编辑或注销。';

  @override
  String get contactUs => '联系我们';

  @override
  String get supportChatSoon => '支持聊天（即将推出）';

  @override
  String get updateYourName => '更新您的名称';

  @override
  String get updateYourBio => '更新您的个人简介';

  @override
  String get edit => '编辑';

  @override
  String get about => '关于';

  @override
  String get oldPassword => '旧密码';

  @override
  String get deleteMyAccount => '删除我的帐号';

  @override
  String get passwordHasBeenChanged => '密码已更改';

  @override
  String get logoutFromAllDevices => '从所有设备注销？';

  @override
  String get updateYourPassword => '更新您的密码';

  @override
  String get enterNameAndAddOptionalProfilePicture => '输入您的名称并添加可选的个人资料图片';

  @override
  String get privacyPolicy => '隐私政策';

  @override
  String get chat => '聊天';

  @override
  String get send => '发送';

  @override
  String get reportHasBeenSubmitted => '您的报告已提交';

  @override
  String get offline => '离线';

  @override
  String get harassmentOrBullyingDescription =>
      '骚扰或欺凌：此选项允许用户举报针对他们或其他人的骚扰消息、威胁或其他形式的欺凌。';

  @override
  String get spamOrScamDescription =>
      '垃圾邮件或欺诈：此选项可用于举报发送垃圾邮件消息、未经请求的广告或试图欺诈他人的帐户。';

  @override
  String get areYouSureToReportUserToAdmin => '您确定要向管理员举报此用户吗？';

  @override
  String get groupWith => '与以下成员组成群组：';

  @override
  String get inappropriateContentDescription =>
      '不适当内容：用户可以选择此选项以举报任何涉及性暴露、仇恨言论或其他违反社区标准的内容。';

  @override
  String get otherCategoryDescription =>
      '其他：这是一个通用类别，适用于不容易归类到上述类别的违规行为。可以提供一个文本框供用户提供额外的细节。';

  @override
  String get explainWhatHappens => '在此解释发生了什么';

  @override
  String get loginAgain => '重新登录！';

  @override
  String get yourSessionIsEndedPleaseLoginAgain => '您的会话已结束，请重新登录！';

  @override
  String get aboutToBlockUserWithConsequences =>
      '您即将屏蔽此用户。您将无法向他发送消息，也无法将他添加到群组或广播中！';

  @override
  String
      get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
          '您即将删除您的帐号，您的帐号将不再出现在用户列表中';

  @override
  String get admin => '管理员';

  @override
  String get member => '成员';

  @override
  String get creator => '创建者';

  @override
  String get currentDevice => '当前设备';

  @override
  String get visits => '访问次数';

  @override
  String get chooseRoom => '选择房间';

  @override
  String get deleteThisDeviceDesc => '删除此设备意味着立即注销此设备';

  @override
  String get youAreAboutToUpgradeToAdmin => '您即将升级为管理员';

  @override
  String get microphonePermissionMustBeAccepted =>
      'Microphone permission must be accepted';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'Microphone and camera permission must be accepted';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater => '现在允许登录。请稍后再试。';

  @override
  String get dashboard => '仪表板';

  @override
  String get notification => '通知';

  @override
  String get total => '总计';

  @override
  String get blocked => '已阻止';

  @override
  String get deleted => '已删除';

  @override
  String get accepted => '已接受';

  @override
  String get notAccepted => '未接受';

  @override
  String get web => 'Web';

  @override
  String get android => 'Android';

  @override
  String get macOs => 'macOS';

  @override
  String get windows => 'Windows';

  @override
  String get other => '其他';

  @override
  String get totalVisits => '总访问量';

  @override
  String get totalMessages => '总消息数';

  @override
  String get textMessages => '文本消息';

  @override
  String get imageMessages => '图片消息';

  @override
  String get videoMessages => '视频消息';

  @override
  String get voiceMessages => '语音消息';

  @override
  String get fileMessages => '文件消息';

  @override
  String get infoMessages => '信息消息';

  @override
  String get voiceCallMessages => '语音通话消息';

  @override
  String get videoCallMessages => '视频通话消息';

  @override
  String get locationMessages => '位置消息';

  @override
  String get directChat => '直接聊天';

  @override
  String get group => '群组';

  @override
  String get broadcast => '广播';

  @override
  String get messageCounter => '消息计数器';

  @override
  String get roomCounter => '房间计数器';

  @override
  String get countries => '国家';

  @override
  String get devices => '设备';

  @override
  String get notificationTitle => '通知标题';

  @override
  String get notificationDescription => '通知描述';

  @override
  String get notificationsPage => '通知页面';

  @override
  String get updateFeedBackEmail => '更新反馈电子邮件';

  @override
  String get setMaxMessageForwardAndShare => '设置最大消息转发和分享数';

  @override
  String get setNewPrivacyPolicyUrl => '设置新的隐私政策链接';

  @override
  String get forgetPasswordExpireTime => '忘记密码过期时间';

  @override
  String get chatsAndCallsAreEndToEndEncrypted =>
      'Chats and calls are end-to-end encrypted. Only people in this chat can read, listen to, or share them.';

  @override
  String get clickToLearnMore => 'Click to learn more.';

  @override
  String get callTimeoutInSeconds => '呼叫超时（秒）';

  @override
  String get setMaxGroupMembers => '设置最大群组成员数';

  @override
  String get setMaxBroadcastMembers => '设置最大广播成员数';

  @override
  String get allowCalls => '允许通话';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      '如果启用此选项，视频和语音通话将被允许';

  @override
  String get allowAds => '允许广告';

  @override
  String get allowMobileLogin => '允许移动登录';

  @override
  String get allowWebLogin => '允许Web登录';

  @override
  String get messages => '消息';

  @override
  String get appleStoreAppUrl => 'Apple App Store 链接';

  @override
  String get googlePlayAppUrl => 'Google Play 应用链接';

  @override
  String get privacyUrl => '隐私政策链接';

  @override
  String get feedBackEmail => '反馈电子邮件';

  @override
  String
      get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
          '如果禁用此选项，将阻止发送聊天文件、图片、视频和位置信息';

  @override
  String get allowSendMedia => '允许发送媒体文件';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      '如果禁用此选项，将阻止创建聊天广播';

  @override
  String get allowCreateBroadcast => '允许创建广播';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      '如果禁用此选项，将阻止创建聊天群组';

  @override
  String get allowCreateGroups => '允许创建群组';

  @override
  String
      get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
          '如果禁用此选项，将阻止桌面登录或注册（Windows 和 macOS）';

  @override
  String get allowDesktopLogin => '允许桌面登录';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      '如果禁用此选项，将阻止Web登录或注册';

  @override
  String
      get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
          '如果启用此选项，Google Ads 横幅将出现在聊天中';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'If this option is enabled, the Google Ads banner will appear in chats.';

  @override
  String get userProfile => '用户个人资料';

  @override
  String get userInfo => '用户信息';

  @override
  String get fullName => '全名';

  @override
  String get bio => '简介';

  @override
  String get noBio => '无简介';

  @override
  String get verifiedAt => '已验证';

  @override
  String get country => '国家';

  @override
  String get registerStatus => '注册状态';

  @override
  String get registerMethod => '注册方式';

  @override
  String get banTo => '封禁至';

  @override
  String get deletedAt => '已删除于';

  @override
  String get createdAt => '创建于';

  @override
  String get updatedAt => '更新于';

  @override
  String get reports => '报告';

  @override
  String get clickToSeeAllUserDevicesDetails => '点击查看所有用户设备详细信息';

  @override
  String get allDeletedMessages => '所有已删除消息';

  @override
  String get voiceCallMessage => '语音通话消息';

  @override
  String get totalRooms => '总房间数';

  @override
  String get directRooms => '直接房间';

  @override
  String get userAction => '用户操作';

  @override
  String get status => '状态';

  @override
  String get joinedAt => '加入于';

  @override
  String get saveLogin => '保存登录';

  @override
  String get passwordIsRequired => '密码必填';

  @override
  String get verified => '已验证';

  @override
  String get pending => '待处理';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => '描述必填';

  @override
  String get seconds => '秒';

  @override
  String get clickToSeeAllUserInformations => '点击查看所有用户信息';

  @override
  String get clickToSeeAllUserCountries => '点击查看所有用户国家';

  @override
  String get clickToSeeAllUserMessagesDetails => '点击查看所有用户消息详细信息';

  @override
  String get clickToSeeAllUserRoomsDetails => '点击查看所有用户房间详细信息';

  @override
  String get clickToSeeAllUserReports => '点击查看所有用户报告';

  @override
  String get banAt => '封禁于';

  @override
  String
      get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
          '现在您以只读管理员身份登录。由于这是测试版本，您所做的所有编辑都将不会生效。';

  @override
  String get createStory => '创作故事';

  @override
  String get writeACaption => '写标题...';

  @override
  String get storyCreatedSuccessfully => '故事创建成功';

  @override
  String get stories => '故事';

  @override
  String get clear => '清除';

  @override
  String get clearCallsConfirm => '确认清除通话记录？';

  @override
  String get chooseHowAutomaticDownloadWorks => '选择自动下载的工作方式';

  @override
  String get whenUsingMobileData => '使用移动数据时';

  @override
  String get whenUsingWifi => '使用 Wi-Fi 时';

  @override
  String get image => '图片';

  @override
  String get myPrivacy => '我的隐私';

  @override
  String get createTextStory => '创建文本故事';

  @override
  String get createMediaStory => '创建媒体故事';

  @override
  String get camera => '相机';

  @override
  String get gallery => '相册';

  @override
  String get recentUpdate => '最近更新';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => '添加新故事';

  @override
  String get updateYourProfile => '更新您的个人资料';

  @override
  String get configureYourAccountPrivacy => '配置您的帐户隐私';

  @override
  String get youInPublicSearch => '您在公共搜索中';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      '您的个人资料将显示在公共搜索和添加到群组中';

  @override
  String get yourLastSeen => '最后一次看到';

  @override
  String get yourLastSeenInChats => '在聊天中最后一次看到';

  @override
  String get startNewChatWithYou => '与您开始新的聊天';

  @override
  String get yourStory => '您的故事';

  @override
  String get forRequest => '用于请求';

  @override
  String get public => '公共';

  @override
  String get createYourStory => '创建您的故事';

  @override
  String get shareYourStatus => '分享您的状态';

  @override
  String get oneSeenMessage => '已看过的消息';

  @override
  String get messageHasBeenViewed => '消息已查看';

  @override
  String get clickToSee => '点击查看';

  @override
  String get images => '图片';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String get areYouSureRemoveAccount =>
      'Are you sure you want to remove this account?';

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';

  @override
  String get accountAddedSuccessfully => 'Account added successfully';

  @override
  String get addProfilePicture => 'Add Profile Picture';

  @override
  String get addProfilePictureSubtitle =>
      'Add a profile picture to help others recognize you';

  @override
  String get pleaseSelectProfilePicture => 'Please select a profile picture';

  @override
  String get uploading => 'Uploading...';

  @override
  String get continueText => 'Continue';

  @override
  String get profilePictureRequired =>
      'A profile picture is required to continue. Please select an image to upload.';

  @override
  String get shareProfile => 'Share Profile';
}
