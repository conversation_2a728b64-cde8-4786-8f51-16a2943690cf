// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ko locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ko';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Dashboard": MessageLookupByLibrary.simpleMessage("대시보드"),
        "about": MessageLookupByLibrary.simpleMessage("정보"),
        "aboutToBlockUserWithConsequences":
            MessageLookupByLibrary.simpleMessage(
                "이 사용자를 차단하려고 합니다. 그에게 채팅을 보낼 수 없으며 그룹이나 방송에 추가할 수 없습니다!"),
        "accepted": MessageLookupByLibrary.simpleMessage("수락됨"),
        "account": MessageLookupByLibrary.simpleMessage("계정"),
        "actions": MessageLookupByLibrary.simpleMessage("작업"),
        "addMembers": MessageLookupByLibrary.simpleMessage("멤버 추가"),
        "addNewStory": MessageLookupByLibrary.simpleMessage("새로운 스토리 추가"),
        "addParticipants": MessageLookupByLibrary.simpleMessage("참가자 추가"),
        "addedYouToNewBroadcast":
            MessageLookupByLibrary.simpleMessage("새로운 방송에 당신을 추가했습니다"),
        "admin": MessageLookupByLibrary.simpleMessage("관리자"),
        "adminNotification": MessageLookupByLibrary.simpleMessage("관리자 알림"),
        "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself":
            MessageLookupByLibrary.simpleMessage(
                "모든 데이터가 백업되었으므로 데이터를 직접 관리할 필요가 없습니다. 다시 로그인하면 모든 채팅이 동일하게 표시됩니다."),
        "allDeletedMessages":
            MessageLookupByLibrary.simpleMessage("모든 삭제된 메시지"),
        "allowAds": MessageLookupByLibrary.simpleMessage("광고 허용"),
        "allowCalls": MessageLookupByLibrary.simpleMessage("통화 허용"),
        "allowCreateBroadcast":
            MessageLookupByLibrary.simpleMessage("방송 생성 허용"),
        "allowCreateGroups": MessageLookupByLibrary.simpleMessage("그룹 생성 허용"),
        "allowDesktopLogin":
            MessageLookupByLibrary.simpleMessage("데스크톱 로그인 허용"),
        "allowMobileLogin": MessageLookupByLibrary.simpleMessage("모바일 로그인 허용"),
        "allowSendMedia": MessageLookupByLibrary.simpleMessage("미디어 전송 허용"),
        "allowWebLogin": MessageLookupByLibrary.simpleMessage("웹 로그인 허용"),
        "alreadyHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("이미 계정이 있으십니까?"),
        "android": MessageLookupByLibrary.simpleMessage("안드로이드"),
        "appMembers": MessageLookupByLibrary.simpleMessage("앱 멤버"),
        "appleStoreAppUrl":
            MessageLookupByLibrary.simpleMessage("애플 스토어 앱 URL"),
        "areYouSure": MessageLookupByLibrary.simpleMessage("확실합니까?"),
        "areYouSureToBlock": MessageLookupByLibrary.simpleMessage("차단하시겠습니까"),
        "areYouSureToLeaveThisGroupThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "이 그룹을 나가시겠습니까? 이 작업은 되돌릴 수 없습니다"),
        "areYouSureToPermitYourCopyThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "내 사본을 허용하시겠습니까? 이 작업은 되돌릴 수 없습니다"),
        "areYouSureToReportUserToAdmin": MessageLookupByLibrary.simpleMessage(
            "이 사용자에 대한 신고를 관리자에게 제출하시겠습니까?"),
        "areYouSureToUnBlock":
            MessageLookupByLibrary.simpleMessage("차단 해제하시겠습니까"),
        "areYouWantToMakeVideoCall":
            MessageLookupByLibrary.simpleMessage("비디오 통화를 시작하시겠습니까?"),
        "areYouWantToMakeVoiceCall":
            MessageLookupByLibrary.simpleMessage("음성 통화를 시작하시겠습니까?"),
        "audio": MessageLookupByLibrary.simpleMessage("오디오"),
        "audioCall": MessageLookupByLibrary.simpleMessage("음성 통화"),
        "back": MessageLookupByLibrary.simpleMessage("뒤로"),
        "banAt": MessageLookupByLibrary.simpleMessage("차단 일자"),
        "banTo": MessageLookupByLibrary.simpleMessage("차단 종료 시간"),
        "bio": MessageLookupByLibrary.simpleMessage("자기 소개"),
        "block": MessageLookupByLibrary.simpleMessage("차단"),
        "blockUser": MessageLookupByLibrary.simpleMessage("사용자 차단"),
        "blocked": MessageLookupByLibrary.simpleMessage("차단됨"),
        "blockedUsers": MessageLookupByLibrary.simpleMessage("차단된 사용자"),
        "broadcast": MessageLookupByLibrary.simpleMessage("방송"),
        "broadcastInfo": MessageLookupByLibrary.simpleMessage("방송 정보"),
        "broadcastMembers": MessageLookupByLibrary.simpleMessage("방송 멤버"),
        "broadcastName": MessageLookupByLibrary.simpleMessage("방송 이름"),
        "broadcastParticipants": MessageLookupByLibrary.simpleMessage("방송 참가자"),
        "broadcastSettings": MessageLookupByLibrary.simpleMessage("방송 설정"),
        "callNotAllowed": MessageLookupByLibrary.simpleMessage("통화가 허용되지 않음"),
        "callTimeoutInSeconds":
            MessageLookupByLibrary.simpleMessage("통화 제한 시간 (초)"),
        "calls": MessageLookupByLibrary.simpleMessage("통화"),
        "camera": MessageLookupByLibrary.simpleMessage("카메라"),
        "cancel": MessageLookupByLibrary.simpleMessage("취소"),
        "canceled": MessageLookupByLibrary.simpleMessage("취소됨"),
        "changeSubject": MessageLookupByLibrary.simpleMessage("주제 변경"),
        "chat": MessageLookupByLibrary.simpleMessage("채팅"),
        "chats": MessageLookupByLibrary.simpleMessage("채팅"),
        "checkForUpdates": MessageLookupByLibrary.simpleMessage("업데이트 확인"),
        "chooseAtLestOneMember":
            MessageLookupByLibrary.simpleMessage("적어도 하나의 멤버를 선택하세요"),
        "chooseHowAutomaticDownloadWorks":
            MessageLookupByLibrary.simpleMessage("자동 다운로드 작동 방식 선택"),
        "chooseRoom": MessageLookupByLibrary.simpleMessage("방 선택"),
        "clear": MessageLookupByLibrary.simpleMessage("지우기"),
        "clearCallsConfirm":
            MessageLookupByLibrary.simpleMessage("전화 지우기를 확인하시겠습니까?"),
        "clearChat": MessageLookupByLibrary.simpleMessage("채팅 지우기"),
        "clickToAddGroupDescription":
            MessageLookupByLibrary.simpleMessage("그룹 설명을 추가하려면 클릭하세요"),
        "clickToSee": MessageLookupByLibrary.simpleMessage("보려면 클릭"),
        "clickToSeeAllUserCountries":
            MessageLookupByLibrary.simpleMessage("모든 사용자 국가 보기를 클릭하세요"),
        "clickToSeeAllUserDevicesDetails":
            MessageLookupByLibrary.simpleMessage("모든 사용자 디바이스 세부 정보 보기"),
        "clickToSeeAllUserInformations":
            MessageLookupByLibrary.simpleMessage("모든 사용자 정보 보기를 클릭하세요"),
        "clickToSeeAllUserMessagesDetails":
            MessageLookupByLibrary.simpleMessage("모든 사용자 메시지 세부 정보 보기를 클릭하세요"),
        "clickToSeeAllUserReports":
            MessageLookupByLibrary.simpleMessage("모든 사용자 리포트 보기를 클릭하세요"),
        "clickToSeeAllUserRoomsDetails":
            MessageLookupByLibrary.simpleMessage("모든 사용자 방 세부 정보 보기를 클릭하세요"),
        "close": MessageLookupByLibrary.simpleMessage("닫기"),
        "codeHasBeenExpired":
            MessageLookupByLibrary.simpleMessage("코드가 만료되었습니다"),
        "codeMustEqualToSixNumbers":
            MessageLookupByLibrary.simpleMessage("코드는 6자리여야 합니다"),
        "configureYourAccountPrivacy":
            MessageLookupByLibrary.simpleMessage("계정 개인 정보 설정"),
        "confirmPassword": MessageLookupByLibrary.simpleMessage("비밀번호 확인"),
        "confirmPasswordMustHaveValue":
            MessageLookupByLibrary.simpleMessage("비밀번호 확인은 값을 가져야 합니다"),
        "congregationsYourAccountHasBeenAccepted":
            MessageLookupByLibrary.simpleMessage("계정이 승인되었습니다"),
        "connecting": MessageLookupByLibrary.simpleMessage("연결 중..."),
        "contactInfo": MessageLookupByLibrary.simpleMessage("연락처 정보"),
        "contactUs": MessageLookupByLibrary.simpleMessage("문의하기"),
        "copy": MessageLookupByLibrary.simpleMessage("복사"),
        "countries": MessageLookupByLibrary.simpleMessage("국가"),
        "country": MessageLookupByLibrary.simpleMessage("국가"),
        "create": MessageLookupByLibrary.simpleMessage("생성"),
        "createBroadcast": MessageLookupByLibrary.simpleMessage("방송 만들기"),
        "createGroup": MessageLookupByLibrary.simpleMessage("그룹 만들기"),
        "createMediaStory": MessageLookupByLibrary.simpleMessage("미디어 스토리 만들기"),
        "createStory": MessageLookupByLibrary.simpleMessage("스토리 만들기"),
        "createTextStory": MessageLookupByLibrary.simpleMessage("텍스트 스토리 만들기"),
        "createYourStory": MessageLookupByLibrary.simpleMessage("당신의 스토리 만들기"),
        "createdAt": MessageLookupByLibrary.simpleMessage("생성 시간"),
        "creator": MessageLookupByLibrary.simpleMessage("창조자"),
        "currentDevice": MessageLookupByLibrary.simpleMessage("현재 기기"),
        "dashboard": MessageLookupByLibrary.simpleMessage("대시보드"),
        "dataPrivacy": MessageLookupByLibrary.simpleMessage("데이터 개인 정보 보호"),
        "delete": MessageLookupByLibrary.simpleMessage("삭제"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("채팅 삭제"),
        "deleteFromAll": MessageLookupByLibrary.simpleMessage("모두에서 삭제"),
        "deleteFromMe": MessageLookupByLibrary.simpleMessage("나에서 삭제"),
        "deleteMember": MessageLookupByLibrary.simpleMessage("멤버 삭제"),
        "deleteMyAccount": MessageLookupByLibrary.simpleMessage("내 계정 삭제"),
        "deleteThisDeviceDesc":
            MessageLookupByLibrary.simpleMessage("이 기기를 삭제하면 즉시 로그아웃됩니다"),
        "deleteUser": MessageLookupByLibrary.simpleMessage("사용자 삭제"),
        "deleteYouCopy": MessageLookupByLibrary.simpleMessage("내 사본 삭제"),
        "deleted": MessageLookupByLibrary.simpleMessage("삭제됨"),
        "deletedAt": MessageLookupByLibrary.simpleMessage("삭제 시간"),
        "delivered": MessageLookupByLibrary.simpleMessage("전달됨"),
        "description": MessageLookupByLibrary.simpleMessage("설명"),
        "descriptionIsRequired": MessageLookupByLibrary.simpleMessage("설명 필수"),
        "desktopAndOtherDevices":
            MessageLookupByLibrary.simpleMessage("데스크톱 및 기타 기기"),
        "deviceHasBeenLogoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage("모든 기기에서 로그아웃되었습니다"),
        "deviceStatus": MessageLookupByLibrary.simpleMessage("기기 상태"),
        "devices": MessageLookupByLibrary.simpleMessage("기기"),
        "directChat": MessageLookupByLibrary.simpleMessage("직접 채팅"),
        "directRooms": MessageLookupByLibrary.simpleMessage("직접 방"),
        "dismissedToMemberBy":
            MessageLookupByLibrary.simpleMessage("멤버로 내린 사람:"),
        "dismissesToMember": MessageLookupByLibrary.simpleMessage("멤버로 내림"),
        "docs": MessageLookupByLibrary.simpleMessage("문서"),
        "done": MessageLookupByLibrary.simpleMessage("완료"),
        "download": MessageLookupByLibrary.simpleMessage("다운로드"),
        "downloading": MessageLookupByLibrary.simpleMessage("다운로드 중..."),
        "edit": MessageLookupByLibrary.simpleMessage("편집"),
        "email": MessageLookupByLibrary.simpleMessage("이메일"),
        "emailMustBeValid":
            MessageLookupByLibrary.simpleMessage("이메일은 유효해야 합니다"),
        "emailNotValid": MessageLookupByLibrary.simpleMessage("유효하지 않은 이메일"),
        "enterNameAndAddOptionalProfilePicture":
            MessageLookupByLibrary.simpleMessage("이름을 입력하고 선택적 프로필 사진을 추가하세요"),
        "error": MessageLookupByLibrary.simpleMessage("오류"),
        "exitGroup": MessageLookupByLibrary.simpleMessage("그룹 나가기"),
        "explainWhatHappens":
            MessageLookupByLibrary.simpleMessage("여기에 무슨 일이 일어나는지 설명하세요"),
        "feedBackEmail": MessageLookupByLibrary.simpleMessage("피드백 이메일"),
        "fileHasBeenSavedTo":
            MessageLookupByLibrary.simpleMessage("파일이 다음 위치에 저장되었습니다"),
        "fileMessages": MessageLookupByLibrary.simpleMessage("파일 메시지"),
        "files": MessageLookupByLibrary.simpleMessage("파일"),
        "finished": MessageLookupByLibrary.simpleMessage("완료됨"),
        "forRequest": MessageLookupByLibrary.simpleMessage("요청 용"),
        "forgetPassword": MessageLookupByLibrary.simpleMessage("비밀번호 잊어버림"),
        "forgetPasswordExpireTime":
            MessageLookupByLibrary.simpleMessage("비밀번호 재설정 만료 시간"),
        "forward": MessageLookupByLibrary.simpleMessage("전달"),
        "fullName": MessageLookupByLibrary.simpleMessage("전체 이름"),
        "gallery": MessageLookupByLibrary.simpleMessage("갤러리"),
        "globalSearch": MessageLookupByLibrary.simpleMessage("글로벌 검색"),
        "googlePlayAppUrl":
            MessageLookupByLibrary.simpleMessage("구글 플레이 앱 URL"),
        "group": MessageLookupByLibrary.simpleMessage("그룹"),
        "groupCreatedBy": MessageLookupByLibrary.simpleMessage("그룹 생성자:"),
        "groupDescription": MessageLookupByLibrary.simpleMessage("그룹 설명"),
        "groupIcon": MessageLookupByLibrary.simpleMessage("그룹 아이콘"),
        "groupInfo": MessageLookupByLibrary.simpleMessage("그룹 정보"),
        "groupMembers": MessageLookupByLibrary.simpleMessage("그룹 멤버"),
        "groupName": MessageLookupByLibrary.simpleMessage("그룹 이름"),
        "groupParticipants": MessageLookupByLibrary.simpleMessage("그룹 참가자"),
        "groupSettings": MessageLookupByLibrary.simpleMessage("그룹 설정"),
        "groupWith": MessageLookupByLibrary.simpleMessage("그룹과 함께"),
        "harassmentOrBullyingDescription": MessageLookupByLibrary.simpleMessage(
            "괴롭힘 또는 괴롭힘: 이 옵션은 사용자가 자신이나 다른 사람을 괴롭히거나 협박하는 개인을 신고하는 데 사용됩니다."),
        "help": MessageLookupByLibrary.simpleMessage("도움말"),
        "hiIamUse": MessageLookupByLibrary.simpleMessage("안녕하세요, 사용 중입니다"),
        "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "이 옵션이 비활성화된 경우 채팅 방송 생성이 차단됩니다"),
        "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "이 옵션이 비활성화된 경우 채팅 그룹 생성이 차단됩니다"),
        "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "이 옵션이 비활성화된 경우 데스크톱 로그인 또는 등록 (Windows 및 macOS)이 차단됩니다"),
        "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly":
            MessageLookupByLibrary.simpleMessage(
                "이 옵션이 활성화된 경우 Google 광고 배너가 채팅에 표시됩니다"),
        "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "이 옵션이 비활성화된 경우 채팅 파일, 이미지, 비디오 및 위치 전송이 차단됩니다"),
        "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "이 옵션이 비활성화된 경우 웹 로그인 또는 등록이 차단됩니다"),
        "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed":
            MessageLookupByLibrary.simpleMessage(
                "이 옵션이 활성화되면 비디오 및 음성 통화가 허용됩니다"),
        "image": MessageLookupByLibrary.simpleMessage("이미지"),
        "imageMessages": MessageLookupByLibrary.simpleMessage("이미지 메시지"),
        "images": MessageLookupByLibrary.simpleMessage("이미지"),
        "inAppAlerts": MessageLookupByLibrary.simpleMessage("앱 내 알림"),
        "inCall": MessageLookupByLibrary.simpleMessage("통화 중"),
        "inappropriateContentDescription": MessageLookupByLibrary.simpleMessage(
            "부적절한 콘텐츠: 사용자는 성적으로 음란한 자료, 혐오 발언 또는 공동체 기준을 위반하는 기타 콘텐츠를 신고하기 위해이 옵션을 선택할 수 있습니다."),
        "info": MessageLookupByLibrary.simpleMessage("정보"),
        "infoMessages": MessageLookupByLibrary.simpleMessage("정보 메시지"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("유효하지 않은 코드"),
        "invalidLoginData":
            MessageLookupByLibrary.simpleMessage("유효하지 않은 로그인 데이터"),
        "ios": MessageLookupByLibrary.simpleMessage("iOS"),
        "joinedAt": MessageLookupByLibrary.simpleMessage("가입 일자"),
        "joinedBy": MessageLookupByLibrary.simpleMessage("가입한 사람:"),
        "kickMember": MessageLookupByLibrary.simpleMessage("멤버 퇴출"),
        "kickedBy": MessageLookupByLibrary.simpleMessage("강퇴한 사람:"),
        "language": MessageLookupByLibrary.simpleMessage("언어"),
        "lastActiveFrom": MessageLookupByLibrary.simpleMessage("마지막 활동 일시"),
        "leaveGroup": MessageLookupByLibrary.simpleMessage("그룹 나가기"),
        "leaveGroupAndDeleteYourMessageCopy":
            MessageLookupByLibrary.simpleMessage("그룹 나가기 및 내 메시지 사본 삭제"),
        "leftTheGroup": MessageLookupByLibrary.simpleMessage("그룹에서 나감"),
        "linkADeviceSoon": MessageLookupByLibrary.simpleMessage("기기 연결 (곧)"),
        "linkByQrCode": MessageLookupByLibrary.simpleMessage("QR 코드로 연결"),
        "linkedDevices": MessageLookupByLibrary.simpleMessage("연결된 기기"),
        "links": MessageLookupByLibrary.simpleMessage("링크"),
        "loading": MessageLookupByLibrary.simpleMessage("로드 중..."),
        "location": MessageLookupByLibrary.simpleMessage("위치"),
        "locationMessages": MessageLookupByLibrary.simpleMessage("위치 메시지"),
        "logOut": MessageLookupByLibrary.simpleMessage("로그아웃"),
        "login": MessageLookupByLibrary.simpleMessage("로그인"),
        "loginAgain": MessageLookupByLibrary.simpleMessage("다시 로그인하세요!"),
        "loginNowAllowedNowPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "현재 로그인이 허용되지 않았습니다. 나중에 다시 시도하세요."),
        "logoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage("모든 기기에서 로그아웃하시겠습니까?"),
        "macOs": MessageLookupByLibrary.simpleMessage("macOS"),
        "makeCall": MessageLookupByLibrary.simpleMessage("통화 시작"),
        "media": MessageLookupByLibrary.simpleMessage("미디어"),
        "mediaLinksAndDocs":
            MessageLookupByLibrary.simpleMessage("미디어, 링크 및 문서"),
        "member": MessageLookupByLibrary.simpleMessage("멤버"),
        "members": MessageLookupByLibrary.simpleMessage("멤버"),
        "messageCounter": MessageLookupByLibrary.simpleMessage("메시지 카운터"),
        "messageHasBeenDeleted":
            MessageLookupByLibrary.simpleMessage("메시지가 삭제되었습니다"),
        "messageHasBeenViewed":
            MessageLookupByLibrary.simpleMessage("메시지가 확인되었습니다"),
        "messageInfo": MessageLookupByLibrary.simpleMessage("메시지 정보"),
        "messages": MessageLookupByLibrary.simpleMessage("메시지"),
        "microphoneAndCameraPermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone and camera permission must be accepted"),
        "microphonePermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone permission must be accepted"),
        "minutes": MessageLookupByLibrary.simpleMessage("분"),
        "more": MessageLookupByLibrary.simpleMessage("더 보기"),
        "mute": MessageLookupByLibrary.simpleMessage("음소거"),
        "muteNotifications": MessageLookupByLibrary.simpleMessage("알림 음소거"),
        "myPrivacy": MessageLookupByLibrary.simpleMessage("개인 정보"),
        "name": MessageLookupByLibrary.simpleMessage("이름"),
        "nameMustHaveValue":
            MessageLookupByLibrary.simpleMessage("이름은 값을 가져야 합니다"),
        "needNewAccount": MessageLookupByLibrary.simpleMessage("새 계정이 필요합니까?"),
        "newBroadcast": MessageLookupByLibrary.simpleMessage("새로운 방송"),
        "newGroup": MessageLookupByLibrary.simpleMessage("새 그룹"),
        "newPassword": MessageLookupByLibrary.simpleMessage("새 비밀번호"),
        "newPasswordMustHaveValue":
            MessageLookupByLibrary.simpleMessage("새 비밀번호는 값을 가져야 합니다"),
        "newUpdateIsAvailable":
            MessageLookupByLibrary.simpleMessage("새 업데이트가 있습니다"),
        "next": MessageLookupByLibrary.simpleMessage("다음"),
        "nickname": MessageLookupByLibrary.simpleMessage("닉네임"),
        "no": MessageLookupByLibrary.simpleMessage("아니요"),
        "noBio": MessageLookupByLibrary.simpleMessage("자기 소개 없음"),
        "noCodeHasBeenSendToYouToVerifyYourEmail":
            MessageLookupByLibrary.simpleMessage(
                "귀하의 이메일을 확인하기 위한 코드가 전송되지 않았습니다"),
        "noUpdatesAvailableNow":
            MessageLookupByLibrary.simpleMessage("현재 업데이트 없음"),
        "none": MessageLookupByLibrary.simpleMessage("없음"),
        "notAccepted": MessageLookupByLibrary.simpleMessage("미수락됨"),
        "notification": MessageLookupByLibrary.simpleMessage("알림"),
        "notificationDescription":
            MessageLookupByLibrary.simpleMessage("알림 설명"),
        "notificationTitle": MessageLookupByLibrary.simpleMessage("알림 제목"),
        "notificationsPage": MessageLookupByLibrary.simpleMessage("알림 페이지"),
        "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion":
            MessageLookupByLibrary.simpleMessage(
                "현재 읽기 전용 관리자로 로그인되었습니다. 이것은 테스트 버전이므로 수행한 모든 편집은 적용되지 않습니다."),
        "off": MessageLookupByLibrary.simpleMessage("끄기"),
        "offline": MessageLookupByLibrary.simpleMessage("오프라인"),
        "ok": MessageLookupByLibrary.simpleMessage("확인"),
        "oldPassword": MessageLookupByLibrary.simpleMessage("이전 비밀번호"),
        "on": MessageLookupByLibrary.simpleMessage("켜기"),
        "oneSeenMessage": MessageLookupByLibrary.simpleMessage("한 번 본 메시지"),
        "online": MessageLookupByLibrary.simpleMessage("온라인"),
        "orLoginWith": MessageLookupByLibrary.simpleMessage("또는 다음으로 로그인:"),
        "other": MessageLookupByLibrary.simpleMessage("기타"),
        "otherCategoryDescription": MessageLookupByLibrary.simpleMessage(
            "기타: 이 옵션은 위의 카테고리에 쉽게 포함되지 않는 위반 사항에 사용할 수 있습니다. 사용자가 추가 세부 정보를 제공할 수 있도록 텍스트 상자를 포함하는 것이 도움이 될 수 있습니다."),
        "otpCode": MessageLookupByLibrary.simpleMessage("OTP 코드"),
        "password": MessageLookupByLibrary.simpleMessage("비밀번호"),
        "passwordHasBeenChanged":
            MessageLookupByLibrary.simpleMessage("비밀번호가 변경되었습니다"),
        "passwordIsRequired": MessageLookupByLibrary.simpleMessage("비밀번호 필수"),
        "passwordMustHaveValue":
            MessageLookupByLibrary.simpleMessage("비밀번호는 값을 가져야 합니다"),
        "passwordNotMatch":
            MessageLookupByLibrary.simpleMessage("비밀번호가 일치하지 않습니다"),
        "peerUserDeviceOffline":
            MessageLookupByLibrary.simpleMessage("피어 사용자의 기기 오프라인 상태"),
        "peerUserInCallNow":
            MessageLookupByLibrary.simpleMessage("현재 통화 중인 사용자"),
        "pending": MessageLookupByLibrary.simpleMessage("보류 중"),
        "phone": MessageLookupByLibrary.simpleMessage("전화"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("개인 정보 보호 정책"),
        "privacyUrl": MessageLookupByLibrary.simpleMessage("개인 정보 보호 URL"),
        "profile": MessageLookupByLibrary.simpleMessage("프로필"),
        "promotedToAdminBy":
            MessageLookupByLibrary.simpleMessage("관리자로 승격한 사람:"),
        "public": MessageLookupByLibrary.simpleMessage("공개"),
        "read": MessageLookupByLibrary.simpleMessage("읽음"),
        "recentUpdate": MessageLookupByLibrary.simpleMessage("최근 업데이트"),
        "recentUpdates": MessageLookupByLibrary.simpleMessage("최근 업데이트"),
        "recording": MessageLookupByLibrary.simpleMessage("녹음 중..."),
        "register": MessageLookupByLibrary.simpleMessage("등록"),
        "registerMethod": MessageLookupByLibrary.simpleMessage("등록 방법"),
        "registerStatus": MessageLookupByLibrary.simpleMessage("등록 상태"),
        "rejected": MessageLookupByLibrary.simpleMessage("거부됨"),
        "repliedToYourSelf":
            MessageLookupByLibrary.simpleMessage("자기 자신에게 답장함"),
        "reply": MessageLookupByLibrary.simpleMessage("답장"),
        "replyToYourSelf": MessageLookupByLibrary.simpleMessage("자기 자신에게 답장"),
        "report": MessageLookupByLibrary.simpleMessage("신고"),
        "reportHasBeenSubmitted":
            MessageLookupByLibrary.simpleMessage("신고가 제출되었습니다"),
        "reportUser": MessageLookupByLibrary.simpleMessage("사용자 신고"),
        "reports": MessageLookupByLibrary.simpleMessage("리포트"),
        "resetPassword": MessageLookupByLibrary.simpleMessage("비밀번호 재설정"),
        "retry": MessageLookupByLibrary.simpleMessage("재시도"),
        "ring": MessageLookupByLibrary.simpleMessage("벨 울림"),
        "roomAlreadyInCall": MessageLookupByLibrary.simpleMessage("이미 통화 중인 방"),
        "roomCounter": MessageLookupByLibrary.simpleMessage("방 카운터"),
        "saveLogin": MessageLookupByLibrary.simpleMessage("로그인 정보 저장"),
        "search": MessageLookupByLibrary.simpleMessage("검색"),
        "seconds": MessageLookupByLibrary.simpleMessage("초"),
        "send": MessageLookupByLibrary.simpleMessage("보내기"),
        "sendCodeToMyEmail":
            MessageLookupByLibrary.simpleMessage("이메일로 코드 보내기"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("메시지 보내기"),
        "sessionEnd": MessageLookupByLibrary.simpleMessage("세션 종료"),
        "setMaxBroadcastMembers":
            MessageLookupByLibrary.simpleMessage("최대 방송 멤버 설정"),
        "setMaxGroupMembers":
            MessageLookupByLibrary.simpleMessage("최대 그룹 멤버 설정"),
        "setMaxMessageForwardAndShare":
            MessageLookupByLibrary.simpleMessage("최대 메시지 전달 및 공유 설정"),
        "setNewPrivacyPolicyUrl":
            MessageLookupByLibrary.simpleMessage("새 개인 정보 보호 정책 URL 설정"),
        "setToAdmin": MessageLookupByLibrary.simpleMessage("관리자로 설정"),
        "settings": MessageLookupByLibrary.simpleMessage("설정"),
        "share": MessageLookupByLibrary.simpleMessage("공유"),
        "shareMediaAndLocation":
            MessageLookupByLibrary.simpleMessage("미디어 및 위치 공유"),
        "shareYourStatus": MessageLookupByLibrary.simpleMessage("상태 공유"),
        "showHistory": MessageLookupByLibrary.simpleMessage("기록 보기"),
        "showMedia": MessageLookupByLibrary.simpleMessage("미디어 표시"),
        "soon": MessageLookupByLibrary.simpleMessage("곧"),
        "spamOrScamDescription": MessageLookupByLibrary.simpleMessage(
            "스팸 또는 사기: 이 옵션은 스팸 메시지, 무단 광고 메시지를 보내는 계정 또는 다른 사람을 사기치려고 하는 계정을 신고하는 데 사용됩니다."),
        "star": MessageLookupByLibrary.simpleMessage("별표"),
        "starMessage": MessageLookupByLibrary.simpleMessage("메시지에 별표"),
        "starredMessage": MessageLookupByLibrary.simpleMessage("별표 표시된 메시지"),
        "starredMessages": MessageLookupByLibrary.simpleMessage("스타된 메시지"),
        "startChat": MessageLookupByLibrary.simpleMessage("채팅 시작"),
        "startNewChatWithYou":
            MessageLookupByLibrary.simpleMessage("당신과 새로운 채팅 시작"),
        "status": MessageLookupByLibrary.simpleMessage("상태"),
        "storageAndData": MessageLookupByLibrary.simpleMessage("저장 및 데이터"),
        "stories": MessageLookupByLibrary.simpleMessage("스토리"),
        "storyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("스토리가 성공적으로 만들어졌습니다"),
        "success": MessageLookupByLibrary.simpleMessage("성공"),
        "successfullyDownloadedIn":
            MessageLookupByLibrary.simpleMessage("다음 위치에 성공적으로 다운로드됨"),
        "supportChatSoon": MessageLookupByLibrary.simpleMessage("지원 채팅 (곧)"),
        "tapADeviceToEditOrLogOut":
            MessageLookupByLibrary.simpleMessage("편집 또는 로그아웃하려면 기기를 탭하세요."),
        "tapForPhoto": MessageLookupByLibrary.simpleMessage("사진을 보려면 탭하세요"),
        "tapToSelectAnIcon":
            MessageLookupByLibrary.simpleMessage("아이콘 선택을 위해 탭하세요"),
        "tellAFriend": MessageLookupByLibrary.simpleMessage("친구에게 알리기"),
        "textFieldHint": MessageLookupByLibrary.simpleMessage("메시지 입력..."),
        "textMessages": MessageLookupByLibrary.simpleMessage("텍스트 메시지"),
        "thereIsFileHasSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage("허용된 크기보다 큰 파일이 있습니다"),
        "thereIsVideoSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage("허용된 크기보다 큰 비디오가 있습니다"),
        "timeout": MessageLookupByLibrary.simpleMessage("시간 초과"),
        "titleIsRequired": MessageLookupByLibrary.simpleMessage("제목은 필수 항목입니다"),
        "today": MessageLookupByLibrary.simpleMessage("오늘"),
        "total": MessageLookupByLibrary.simpleMessage("총"),
        "totalMessages": MessageLookupByLibrary.simpleMessage("총 메시지 수"),
        "totalRooms": MessageLookupByLibrary.simpleMessage("총 방 수"),
        "totalVisits": MessageLookupByLibrary.simpleMessage("총 방문수"),
        "typing": MessageLookupByLibrary.simpleMessage("입력 중..."),
        "unBlock": MessageLookupByLibrary.simpleMessage("차단 해제"),
        "unBlockUser": MessageLookupByLibrary.simpleMessage("사용자 차단 해제"),
        "unMute": MessageLookupByLibrary.simpleMessage("음소거 해제"),
        "unStar": MessageLookupByLibrary.simpleMessage("스타 해제"),
        "update": MessageLookupByLibrary.simpleMessage("업데이트"),
        "updateBroadcastTitle":
            MessageLookupByLibrary.simpleMessage("방송 제목 업데이트"),
        "updateFeedBackEmail":
            MessageLookupByLibrary.simpleMessage("피드백 이메일 업데이트"),
        "updateGroupDescription":
            MessageLookupByLibrary.simpleMessage("그룹 설명 업데이트"),
        "updateGroupDescriptionWillUpdateAllGroupMembers":
            MessageLookupByLibrary.simpleMessage(
                "그룹 설명을 업데이트하면 모든 그룹 멤버가 업데이트됩니다"),
        "updateGroupTitle": MessageLookupByLibrary.simpleMessage("그룹 제목 업데이트"),
        "updateImage": MessageLookupByLibrary.simpleMessage("이미지 업데이트"),
        "updateNickname": MessageLookupByLibrary.simpleMessage("닉네임 업데이트"),
        "updateTitle": MessageLookupByLibrary.simpleMessage("제목 업데이트"),
        "updateTitleTo": MessageLookupByLibrary.simpleMessage("제목 업데이트"),
        "updateYourBio": MessageLookupByLibrary.simpleMessage("자기 소개 업데이트"),
        "updateYourName": MessageLookupByLibrary.simpleMessage("이름 업데이트"),
        "updateYourPassword": MessageLookupByLibrary.simpleMessage("비밀번호 업데이트"),
        "updateYourProfile": MessageLookupByLibrary.simpleMessage("프로필 업데이트"),
        "updatedAt": MessageLookupByLibrary.simpleMessage("업데이트 시간"),
        "upgradeToAdmin": MessageLookupByLibrary.simpleMessage("관리자로 업그레이드"),
        "userAction": MessageLookupByLibrary.simpleMessage("사용자 작업"),
        "userAlreadyRegister":
            MessageLookupByLibrary.simpleMessage("사용자가 이미 등록되었습니다"),
        "userDeviceSessionEndDeviceDeleted":
            MessageLookupByLibrary.simpleMessage(
                "사용자 기기 세션이 종료되었으며 기기가 삭제되었습니다"),
        "userEmailNotFound":
            MessageLookupByLibrary.simpleMessage("사용자 이메일을 찾을 수 없습니다"),
        "userInfo": MessageLookupByLibrary.simpleMessage("사용자 정보"),
        "userPage": MessageLookupByLibrary.simpleMessage("사용자 페이지"),
        "userProfile": MessageLookupByLibrary.simpleMessage("사용자 프로필"),
        "userRegisterStatusNotAcceptedYet":
            MessageLookupByLibrary.simpleMessage("사용자 등록 상태가 아직 승인되지 않았습니다"),
        "users": MessageLookupByLibrary.simpleMessage("사용자"),
        "usersAddedSuccessfully":
            MessageLookupByLibrary.simpleMessage("사용자가 성공적으로 추가되었습니다"),
        "vMessageInfoTrans": MessageLookupByLibrary.simpleMessage("메시지 정보"),
        "vMessagesInfoTrans": MessageLookupByLibrary.simpleMessage("메시지 정보"),
        "verified": MessageLookupByLibrary.simpleMessage("인증됨"),
        "verifiedAt": MessageLookupByLibrary.simpleMessage("인증됨"),
        "video": MessageLookupByLibrary.simpleMessage("비디오"),
        "videoCallMessages": MessageLookupByLibrary.simpleMessage("비디오 통화 메시지"),
        "videoMessages": MessageLookupByLibrary.simpleMessage("비디오 메시지"),
        "visits": MessageLookupByLibrary.simpleMessage("방문"),
        "voiceCallMessage": MessageLookupByLibrary.simpleMessage("음성 통화 메시지"),
        "voiceCallMessages": MessageLookupByLibrary.simpleMessage("음성 통화 메시지"),
        "voiceMessages": MessageLookupByLibrary.simpleMessage("음성 메시지"),
        "wait2MinutesToSendMail":
            MessageLookupByLibrary.simpleMessage("이메일을 보내려면 2분을 기다려야 합니다"),
        "waitingList": MessageLookupByLibrary.simpleMessage("대기 목록"),
        "weHighRecommendToDownloadThisUpdate":
            MessageLookupByLibrary.simpleMessage("이 업데이트를 다운로드하는 것을 강력히 권장합니다"),
        "web": MessageLookupByLibrary.simpleMessage("웹"),
        "welcome": MessageLookupByLibrary.simpleMessage("환영합니다"),
        "whenUsingMobileData":
            MessageLookupByLibrary.simpleMessage("모바일 데이터 사용 시"),
        "whenUsingWifi": MessageLookupByLibrary.simpleMessage("Wi-Fi 사용 시"),
        "whileAuthCanFindYou":
            MessageLookupByLibrary.simpleMessage("인증 중에 귀하를 찾을 수 없습니다"),
        "windows": MessageLookupByLibrary.simpleMessage("Windows"),
        "writeACaption": MessageLookupByLibrary.simpleMessage("캡션 쓰기..."),
        "yes": MessageLookupByLibrary.simpleMessage("예"),
        "yesterday": MessageLookupByLibrary.simpleMessage("어제"),
        "you": MessageLookupByLibrary.simpleMessage("당신"),
        "youAreAboutToDeleteThisUserFromYourList":
            MessageLookupByLibrary.simpleMessage("이 사용자를 목록에서 삭제하려고 합니다"),
        "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList":
            MessageLookupByLibrary.simpleMessage(
                "계정을 삭제하려고 합니다. 계정은 사용자 목록에 다시 나타나지 않습니다"),
        "youAreAboutToDismissesToMember":
            MessageLookupByLibrary.simpleMessage("멤버를 내리려고 합니다"),
        "youAreAboutToKick":
            MessageLookupByLibrary.simpleMessage("멤버를 퇴출하려고 합니다"),
        "youAreAboutToUpgradeToAdmin":
            MessageLookupByLibrary.simpleMessage("관리자로 업그레이드하려고 합니다"),
        "youDontHaveAccess":
            MessageLookupByLibrary.simpleMessage("접근 권한이 없습니다"),
        "youInPublicSearch": MessageLookupByLibrary.simpleMessage("공개 검색에서 당신"),
        "youNotParticipantInThisGroup":
            MessageLookupByLibrary.simpleMessage("이 그룹에 참가자가 아닙니다"),
        "yourAccountBlocked":
            MessageLookupByLibrary.simpleMessage("귀하의 계정이 차단되었습니다"),
        "yourAccountDeleted":
            MessageLookupByLibrary.simpleMessage("귀하의 계정이 삭제되었습니다"),
        "yourAccountIsUnderReview":
            MessageLookupByLibrary.simpleMessage("귀하의 계정은 검토 중입니다"),
        "yourAreAboutToLogoutFromThisAccount":
            MessageLookupByLibrary.simpleMessage("이 계정에서 로그아웃하려고 합니다"),
        "yourLastSeen": MessageLookupByLibrary.simpleMessage("마지막으로 본 시간"),
        "yourLastSeenInChats":
            MessageLookupByLibrary.simpleMessage("채팅에서 마지막으로 본 시간"),
        "yourProfileAppearsInPublicSearchAndAddingForGroups":
            MessageLookupByLibrary.simpleMessage(
                "귀하의 프로필은 공개 검색 및 그룹 추가에 표시됩니다"),
        "yourSessionIsEndedPleaseLoginAgain":
            MessageLookupByLibrary.simpleMessage("세션이 종료되었습니다. 다시 로그인하세요!"),
        "yourStory": MessageLookupByLibrary.simpleMessage("당신의 스토리")
      };
}
