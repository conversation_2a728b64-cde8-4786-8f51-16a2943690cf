// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a id locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'id';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Dashboard": MessageLookupByLibrary.simpleMessage("Dasbor"),
        "about": MessageLookupByLibrary.simpleMessage("Tentang"),
        "aboutToBlockUserWithConsequences": MessageLookupByLibrary.simpleMessage(
            "Anda akan memblokir pengguna ini. Anda tidak dapat mengirimkan pesan kepadanya dan tidak dapat menambahkannya ke grup atau siaran!"),
        "accepted": MessageLookupByLibrary.simpleMessage("Diterima"),
        "account": MessageLookupByLibrary.simpleMessage("Akun"),
        "actions": MessageLookupByLibrary.simpleMessage("Tindakan"),
        "addMembers": MessageLookupByLibrary.simpleMessage("Tambahkan Anggota"),
        "addNewStory":
            MessageLookupByLibrary.simpleMessage("Tambahkan cerita baru"),
        "addParticipants":
            MessageLookupByLibrary.simpleMessage("Tambahkan Peserta"),
        "addedYouToNewBroadcast": MessageLookupByLibrary.simpleMessage(
            "Menambahkan Anda ke siaran baru"),
        "admin": MessageLookupByLibrary.simpleMessage("Admin"),
        "adminNotification":
            MessageLookupByLibrary.simpleMessage("Notifikasi Admin"),
        "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself":
            MessageLookupByLibrary.simpleMessage(
                "Semua data telah dicadangkan, Anda tidak perlu mengelola penyimpanan data sendiri! Jika Anda keluar dan masuk kembali, Anda akan melihat semua obrolan sama dengan versi web"),
        "allDeletedMessages":
            MessageLookupByLibrary.simpleMessage("Semua Pesan yang Dihapus"),
        "allowAds": MessageLookupByLibrary.simpleMessage("Izinkan Iklan"),
        "allowCalls": MessageLookupByLibrary.simpleMessage("Izinkan Panggilan"),
        "allowCreateBroadcast":
            MessageLookupByLibrary.simpleMessage("Izinkan Pembuatan Siaran"),
        "allowCreateGroups":
            MessageLookupByLibrary.simpleMessage("Izinkan Pembuatan Grup"),
        "allowDesktopLogin":
            MessageLookupByLibrary.simpleMessage("Izinkan Login Desktop"),
        "allowMobileLogin":
            MessageLookupByLibrary.simpleMessage("Izinkan Login di Ponsel"),
        "allowSendMedia":
            MessageLookupByLibrary.simpleMessage("Izinkan Pengiriman Media"),
        "allowWebLogin":
            MessageLookupByLibrary.simpleMessage("Izinkan Login di Web"),
        "alreadyHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Sudah memiliki akun?"),
        "android": MessageLookupByLibrary.simpleMessage("Android"),
        "appMembers": MessageLookupByLibrary.simpleMessage("Anggota Aplikasi"),
        "appleStoreAppUrl":
            MessageLookupByLibrary.simpleMessage("URL Aplikasi Apple Store"),
        "areYouSure":
            MessageLookupByLibrary.simpleMessage("Apakah Anda yakin?"),
        "areYouSureToBlock": MessageLookupByLibrary.simpleMessage(
            "Apakah Anda yakin ingin memblokir"),
        "areYouSureToLeaveThisGroupThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "Apakah Anda yakin ingin meninggalkan grup ini? Tindakan ini tidak dapat dibatalkan"),
        "areYouSureToPermitYourCopyThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "Apakah Anda yakin ingin mengizinkan salinan Anda? Tindakan ini tidak dapat dibatalkan"),
        "areYouSureToReportUserToAdmin": MessageLookupByLibrary.simpleMessage(
            "Apakah Anda yakin ingin melaporkan pengguna ini ke admin?"),
        "areYouSureToUnBlock": MessageLookupByLibrary.simpleMessage(
            "Apakah Anda yakin ingin membuka blokir"),
        "areYouWantToMakeVideoCall": MessageLookupByLibrary.simpleMessage(
            "Apakah Anda ingin membuat panggilan video?"),
        "areYouWantToMakeVoiceCall": MessageLookupByLibrary.simpleMessage(
            "Apakah Anda ingin membuat panggilan suara?"),
        "audio": MessageLookupByLibrary.simpleMessage("Audio"),
        "audioCall": MessageLookupByLibrary.simpleMessage("Panggilan Audio"),
        "back": MessageLookupByLibrary.simpleMessage("Kembali"),
        "banAt": MessageLookupByLibrary.simpleMessage("Diblokir pada"),
        "banTo": MessageLookupByLibrary.simpleMessage("Diblokir hingga"),
        "bio": MessageLookupByLibrary.simpleMessage("Biodata"),
        "block": MessageLookupByLibrary.simpleMessage("Blokir"),
        "blockUser": MessageLookupByLibrary.simpleMessage("Blokir pengguna"),
        "blocked": MessageLookupByLibrary.simpleMessage("Diblokir"),
        "blockedUsers":
            MessageLookupByLibrary.simpleMessage("Pengguna yang Diblokir"),
        "broadcast": MessageLookupByLibrary.simpleMessage("Siaran"),
        "broadcastInfo": MessageLookupByLibrary.simpleMessage("Info Siaran"),
        "broadcastMembers":
            MessageLookupByLibrary.simpleMessage("Anggota Siaran"),
        "broadcastName": MessageLookupByLibrary.simpleMessage("Nama siaran"),
        "broadcastParticipants":
            MessageLookupByLibrary.simpleMessage("Peserta Siaran"),
        "broadcastSettings":
            MessageLookupByLibrary.simpleMessage("Pengaturan Siaran"),
        "callNotAllowed":
            MessageLookupByLibrary.simpleMessage("Panggilan tidak diizinkan"),
        "callTimeoutInSeconds": MessageLookupByLibrary.simpleMessage(
            "Batas Waktu Panggilan (dalam detik)"),
        "calls": MessageLookupByLibrary.simpleMessage("Panggilan"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Batal"),
        "canceled": MessageLookupByLibrary.simpleMessage("Dibatalkan"),
        "changeSubject": MessageLookupByLibrary.simpleMessage("Ubah subjek"),
        "chat": MessageLookupByLibrary.simpleMessage("Obrolan"),
        "chats": MessageLookupByLibrary.simpleMessage("OBROLAN"),
        "checkForUpdates":
            MessageLookupByLibrary.simpleMessage("Periksa Pembaruan"),
        "chooseAtLestOneMember": MessageLookupByLibrary.simpleMessage(
            "Pilih setidaknya satu anggota"),
        "chooseHowAutomaticDownloadWorks": MessageLookupByLibrary.simpleMessage(
            "Pilih cara kerja unduhan otomatis"),
        "chooseRoom": MessageLookupByLibrary.simpleMessage("Pilih ruangan"),
        "clear": MessageLookupByLibrary.simpleMessage("Hapus"),
        "clearCallsConfirm":
            MessageLookupByLibrary.simpleMessage("Konfirmasi hapus panggilan"),
        "clearChat": MessageLookupByLibrary.simpleMessage("Bersihkan obrolan"),
        "clickToAddGroupDescription": MessageLookupByLibrary.simpleMessage(
            "Klik untuk menambahkan deskripsi grup"),
        "clickToSee":
            MessageLookupByLibrary.simpleMessage("Klik untuk melihat"),
        "clickToSeeAllUserCountries": MessageLookupByLibrary.simpleMessage(
            "Klik untuk melihat semua negara pengguna"),
        "clickToSeeAllUserDevicesDetails": MessageLookupByLibrary.simpleMessage(
            "Klik untuk melihat semua detail perangkat pengguna"),
        "clickToSeeAllUserInformations": MessageLookupByLibrary.simpleMessage(
            "Klik untuk melihat semua informasi pengguna"),
        "clickToSeeAllUserMessagesDetails":
            MessageLookupByLibrary.simpleMessage(
                "Klik untuk melihat semua detail pesan pengguna"),
        "clickToSeeAllUserReports": MessageLookupByLibrary.simpleMessage(
            "Klik untuk melihat semua laporan pengguna"),
        "clickToSeeAllUserRoomsDetails": MessageLookupByLibrary.simpleMessage(
            "Klik untuk melihat semua detail ruangan pengguna"),
        "close": MessageLookupByLibrary.simpleMessage("Tutup"),
        "codeHasBeenExpired":
            MessageLookupByLibrary.simpleMessage("Kode telah kedaluwarsa"),
        "codeMustEqualToSixNumbers": MessageLookupByLibrary.simpleMessage(
            "Kode harus sama dengan enam angka"),
        "configureYourAccountPrivacy": MessageLookupByLibrary.simpleMessage(
            "Konfigurasikan privasi akun Anda"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Konfirmasi kata sandi"),
        "confirmPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Konfirmasi kata sandi harus memiliki nilai"),
        "congregationsYourAccountHasBeenAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Selamat, akun Anda telah diterima"),
        "connecting": MessageLookupByLibrary.simpleMessage("Menghubungkan..."),
        "contactInfo": MessageLookupByLibrary.simpleMessage("Info Kontak"),
        "contactUs": MessageLookupByLibrary.simpleMessage("Hubungi Kami"),
        "copy": MessageLookupByLibrary.simpleMessage("Salin"),
        "countries": MessageLookupByLibrary.simpleMessage("Negara"),
        "country": MessageLookupByLibrary.simpleMessage("Negara"),
        "create": MessageLookupByLibrary.simpleMessage("Buat"),
        "createBroadcast": MessageLookupByLibrary.simpleMessage("Buat Siaran"),
        "createGroup": MessageLookupByLibrary.simpleMessage("Buat Grup"),
        "createMediaStory":
            MessageLookupByLibrary.simpleMessage("Buat Cerita Media"),
        "createStory": MessageLookupByLibrary.simpleMessage("Buat Cerita"),
        "createTextStory":
            MessageLookupByLibrary.simpleMessage("Buat Cerita Teks"),
        "createYourStory":
            MessageLookupByLibrary.simpleMessage("Buat cerita Anda"),
        "createdAt": MessageLookupByLibrary.simpleMessage("Dibuat pada"),
        "creator": MessageLookupByLibrary.simpleMessage("Pembuat"),
        "currentDevice":
            MessageLookupByLibrary.simpleMessage("Perangkat Saat Ini"),
        "dashboard": MessageLookupByLibrary.simpleMessage("Dasbor"),
        "dataPrivacy": MessageLookupByLibrary.simpleMessage("Privasi Data"),
        "delete": MessageLookupByLibrary.simpleMessage("Hapus"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("Hapus obrolan"),
        "deleteFromAll":
            MessageLookupByLibrary.simpleMessage("Hapus dari semua"),
        "deleteFromMe": MessageLookupByLibrary.simpleMessage("Hapus dari saya"),
        "deleteMember": MessageLookupByLibrary.simpleMessage("Hapus anggota"),
        "deleteMyAccount":
            MessageLookupByLibrary.simpleMessage("Hapus akun saya"),
        "deleteThisDeviceDesc": MessageLookupByLibrary.simpleMessage(
            "Menghapus perangkat ini berarti langsung keluar dari perangkat ini"),
        "deleteUser": MessageLookupByLibrary.simpleMessage("Hapus pengguna"),
        "deleteYouCopy":
            MessageLookupByLibrary.simpleMessage("Hapus salinan Anda"),
        "deleted": MessageLookupByLibrary.simpleMessage("Dihapus"),
        "deletedAt": MessageLookupByLibrary.simpleMessage("Dihapus pada"),
        "delivered": MessageLookupByLibrary.simpleMessage("Dikirim"),
        "description": MessageLookupByLibrary.simpleMessage("Deskripsi"),
        "descriptionIsRequired":
            MessageLookupByLibrary.simpleMessage("Deskripsi Diperlukan"),
        "desktopAndOtherDevices": MessageLookupByLibrary.simpleMessage(
            "Desktop, dan perangkat lainnya"),
        "deviceHasBeenLogoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage(
                "Perangkat telah keluar dari semua perangkat"),
        "deviceStatus":
            MessageLookupByLibrary.simpleMessage("Status Perangkat"),
        "devices": MessageLookupByLibrary.simpleMessage("Perangkat"),
        "directChat": MessageLookupByLibrary.simpleMessage("Obrolan Langsung"),
        "directRooms": MessageLookupByLibrary.simpleMessage("Ruangan Langsung"),
        "dismissedToMemberBy": MessageLookupByLibrary.simpleMessage(
            "Dikeluarkan menjadi anggota oleh"),
        "dismissesToMember": MessageLookupByLibrary.simpleMessage(
            "Mengeluarkan menjadi anggota"),
        "docs": MessageLookupByLibrary.simpleMessage("Dokumen"),
        "done": MessageLookupByLibrary.simpleMessage("Selesai"),
        "download": MessageLookupByLibrary.simpleMessage("Unduh"),
        "downloading": MessageLookupByLibrary.simpleMessage("Mengunduh..."),
        "edit": MessageLookupByLibrary.simpleMessage("Edit"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "emailMustBeValid":
            MessageLookupByLibrary.simpleMessage("Email harus valid"),
        "emailNotValid":
            MessageLookupByLibrary.simpleMessage("Email tidak valid"),
        "enterNameAndAddOptionalProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Masukkan nama Anda dan tambahkan foto profil opsional"),
        "error": MessageLookupByLibrary.simpleMessage("Kesalahan"),
        "exitGroup": MessageLookupByLibrary.simpleMessage("Keluar dari Grup"),
        "explainWhatHappens":
            MessageLookupByLibrary.simpleMessage("Jelaskan apa yang terjadi"),
        "feedBackEmail": MessageLookupByLibrary.simpleMessage("Email Masukan"),
        "fileHasBeenSavedTo":
            MessageLookupByLibrary.simpleMessage("Berkas telah disimpan ke"),
        "fileMessages": MessageLookupByLibrary.simpleMessage("Pesan Berkas"),
        "files": MessageLookupByLibrary.simpleMessage("Berkas"),
        "finished": MessageLookupByLibrary.simpleMessage("Selesai"),
        "forRequest": MessageLookupByLibrary.simpleMessage("Untuk permintaan"),
        "forgetPassword":
            MessageLookupByLibrary.simpleMessage("Lupa Kata Sandi"),
        "forgetPasswordExpireTime": MessageLookupByLibrary.simpleMessage(
            "Waktu Kadaluarsa Lupa Kata Sandi"),
        "forward": MessageLookupByLibrary.simpleMessage("Teruskan"),
        "fullName": MessageLookupByLibrary.simpleMessage("Nama Lengkap"),
        "gallery": MessageLookupByLibrary.simpleMessage("Galeri"),
        "globalSearch":
            MessageLookupByLibrary.simpleMessage("Pencarian Global"),
        "googlePlayAppUrl":
            MessageLookupByLibrary.simpleMessage("URL Aplikasi Google Play"),
        "group": MessageLookupByLibrary.simpleMessage("Grup"),
        "groupCreatedBy":
            MessageLookupByLibrary.simpleMessage("Grup dibuat oleh"),
        "groupDescription":
            MessageLookupByLibrary.simpleMessage("Deskripsi Grup"),
        "groupIcon": MessageLookupByLibrary.simpleMessage("Ikon Grup"),
        "groupInfo": MessageLookupByLibrary.simpleMessage("Info Grup"),
        "groupMembers": MessageLookupByLibrary.simpleMessage("Anggota Grup"),
        "groupName": MessageLookupByLibrary.simpleMessage("Nama grup"),
        "groupParticipants":
            MessageLookupByLibrary.simpleMessage("Peserta Grup"),
        "groupSettings":
            MessageLookupByLibrary.simpleMessage("Pengaturan Grup"),
        "groupWith": MessageLookupByLibrary.simpleMessage("Grup dengan"),
        "harassmentOrBullyingDescription": MessageLookupByLibrary.simpleMessage(
            "Pelecehan atau Penggertakan: Pilihan ini memungkinkan pengguna melaporkan individu yang menargetkan mereka atau orang lain dengan pesan pelecehan, ancaman, atau bentuk pelecehan lainnya."),
        "help": MessageLookupByLibrary.simpleMessage("Bantuan"),
        "hiIamUse":
            MessageLookupByLibrary.simpleMessage("Halo, saya menggunakan"),
        "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Jika opsi ini dinonaktifkan, pembuatan siaran chat akan diblokir"),
        "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Jika opsi ini dinonaktifkan, pembuatan grup chat akan diblokir"),
        "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Jika opsi ini dinonaktifkan, login atau registrasi desktop (Windows dan macOS) akan diblokir"),
        "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly":
            MessageLookupByLibrary.simpleMessage(
                "Jika opsi ini diaktifkan, iklan banner Google akan muncul dalam obrolan"),
        "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Jika opsi ini dinonaktifkan, pengiriman berkas chat, gambar, video, dan lokasi akan diblokir"),
        "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Jika opsi ini dinonaktifkan, login atau registrasi web akan diblokir"),
        "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed":
            MessageLookupByLibrary.simpleMessage(
                "Jika opsi ini diaktifkan, panggilan video dan suara akan diizinkan"),
        "image": MessageLookupByLibrary.simpleMessage("Gambar"),
        "imageMessages": MessageLookupByLibrary.simpleMessage("Pesan Gambar"),
        "images": MessageLookupByLibrary.simpleMessage("Gambar"),
        "inAppAlerts": MessageLookupByLibrary.simpleMessage(
            "Pemberitahuan dalam Aplikasi"),
        "inCall": MessageLookupByLibrary.simpleMessage("Dalam panggilan"),
        "inappropriateContentDescription": MessageLookupByLibrary.simpleMessage(
            "Konten yang Tidak Pantas: Pengguna dapat memilih opsi ini untuk melaporkan materi yang mengandung konten seksual eksplisit, ujaran kebencian, atau konten lain yang melanggar standar komunitas."),
        "info": MessageLookupByLibrary.simpleMessage("Info"),
        "infoMessages": MessageLookupByLibrary.simpleMessage("Pesan Informasi"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("Kode tidak valid"),
        "invalidLoginData":
            MessageLookupByLibrary.simpleMessage("Data masuk tidak valid"),
        "ios": MessageLookupByLibrary.simpleMessage("iOS"),
        "joinedAt": MessageLookupByLibrary.simpleMessage("Bergabung pada"),
        "joinedBy": MessageLookupByLibrary.simpleMessage("Bergabung oleh"),
        "kickMember": MessageLookupByLibrary.simpleMessage("Keluarkan anggota"),
        "kickedBy": MessageLookupByLibrary.simpleMessage("Dikeluarkan oleh"),
        "language": MessageLookupByLibrary.simpleMessage("Bahasa"),
        "lastActiveFrom":
            MessageLookupByLibrary.simpleMessage("Terakhir aktif dari"),
        "leaveGroup": MessageLookupByLibrary.simpleMessage("Tinggalkan grup"),
        "leaveGroupAndDeleteYourMessageCopy":
            MessageLookupByLibrary.simpleMessage(
                "Tinggalkan grup dan hapus salinan pesan Anda"),
        "leftTheGroup":
            MessageLookupByLibrary.simpleMessage("Meninggalkan grup"),
        "linkADeviceSoon":
            MessageLookupByLibrary.simpleMessage("Tautkan Perangkat (Segera)"),
        "linkByQrCode":
            MessageLookupByLibrary.simpleMessage("Tautkan dengan Kode QR"),
        "linkedDevices":
            MessageLookupByLibrary.simpleMessage("Perangkat Tersambung"),
        "links": MessageLookupByLibrary.simpleMessage("Tautan"),
        "loading": MessageLookupByLibrary.simpleMessage("Memuat..."),
        "location": MessageLookupByLibrary.simpleMessage("Lokasi"),
        "locationMessages":
            MessageLookupByLibrary.simpleMessage("Pesan Lokasi"),
        "logOut": MessageLookupByLibrary.simpleMessage("Keluar"),
        "login": MessageLookupByLibrary.simpleMessage("Masuk"),
        "loginAgain": MessageLookupByLibrary.simpleMessage("Masuk lagi!"),
        "loginNowAllowedNowPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Saat ini login tidak diizinkan. Silakan coba lagi nanti."),
        "logoutFromAllDevices": MessageLookupByLibrary.simpleMessage(
            "Keluar dari semua perangkat?"),
        "macOs": MessageLookupByLibrary.simpleMessage("macOS"),
        "makeCall": MessageLookupByLibrary.simpleMessage("Buat panggilan"),
        "media": MessageLookupByLibrary.simpleMessage("Media"),
        "mediaLinksAndDocs":
            MessageLookupByLibrary.simpleMessage("Media, Tautan, dan Dokumen"),
        "member": MessageLookupByLibrary.simpleMessage("Anggota"),
        "members": MessageLookupByLibrary.simpleMessage("Anggota"),
        "messageCounter":
            MessageLookupByLibrary.simpleMessage("Penghitung Pesan"),
        "messageHasBeenDeleted":
            MessageLookupByLibrary.simpleMessage("Pesan telah dihapus"),
        "messageHasBeenViewed":
            MessageLookupByLibrary.simpleMessage("Pesan telah dilihat"),
        "messageInfo": MessageLookupByLibrary.simpleMessage("Info Pesan"),
        "messages": MessageLookupByLibrary.simpleMessage("Pesan"),
        "microphoneAndCameraPermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone and camera permission must be accepted"),
        "microphonePermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone permission must be accepted"),
        "minutes": MessageLookupByLibrary.simpleMessage("Menit"),
        "more": MessageLookupByLibrary.simpleMessage("Lebih banyak"),
        "mute": MessageLookupByLibrary.simpleMessage("Bisukan"),
        "muteNotifications":
            MessageLookupByLibrary.simpleMessage("Bisukan pemberitahuan"),
        "myPrivacy": MessageLookupByLibrary.simpleMessage("Privasi Saya"),
        "name": MessageLookupByLibrary.simpleMessage("Nama"),
        "nameMustHaveValue":
            MessageLookupByLibrary.simpleMessage("Nama harus memiliki nilai"),
        "needNewAccount":
            MessageLookupByLibrary.simpleMessage("Butuh akun baru?"),
        "newBroadcast": MessageLookupByLibrary.simpleMessage("Siaran Baru"),
        "newGroup": MessageLookupByLibrary.simpleMessage("Grup Baru"),
        "newPassword": MessageLookupByLibrary.simpleMessage("Kata sandi baru"),
        "newPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Kata sandi baru harus memiliki nilai"),
        "newUpdateIsAvailable":
            MessageLookupByLibrary.simpleMessage("Pembaruan baru tersedia"),
        "next": MessageLookupByLibrary.simpleMessage("Selanjutnya"),
        "nickname": MessageLookupByLibrary.simpleMessage("Nama panggilan"),
        "no": MessageLookupByLibrary.simpleMessage("Tidak"),
        "noBio": MessageLookupByLibrary.simpleMessage("Tidak ada biodata"),
        "noCodeHasBeenSendToYouToVerifyYourEmail":
            MessageLookupByLibrary.simpleMessage(
                "Tidak ada kode yang dikirimkan kepada Anda untuk memverifikasi email Anda"),
        "noUpdatesAvailableNow": MessageLookupByLibrary.simpleMessage(
            "Tidak ada pembaruan yang tersedia saat ini"),
        "none": MessageLookupByLibrary.simpleMessage("Tidak ada"),
        "notAccepted": MessageLookupByLibrary.simpleMessage("Tidak Diterima"),
        "notification": MessageLookupByLibrary.simpleMessage("Pemberitahuan"),
        "notificationDescription":
            MessageLookupByLibrary.simpleMessage("Deskripsi Pemberitahuan"),
        "notificationTitle":
            MessageLookupByLibrary.simpleMessage("Judul Pemberitahuan"),
        "notificationsPage":
            MessageLookupByLibrary.simpleMessage("Halaman Pemberitahuan"),
        "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion":
            MessageLookupByLibrary.simpleMessage(
                "Sekarang Anda masuk sebagai admin hanya baca. Semua perubahan yang Anda lakukan tidak akan diterapkan karena ini adalah versi uji coba."),
        "off": MessageLookupByLibrary.simpleMessage("Mati"),
        "offline": MessageLookupByLibrary.simpleMessage("Offline"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "oldPassword": MessageLookupByLibrary.simpleMessage("Kata sandi lama"),
        "on": MessageLookupByLibrary.simpleMessage("Hidup"),
        "oneSeenMessage":
            MessageLookupByLibrary.simpleMessage("Satu pesan dilihat"),
        "online": MessageLookupByLibrary.simpleMessage("Online"),
        "orLoginWith":
            MessageLookupByLibrary.simpleMessage("atau masuk dengan"),
        "other": MessageLookupByLibrary.simpleMessage("Lainnya"),
        "otherCategoryDescription": MessageLookupByLibrary.simpleMessage(
            "Lainnya: Kategori ini dapat digunakan untuk pelanggaran yang tidak mudah dimasukkan ke dalam kategori di atas. Mungkin berguna untuk menyertakan kotak teks agar pengguna dapat memberikan detail tambahan."),
        "otpCode": MessageLookupByLibrary.simpleMessage("Kode OTP"),
        "password": MessageLookupByLibrary.simpleMessage("Kata sandi"),
        "passwordHasBeenChanged":
            MessageLookupByLibrary.simpleMessage("Kata sandi telah diubah"),
        "passwordIsRequired":
            MessageLookupByLibrary.simpleMessage("Kata Sandi Diperlukan"),
        "passwordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Kata sandi harus memiliki nilai"),
        "passwordNotMatch":
            MessageLookupByLibrary.simpleMessage("Kata sandi tidak cocok"),
        "peerUserDeviceOffline": MessageLookupByLibrary.simpleMessage(
            "Perangkat pengguna lainnya offline"),
        "peerUserInCallNow": MessageLookupByLibrary.simpleMessage(
            "Pengguna dalam panggilan sekarang"),
        "pending": MessageLookupByLibrary.simpleMessage("Tertunda"),
        "phone": MessageLookupByLibrary.simpleMessage("Telepon"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Kebijakan Privasi"),
        "privacyUrl": MessageLookupByLibrary.simpleMessage("URL Privasi"),
        "profile": MessageLookupByLibrary.simpleMessage("Profil"),
        "promotedToAdminBy": MessageLookupByLibrary.simpleMessage(
            "Dipromosikan menjadi admin oleh"),
        "public": MessageLookupByLibrary.simpleMessage("Publik"),
        "read": MessageLookupByLibrary.simpleMessage("Dibaca"),
        "recentUpdate":
            MessageLookupByLibrary.simpleMessage("Pembaruan terbaru"),
        "recentUpdates":
            MessageLookupByLibrary.simpleMessage("Pembaruan Terbaru"),
        "recording": MessageLookupByLibrary.simpleMessage("Merekam..."),
        "register": MessageLookupByLibrary.simpleMessage("Daftar"),
        "registerMethod":
            MessageLookupByLibrary.simpleMessage("Metode Registrasi"),
        "registerStatus":
            MessageLookupByLibrary.simpleMessage("Status Registrasi"),
        "rejected": MessageLookupByLibrary.simpleMessage("Ditolak"),
        "repliedToYourSelf": MessageLookupByLibrary.simpleMessage(
            "Telah dibalas ke diri Anda sendiri"),
        "reply": MessageLookupByLibrary.simpleMessage("Balas"),
        "replyToYourSelf":
            MessageLookupByLibrary.simpleMessage("Balas ke diri Anda sendiri"),
        "report": MessageLookupByLibrary.simpleMessage("Laporkan"),
        "reportHasBeenSubmitted":
            MessageLookupByLibrary.simpleMessage("Laporan Anda telah diajukan"),
        "reportUser": MessageLookupByLibrary.simpleMessage("Laporkan pengguna"),
        "reports": MessageLookupByLibrary.simpleMessage("Laporan"),
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("Atur ulang kata sandi"),
        "retry": MessageLookupByLibrary.simpleMessage("Coba lagi"),
        "ring": MessageLookupByLibrary.simpleMessage("Berdering"),
        "roomAlreadyInCall":
            MessageLookupByLibrary.simpleMessage("Ruang sudah dalam panggilan"),
        "roomCounter": MessageLookupByLibrary.simpleMessage("Penghitung Ruang"),
        "saveLogin": MessageLookupByLibrary.simpleMessage("Simpan Login"),
        "search": MessageLookupByLibrary.simpleMessage("Cari"),
        "seconds": MessageLookupByLibrary.simpleMessage("detik"),
        "send": MessageLookupByLibrary.simpleMessage("Kirim"),
        "sendCodeToMyEmail":
            MessageLookupByLibrary.simpleMessage("Kirim kode ke email saya"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Kirim pesan"),
        "sessionEnd": MessageLookupByLibrary.simpleMessage("Sesi berakhir"),
        "setMaxBroadcastMembers": MessageLookupByLibrary.simpleMessage(
            "Atur Maksimum Anggota Siaran"),
        "setMaxGroupMembers":
            MessageLookupByLibrary.simpleMessage("Atur Maksimum Anggota Grup"),
        "setMaxMessageForwardAndShare": MessageLookupByLibrary.simpleMessage(
            "Atur Maksimum Pesan yang Dapat Diteruskan dan Dibagikan"),
        "setNewPrivacyPolicyUrl": MessageLookupByLibrary.simpleMessage(
            "Atur URL Kebijakan Privasi Baru"),
        "setToAdmin":
            MessageLookupByLibrary.simpleMessage("Atur sebagai admin"),
        "settings": MessageLookupByLibrary.simpleMessage("Pengaturan"),
        "share": MessageLookupByLibrary.simpleMessage("Bagikan"),
        "shareMediaAndLocation":
            MessageLookupByLibrary.simpleMessage("Bagikan media dan lokasi"),
        "shareYourStatus":
            MessageLookupByLibrary.simpleMessage("Bagikan status Anda"),
        "showHistory":
            MessageLookupByLibrary.simpleMessage("Tampilkan riwayat"),
        "showMedia": MessageLookupByLibrary.simpleMessage("Tampilkan media"),
        "soon": MessageLookupByLibrary.simpleMessage("Segera"),
        "spamOrScamDescription": MessageLookupByLibrary.simpleMessage(
            "Spam atau Penipuan: Pilihan ini untuk melaporkan akun yang mengirimkan pesan spam, iklan yang tidak diminta, atau berusaha menipu orang lain."),
        "star": MessageLookupByLibrary.simpleMessage("Bintang"),
        "starMessage": MessageLookupByLibrary.simpleMessage("Bintang pesan"),
        "starredMessage":
            MessageLookupByLibrary.simpleMessage("Pesan yang Dibintangi"),
        "starredMessages":
            MessageLookupByLibrary.simpleMessage("Pesan yang Dibintangi"),
        "startChat": MessageLookupByLibrary.simpleMessage("Mulai obrolan"),
        "startNewChatWithYou": MessageLookupByLibrary.simpleMessage(
            "Mulai obrolan baru dengan Anda"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "storageAndData":
            MessageLookupByLibrary.simpleMessage("Penyimpanan dan Data"),
        "stories": MessageLookupByLibrary.simpleMessage("Cerita"),
        "storyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Cerita Berhasil Dibuat"),
        "success": MessageLookupByLibrary.simpleMessage("Berhasil"),
        "successfullyDownloadedIn":
            MessageLookupByLibrary.simpleMessage("Berhasil diunduh di"),
        "supportChatSoon":
            MessageLookupByLibrary.simpleMessage("Chat Dukungan (Segera)"),
        "tapADeviceToEditOrLogOut": MessageLookupByLibrary.simpleMessage(
            "Ketuk perangkat untuk mengedit atau keluar."),
        "tapForPhoto": MessageLookupByLibrary.simpleMessage("Ketuk untuk foto"),
        "tapToSelectAnIcon":
            MessageLookupByLibrary.simpleMessage("Ketuk untuk memilih ikon"),
        "tellAFriend": MessageLookupByLibrary.simpleMessage("Beritahu teman"),
        "textFieldHint": MessageLookupByLibrary.simpleMessage("Ketik pesan..."),
        "textMessages": MessageLookupByLibrary.simpleMessage("Pesan Teks"),
        "thereIsFileHasSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "Ada berkas dengan ukuran yang lebih besar dari yang diizinkan"),
        "thereIsVideoSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "Ada ukuran video yang lebih besar dari yang diizinkan"),
        "timeout": MessageLookupByLibrary.simpleMessage("Waktu habis"),
        "titleIsRequired":
            MessageLookupByLibrary.simpleMessage("Judul diperlukan"),
        "today": MessageLookupByLibrary.simpleMessage("Hari ini"),
        "total": MessageLookupByLibrary.simpleMessage("Total"),
        "totalMessages": MessageLookupByLibrary.simpleMessage("Total Pesan"),
        "totalRooms": MessageLookupByLibrary.simpleMessage("Total Ruangan"),
        "totalVisits": MessageLookupByLibrary.simpleMessage("Total Kunjungan"),
        "typing": MessageLookupByLibrary.simpleMessage("Mengetik..."),
        "unBlock": MessageLookupByLibrary.simpleMessage("Buka Blokir"),
        "unBlockUser":
            MessageLookupByLibrary.simpleMessage("Buka blokir pengguna"),
        "unMute": MessageLookupByLibrary.simpleMessage("Batalkan bisu"),
        "unStar": MessageLookupByLibrary.simpleMessage("Batal bintang"),
        "update": MessageLookupByLibrary.simpleMessage("Perbarui"),
        "updateBroadcastTitle":
            MessageLookupByLibrary.simpleMessage("Perbarui judul siaran"),
        "updateFeedBackEmail":
            MessageLookupByLibrary.simpleMessage("Perbarui Email Masukan"),
        "updateGroupDescription":
            MessageLookupByLibrary.simpleMessage("Perbarui deskripsi grup"),
        "updateGroupDescriptionWillUpdateAllGroupMembers":
            MessageLookupByLibrary.simpleMessage(
                "Perbarui deskripsi grup akan memperbarui semua anggota grup"),
        "updateGroupTitle":
            MessageLookupByLibrary.simpleMessage("Perbarui judul grup"),
        "updateImage": MessageLookupByLibrary.simpleMessage("Perbarui gambar"),
        "updateNickname":
            MessageLookupByLibrary.simpleMessage("Perbarui nama panggilan"),
        "updateTitle": MessageLookupByLibrary.simpleMessage("Perbarui judul"),
        "updateTitleTo":
            MessageLookupByLibrary.simpleMessage("Perbarui judul menjadi"),
        "updateYourBio":
            MessageLookupByLibrary.simpleMessage("Perbarui biodata Anda"),
        "updateYourName":
            MessageLookupByLibrary.simpleMessage("Perbarui nama Anda"),
        "updateYourPassword":
            MessageLookupByLibrary.simpleMessage("Perbarui kata sandi Anda"),
        "updateYourProfile":
            MessageLookupByLibrary.simpleMessage("Perbarui profil Anda"),
        "updatedAt": MessageLookupByLibrary.simpleMessage("Diperbarui pada"),
        "upgradeToAdmin":
            MessageLookupByLibrary.simpleMessage("Promosikan ke admin"),
        "userAction": MessageLookupByLibrary.simpleMessage("Tindakan Pengguna"),
        "userAlreadyRegister":
            MessageLookupByLibrary.simpleMessage("Pengguna sudah terdaftar"),
        "userDeviceSessionEndDeviceDeleted":
            MessageLookupByLibrary.simpleMessage(
                "Sesi perangkat pengguna berakhir perangkat dihapus"),
        "userEmailNotFound": MessageLookupByLibrary.simpleMessage(
            "Email pengguna tidak ditemukan"),
        "userInfo": MessageLookupByLibrary.simpleMessage("Informasi Pengguna"),
        "userPage": MessageLookupByLibrary.simpleMessage("Halaman pengguna"),
        "userProfile": MessageLookupByLibrary.simpleMessage("Profil Pengguna"),
        "userRegisterStatusNotAcceptedYet":
            MessageLookupByLibrary.simpleMessage(
                "Status pendaftaran pengguna belum diterima"),
        "users": MessageLookupByLibrary.simpleMessage("Pengguna"),
        "usersAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Pengguna berhasil ditambahkan"),
        "vMessageInfoTrans": MessageLookupByLibrary.simpleMessage("Info Pesan"),
        "vMessagesInfoTrans":
            MessageLookupByLibrary.simpleMessage("Info Pesan"),
        "verified": MessageLookupByLibrary.simpleMessage("Terverifikasi"),
        "verifiedAt":
            MessageLookupByLibrary.simpleMessage("Terverifikasi pada"),
        "video": MessageLookupByLibrary.simpleMessage("Video"),
        "videoCallMessages":
            MessageLookupByLibrary.simpleMessage("Pesan Panggilan Video"),
        "videoMessages": MessageLookupByLibrary.simpleMessage("Pesan Video"),
        "visits": MessageLookupByLibrary.simpleMessage("Kunjungan"),
        "voiceCallMessage":
            MessageLookupByLibrary.simpleMessage("Pesan Panggilan Suara"),
        "voiceCallMessages":
            MessageLookupByLibrary.simpleMessage("Pesan Panggilan Suara"),
        "voiceMessages": MessageLookupByLibrary.simpleMessage("Pesan Suara"),
        "wait2MinutesToSendMail": MessageLookupByLibrary.simpleMessage(
            "Tunggu 2 menit untuk mengirim email"),
        "waitingList": MessageLookupByLibrary.simpleMessage("Daftar Tunggu"),
        "weHighRecommendToDownloadThisUpdate":
            MessageLookupByLibrary.simpleMessage(
                "Kami sangat merekomendasikan untuk mengunduh pembaruan ini"),
        "web": MessageLookupByLibrary.simpleMessage("Web"),
        "welcome": MessageLookupByLibrary.simpleMessage("Selamat datang"),
        "whenUsingMobileData": MessageLookupByLibrary.simpleMessage(
            "Saat menggunakan data seluler"),
        "whenUsingWifi":
            MessageLookupByLibrary.simpleMessage("Saat menggunakan Wi-Fi"),
        "whileAuthCanFindYou": MessageLookupByLibrary.simpleMessage(
            "Saat otentikasi tidak dapat menemukan Anda"),
        "windows": MessageLookupByLibrary.simpleMessage("Windows"),
        "writeACaption":
            MessageLookupByLibrary.simpleMessage("Tulis keterangan..."),
        "yes": MessageLookupByLibrary.simpleMessage("Ya"),
        "yesterday": MessageLookupByLibrary.simpleMessage("Kemarin"),
        "you": MessageLookupByLibrary.simpleMessage("Anda"),
        "youAreAboutToDeleteThisUserFromYourList":
            MessageLookupByLibrary.simpleMessage(
                "Anda akan menghapus pengguna ini dari daftar Anda"),
        "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList":
            MessageLookupByLibrary.simpleMessage(
                "Anda akan menghapus akun Anda. Akun Anda tidak akan muncul lagi dalam daftar pengguna"),
        "youAreAboutToDismissesToMember": MessageLookupByLibrary.simpleMessage(
            "Anda akan mengeluarkan menjadi anggota"),
        "youAreAboutToKick":
            MessageLookupByLibrary.simpleMessage("Anda akan mengeluarkan"),
        "youAreAboutToUpgradeToAdmin": MessageLookupByLibrary.simpleMessage(
            "Anda akan mengupgrade menjadi admin"),
        "youDontHaveAccess":
            MessageLookupByLibrary.simpleMessage("Anda tidak memiliki akses"),
        "youInPublicSearch":
            MessageLookupByLibrary.simpleMessage("Anda dalam pencarian publik"),
        "youNotParticipantInThisGroup": MessageLookupByLibrary.simpleMessage(
            "Anda bukan peserta dalam grup ini"),
        "yourAccountBlocked":
            MessageLookupByLibrary.simpleMessage("Akun Anda telah diblokir"),
        "yourAccountDeleted":
            MessageLookupByLibrary.simpleMessage("Akun Anda telah dihapus"),
        "yourAccountIsUnderReview": MessageLookupByLibrary.simpleMessage(
            "Akun Anda sedang dalam proses peninjauan"),
        "yourAreAboutToLogoutFromThisAccount":
            MessageLookupByLibrary.simpleMessage(
                "Anda akan keluar dari akun ini"),
        "yourLastSeen":
            MessageLookupByLibrary.simpleMessage("Terakhir dilihat"),
        "yourLastSeenInChats":
            MessageLookupByLibrary.simpleMessage("Terakhir dilihat di chat"),
        "yourProfileAppearsInPublicSearchAndAddingForGroups":
            MessageLookupByLibrary.simpleMessage(
                "Profil Anda muncul di pencarian publik dan penambahan untuk grup"),
        "yourSessionIsEndedPleaseLoginAgain":
            MessageLookupByLibrary.simpleMessage(
                "Sesi Anda telah berakhir, silakan masuk kembali!"),
        "yourStory": MessageLookupByLibrary.simpleMessage("Cerita Anda")
      };
}
