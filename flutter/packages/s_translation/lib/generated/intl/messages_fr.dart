// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a fr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'fr';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Dashboard": MessageLookupByLibrary.simpleMessage("Tableau de bord"),
        "about": MessageLookupByLibrary.simpleMessage("À propos"),
        "aboutToBlockUserWithConsequences": MessageLookupByLibrary.simpleMessage(
            "Vous êtes sur le point de bloquer cet utilisateur.Vous ne pouvez pas lui envoyer de discussions et ne pouvez pas l\'ajouter à des groupes ou diffuser!"),
        "accepted": MessageLookupByLibrary.simpleMessage("Accepté"),
        "account": MessageLookupByLibrary.simpleMessage("Compte"),
        "actions": MessageLookupByLibrary.simpleMessage("Actes"),
        "addMembers":
            MessageLookupByLibrary.simpleMessage("Ajouter des membres"),
        "addNewStory": MessageLookupByLibrary.simpleMessage(
            "Ajouter une nouvelle histoire"),
        "addParticipants":
            MessageLookupByLibrary.simpleMessage("Ajouter les participants"),
        "addedYouToNewBroadcast": MessageLookupByLibrary.simpleMessage(
            "Vous a ajouté à une nouvelle diffusion"),
        "admin": MessageLookupByLibrary.simpleMessage("Administrer"),
        "adminNotification":
            MessageLookupByLibrary.simpleMessage("Notification administrative"),
        "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself":
            MessageLookupByLibrary.simpleMessage(
                "Toutes les données ont été une sauvegarde, vous ne voulez pas avoir besoin de gérer les données de vous-même!Si vous vous déconnectez et vous vous connectez à nouveau, vous verrez tous les chats identiques pour la version Web"),
        "allDeletedMessages":
            MessageLookupByLibrary.simpleMessage("Tous les messages supprimés"),
        "allowAds":
            MessageLookupByLibrary.simpleMessage("Autoriser les annonces"),
        "allowCalls":
            MessageLookupByLibrary.simpleMessage("Autoriser les appels"),
        "allowCreateBroadcast": MessageLookupByLibrary.simpleMessage(
            "Autoriser la création de diffusion"),
        "allowCreateGroups": MessageLookupByLibrary.simpleMessage(
            "Autoriser la création de groupes"),
        "allowDesktopLogin": MessageLookupByLibrary.simpleMessage(
            "Autoriser la connexion de bureau"),
        "allowMobileLogin": MessageLookupByLibrary.simpleMessage(
            "Autoriser la connexion mobile"),
        "allowSendMedia": MessageLookupByLibrary.simpleMessage(
            "Autoriser l\'envoi de médias"),
        "allowWebLogin":
            MessageLookupByLibrary.simpleMessage("Autoriser la connexion Web"),
        "alreadyHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Vous avez déjà un compte?"),
        "android": MessageLookupByLibrary.simpleMessage("Androïde"),
        "appMembers":
            MessageLookupByLibrary.simpleMessage("Membres de l\'application"),
        "appleStoreAppUrl": MessageLookupByLibrary.simpleMessage(
            "URL de l\'application Apple Store"),
        "areYouSure": MessageLookupByLibrary.simpleMessage("Es-tu sûr?"),
        "areYouSureToBlock":
            MessageLookupByLibrary.simpleMessage("Êtes-vous sûr de bloquer"),
        "areYouSureToLeaveThisGroupThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "Êtes-vous sûr de quitter ce groupe?Cette action ne peut pas défaire"),
        "areYouSureToPermitYourCopyThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "Êtes-vous sûr d\'autoriser votre copie?Cette action ne peut pas défaire"),
        "areYouSureToReportUserToAdmin": MessageLookupByLibrary.simpleMessage(
            "Êtes-vous sûr de soumettre un rapport sur cet utilisateur à l\'administrateur?"),
        "areYouSureToUnBlock":
            MessageLookupByLibrary.simpleMessage("Êtes-vous sûr de débloquer"),
        "areYouWantToMakeVideoCall": MessageLookupByLibrary.simpleMessage(
            "Voulez-vous passer des appels vidéo?"),
        "areYouWantToMakeVoiceCall":
            MessageLookupByLibrary.simpleMessage("Voulez-vous passer la voix?"),
        "audio": MessageLookupByLibrary.simpleMessage("Audio"),
        "audioCall": MessageLookupByLibrary.simpleMessage("Appel audio"),
        "back": MessageLookupByLibrary.simpleMessage("Dos"),
        "banAt": MessageLookupByLibrary.simpleMessage("Interdire"),
        "banTo": MessageLookupByLibrary.simpleMessage("Banni jusqu\'à"),
        "bio": MessageLookupByLibrary.simpleMessage("Bio"),
        "block": MessageLookupByLibrary.simpleMessage("Bloc"),
        "blockUser":
            MessageLookupByLibrary.simpleMessage("Bloquer l\'utilisateur"),
        "blocked": MessageLookupByLibrary.simpleMessage("Bloquée"),
        "blockedUsers":
            MessageLookupByLibrary.simpleMessage("Utilisateurs bloqués"),
        "broadcast": MessageLookupByLibrary.simpleMessage("Diffuser"),
        "broadcastInfo": MessageLookupByLibrary.simpleMessage(
            "Informations sur la diffusion"),
        "broadcastMembers":
            MessageLookupByLibrary.simpleMessage("Membres de la diffusion"),
        "broadcastName":
            MessageLookupByLibrary.simpleMessage("Nom de diffusion"),
        "broadcastParticipants":
            MessageLookupByLibrary.simpleMessage("Diffusion des participants"),
        "broadcastSettings":
            MessageLookupByLibrary.simpleMessage("Paramètres de diffusion"),
        "callNotAllowed":
            MessageLookupByLibrary.simpleMessage("Appeler non autorisé"),
        "callTimeoutInSeconds": MessageLookupByLibrary.simpleMessage(
            "Appelez le délai d\'attente en quelques secondes"),
        "calls": MessageLookupByLibrary.simpleMessage("Appels"),
        "camera": MessageLookupByLibrary.simpleMessage("Camera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Annuler"),
        "canceled": MessageLookupByLibrary.simpleMessage("Annulé"),
        "changeSubject":
            MessageLookupByLibrary.simpleMessage("Changer de sujet"),
        "chat": MessageLookupByLibrary.simpleMessage("Chat"),
        "chats": MessageLookupByLibrary.simpleMessage("CHATS"),
        "checkForUpdates":
            MessageLookupByLibrary.simpleMessage("Vérifier les mises à jour"),
        "chooseAtLestOneMember": MessageLookupByLibrary.simpleMessage(
            "Choisissez au moins un membre"),
        "chooseHowAutomaticDownloadWorks": MessageLookupByLibrary.simpleMessage(
            "Choisissez le fonctionnement du téléchargement automatique"),
        "chooseRoom":
            MessageLookupByLibrary.simpleMessage("Choisir la chambre"),
        "clear": MessageLookupByLibrary.simpleMessage("effacée"),
        "clearCallsConfirm": MessageLookupByLibrary.simpleMessage(
            "Effacer les appels confirmer"),
        "clearChat": MessageLookupByLibrary.simpleMessage("Chat claire"),
        "clickToAddGroupDescription": MessageLookupByLibrary.simpleMessage(
            "Cliquez pour ajouter la description du groupe"),
        "clickToSee": MessageLookupByLibrary.simpleMessage("Cliquez pour voir"),
        "clickToSeeAllUserCountries": MessageLookupByLibrary.simpleMessage(
            "Cliquez pour voir tous les pays des utilisateurs"),
        "clickToSeeAllUserDevicesDetails": MessageLookupByLibrary.simpleMessage(
            "Cliquez pour voir tous les détails des appareils de l\'utilisateur"),
        "clickToSeeAllUserInformations": MessageLookupByLibrary.simpleMessage(
            "Click to see all user information"),
        "clickToSeeAllUserMessagesDetails":
            MessageLookupByLibrary.simpleMessage(
                "Cliquez pour voir tous les détails des messages utilisateur"),
        "clickToSeeAllUserReports": MessageLookupByLibrary.simpleMessage(
            "Click to see all user reports"),
        "clickToSeeAllUserRoomsDetails": MessageLookupByLibrary.simpleMessage(
            "Click to see all user rooms details"),
        "close": MessageLookupByLibrary.simpleMessage("Fermer"),
        "codeHasBeenExpired":
            MessageLookupByLibrary.simpleMessage("Le code a été expiré"),
        "codeMustEqualToSixNumbers": MessageLookupByLibrary.simpleMessage(
            "Le code doit être égal à six nombres"),
        "configureYourAccountPrivacy": MessageLookupByLibrary.simpleMessage(
            "Configurez la confidentialité de votre compte"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirmez le mot de passe"),
        "confirmPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Confirmer le mot de passe doit avoir une valeur"),
        "congregationsYourAccountHasBeenAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Congrégations Votre compte a été accepté"),
        "connecting": MessageLookupByLibrary.simpleMessage("De liaison..."),
        "contactInfo": MessageLookupByLibrary.simpleMessage("Coordonnées"),
        "contactUs": MessageLookupByLibrary.simpleMessage("Contactez-nous"),
        "copy": MessageLookupByLibrary.simpleMessage("Copie"),
        "countries": MessageLookupByLibrary.simpleMessage("Pays"),
        "country": MessageLookupByLibrary.simpleMessage("Pays"),
        "create": MessageLookupByLibrary.simpleMessage("Créer"),
        "createBroadcast":
            MessageLookupByLibrary.simpleMessage("Créer une diffusion"),
        "createGroup": MessageLookupByLibrary.simpleMessage("Créer un groupe"),
        "createMediaStory": MessageLookupByLibrary.simpleMessage(
            "Créer une histoire médiatique"),
        "createStory":
            MessageLookupByLibrary.simpleMessage("Créer une histoire"),
        "createTextStory": MessageLookupByLibrary.simpleMessage(
            "Créer une histoire textuelle"),
        "createYourStory":
            MessageLookupByLibrary.simpleMessage("Créez votre histoire"),
        "createdAt": MessageLookupByLibrary.simpleMessage("Créé à"),
        "creator": MessageLookupByLibrary.simpleMessage("Créateur"),
        "currentDevice":
            MessageLookupByLibrary.simpleMessage("Dispositif actuel"),
        "dashboard": MessageLookupByLibrary.simpleMessage("Tableau de bord"),
        "dataPrivacy":
            MessageLookupByLibrary.simpleMessage("Confidentialité des données"),
        "delete": MessageLookupByLibrary.simpleMessage("Supprimer"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("Supprimer le chat"),
        "deleteFromAll":
            MessageLookupByLibrary.simpleMessage("Supprimer de tous"),
        "deleteFromMe": MessageLookupByLibrary.simpleMessage("Me supprimer"),
        "deleteMember": MessageLookupByLibrary.simpleMessage("Supprimer"),
        "deleteMyAccount":
            MessageLookupByLibrary.simpleMessage("Supprimer mon compte"),
        "deleteThisDeviceDesc": MessageLookupByLibrary.simpleMessage(
            "La suppression de cet appareil signifie déconnecter instantanément cet appareil"),
        "deleteUser":
            MessageLookupByLibrary.simpleMessage("Supprimer l\'utilisateur"),
        "deleteYouCopy":
            MessageLookupByLibrary.simpleMessage("Supprimer votre copie"),
        "deleted": MessageLookupByLibrary.simpleMessage("Supprimé"),
        "deletedAt": MessageLookupByLibrary.simpleMessage("Supprimé à"),
        "delivered": MessageLookupByLibrary.simpleMessage("Livré"),
        "description": MessageLookupByLibrary.simpleMessage("Description"),
        "descriptionIsRequired":
            MessageLookupByLibrary.simpleMessage("Une description est requise"),
        "desktopAndOtherDevices":
            MessageLookupByLibrary.simpleMessage("Bureau et autres appareils"),
        "deviceHasBeenLogoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage(
                "Le périphérique a été déconnecté de tous les appareils"),
        "deviceStatus":
            MessageLookupByLibrary.simpleMessage("État de l\'appareil"),
        "devices": MessageLookupByLibrary.simpleMessage("Dispositifs"),
        "directChat": MessageLookupByLibrary.simpleMessage("Chat direct"),
        "directRooms":
            MessageLookupByLibrary.simpleMessage("Chambres directes"),
        "dismissedToMemberBy":
            MessageLookupByLibrary.simpleMessage("Licencié au membre par"),
        "dismissesToMember":
            MessageLookupByLibrary.simpleMessage("Rejette au membre"),
        "docs": MessageLookupByLibrary.simpleMessage("Docs"),
        "done": MessageLookupByLibrary.simpleMessage("Fait"),
        "download": MessageLookupByLibrary.simpleMessage("Télécharger"),
        "downloading":
            MessageLookupByLibrary.simpleMessage("Téléchargement..."),
        "edit": MessageLookupByLibrary.simpleMessage("Modifier"),
        "email": MessageLookupByLibrary.simpleMessage("E-mail"),
        "emailMustBeValid": MessageLookupByLibrary.simpleMessage(
            "Le courrier électronique doit être valide"),
        "emailNotValid":
            MessageLookupByLibrary.simpleMessage("E-mail non valide"),
        "enterNameAndAddOptionalProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Entrez votre nom et ajoutez une photo de profil en option"),
        "error": MessageLookupByLibrary.simpleMessage("Erreur"),
        "exitGroup": MessageLookupByLibrary.simpleMessage("Groupe de sortie"),
        "explainWhatHappens": MessageLookupByLibrary.simpleMessage(
            "Expliquez ici ce qui se passe"),
        "feedBackEmail":
            MessageLookupByLibrary.simpleMessage("E-mail de commentaires"),
        "fileHasBeenSavedTo": MessageLookupByLibrary.simpleMessage(
            "Le fichier a été enregistré pour"),
        "fileMessages":
            MessageLookupByLibrary.simpleMessage("Messages de dossier"),
        "files": MessageLookupByLibrary.simpleMessage("Fichiers"),
        "finished": MessageLookupByLibrary.simpleMessage("Fini"),
        "forRequest": MessageLookupByLibrary.simpleMessage("Pour demande"),
        "forgetPassword":
            MessageLookupByLibrary.simpleMessage("Oublier le mot de passe?"),
        "forgetPasswordExpireTime": MessageLookupByLibrary.simpleMessage(
            "Oubliez le mot de passe expirer l\'heure"),
        "forward": MessageLookupByLibrary.simpleMessage("Avant"),
        "fullName": MessageLookupByLibrary.simpleMessage("Nom et prénom"),
        "gallery": MessageLookupByLibrary.simpleMessage("Galerie"),
        "globalSearch":
            MessageLookupByLibrary.simpleMessage("Recherche globale"),
        "googlePlayAppUrl": MessageLookupByLibrary.simpleMessage(
            "URL de l\'application Google Play"),
        "group": MessageLookupByLibrary.simpleMessage("Groupe"),
        "groupCreatedBy":
            MessageLookupByLibrary.simpleMessage("Groupe créé par"),
        "groupDescription":
            MessageLookupByLibrary.simpleMessage("Description du groupe"),
        "groupIcon": MessageLookupByLibrary.simpleMessage("Icône de groupe"),
        "groupInfo":
            MessageLookupByLibrary.simpleMessage("Informations de groupe"),
        "groupMembers":
            MessageLookupByLibrary.simpleMessage("Membres du groupe"),
        "groupName": MessageLookupByLibrary.simpleMessage("nom de groupe"),
        "groupParticipants":
            MessageLookupByLibrary.simpleMessage("Participants au groupe"),
        "groupSettings":
            MessageLookupByLibrary.simpleMessage("Paramètres de groupe"),
        "groupWith": MessageLookupByLibrary.simpleMessage("Se regrouper avec"),
        "harassmentOrBullyingDescription": MessageLookupByLibrary.simpleMessage(
            "CHARMEMENT OU L\'INTÉMINATION: Cette option permet aux utilisateurs de signaler que les personnes qui les ciblent ou d\'autres avec des messages harcelants, des menaces ou d\'autres formes d\'intimidation."),
        "help": MessageLookupByLibrary.simpleMessage("Aide"),
        "hiIamUse":
            MessageLookupByLibrary.simpleMessage("Salut iam en utilisant"),
        "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Si cette option est désactivée, la création de diffusion de chat sera bloquée."),
        "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Si cette option est désactivée, la création de groupes de chat sera bloquée."),
        "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Si cette option est désactivée, la connexion de bureau ou l\'enregistrement (Windows, Mac) sera bloqué."),
        "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly":
            MessageLookupByLibrary.simpleMessage(
                "Si cette option est désactivée, la connexion ou l\'enregistrement mobile sera bloqué uniquement sur Android et iOS."),
        "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Si cette option est désactivée, l\'envoi de fichiers de chat, d\'images, de vidéos et de localisation sera bloqué."),
        "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Si cette option est désactivée, la connexion Web ou l\'enregistrement sera bloqué."),
        "ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats":
            MessageLookupByLibrary.simpleMessage(
                "Si cette option est activée, la bannière Google Ads apparaîtra dans les chats."),
        "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed":
            MessageLookupByLibrary.simpleMessage(
                "Si cette option est activée, l\'appel vidéo et vocal sera autorisé."),
        "image": MessageLookupByLibrary.simpleMessage("Image"),
        "imageMessages":
            MessageLookupByLibrary.simpleMessage("Messages d\'image"),
        "images": MessageLookupByLibrary.simpleMessage("Images"),
        "inAppAlerts": MessageLookupByLibrary.simpleMessage(
            "Dans les alertes d\'applications"),
        "inCall": MessageLookupByLibrary.simpleMessage("En appel"),
        "inappropriateContentDescription": MessageLookupByLibrary.simpleMessage(
            "Contenu inapproprié: les utilisateurs peuvent sélectionner cette option pour signaler tout matériel sexuellement explicite, discours de haine ou autre contenu qui viole les normes communautaires."),
        "info": MessageLookupByLibrary.simpleMessage("Informations"),
        "infoMessages":
            MessageLookupByLibrary.simpleMessage("Messages d\'informations"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("Code non valide"),
        "invalidLoginData": MessageLookupByLibrary.simpleMessage(
            "Données de connexion non valides"),
        "ios": MessageLookupByLibrary.simpleMessage("iOS"),
        "joinedAt": MessageLookupByLibrary.simpleMessage("Rejoint le"),
        "joinedBy": MessageLookupByLibrary.simpleMessage("Rejoint par"),
        "kickMember":
            MessageLookupByLibrary.simpleMessage("Membre du coup de pied"),
        "kickedBy": MessageLookupByLibrary.simpleMessage("Relâché par"),
        "language": MessageLookupByLibrary.simpleMessage("Langue"),
        "lastActiveFrom":
            MessageLookupByLibrary.simpleMessage("Dernier actif de"),
        "leaveGroup": MessageLookupByLibrary.simpleMessage("Quitter le groupe"),
        "leaveGroupAndDeleteYourMessageCopy":
            MessageLookupByLibrary.simpleMessage(
                "Laissez le groupe et supprimez votre copie de message"),
        "leftTheGroup":
            MessageLookupByLibrary.simpleMessage("Quitté le groupe"),
        "linkADeviceSoon":
            MessageLookupByLibrary.simpleMessage("Lier un appareil (bientôt)"),
        "linkByQrCode":
            MessageLookupByLibrary.simpleMessage("Lien par code QR"),
        "linkedDevices":
            MessageLookupByLibrary.simpleMessage("Dispositifs liés"),
        "links": MessageLookupByLibrary.simpleMessage("Links"),
        "loading": MessageLookupByLibrary.simpleMessage("Chargement ..."),
        "location": MessageLookupByLibrary.simpleMessage("Emplacement"),
        "locationMessages":
            MessageLookupByLibrary.simpleMessage("Messages de localisation"),
        "logOut": MessageLookupByLibrary.simpleMessage("Déconnecter"),
        "login": MessageLookupByLibrary.simpleMessage("Se connecter"),
        "loginAgain":
            MessageLookupByLibrary.simpleMessage("Connectez-vous à nouveau!"),
        "loginNowAllowedNowPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Connexion maintenant autorisée.Veuillez réessayer plus tard."),
        "logoutFromAllDevices": MessageLookupByLibrary.simpleMessage(
            "Vous connecte à tous les appareils?"),
        "macOs": MessageLookupByLibrary.simpleMessage("macos"),
        "makeCall": MessageLookupByLibrary.simpleMessage("Faire appel"),
        "media": MessageLookupByLibrary.simpleMessage("Médias"),
        "mediaLinksAndDocs":
            MessageLookupByLibrary.simpleMessage("Médias, liens et documents"),
        "member": MessageLookupByLibrary.simpleMessage("Membre"),
        "members": MessageLookupByLibrary.simpleMessage("Membres"),
        "messageCounter":
            MessageLookupByLibrary.simpleMessage("Compteur de messages"),
        "messageHasBeenDeleted":
            MessageLookupByLibrary.simpleMessage("Le message a été supprimé"),
        "messageHasBeenViewed":
            MessageLookupByLibrary.simpleMessage("Le message a été consulté"),
        "messageInfo":
            MessageLookupByLibrary.simpleMessage("Informations sur le message"),
        "messages": MessageLookupByLibrary.simpleMessage("Messages"),
        "microphoneAndCameraPermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "L\'autorisation du microphone et de la caméra doit être acceptée"),
        "microphonePermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "La permission du microphone doit être acceptée"),
        "minutes": MessageLookupByLibrary.simpleMessage("Minutes"),
        "more": MessageLookupByLibrary.simpleMessage("Plus"),
        "mute": MessageLookupByLibrary.simpleMessage("Mute"),
        "muteNotifications":
            MessageLookupByLibrary.simpleMessage("Notifications muettes"),
        "myPrivacy": MessageLookupByLibrary.simpleMessage("Ma vie privée"),
        "name": MessageLookupByLibrary.simpleMessage("Nom"),
        "nameMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Le nom doit avoir une valeur"),
        "needNewAccount": MessageLookupByLibrary.simpleMessage(
            "Besoin d\'un nouveau compte?"),
        "newBroadcast":
            MessageLookupByLibrary.simpleMessage("Nouvelle diffusion"),
        "newGroup": MessageLookupByLibrary.simpleMessage("Nouveau groupe"),
        "newPassword":
            MessageLookupByLibrary.simpleMessage("Nouveau mot de passe"),
        "newPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Le nouveau mot de passe doit avoir de la valeur"),
        "newUpdateIsAvailable": MessageLookupByLibrary.simpleMessage(
            "Une nouvelle mise à jour est disponible"),
        "next": MessageLookupByLibrary.simpleMessage("Suivant"),
        "nickname": MessageLookupByLibrary.simpleMessage("Surnom"),
        "no": MessageLookupByLibrary.simpleMessage("Non"),
        "noBio": MessageLookupByLibrary.simpleMessage("Pas de bio"),
        "noCodeHasBeenSendToYouToVerifyYourEmail":
            MessageLookupByLibrary.simpleMessage(
                "Aucun code ne vous a été envoyé pour vérifier votre e-mail"),
        "noUpdatesAvailableNow": MessageLookupByLibrary.simpleMessage(
            "Aucune mise à jour disponible maintenant"),
        "none": MessageLookupByLibrary.simpleMessage("Aucun"),
        "notAccepted": MessageLookupByLibrary.simpleMessage("Non accepté"),
        "notification": MessageLookupByLibrary.simpleMessage("Notification"),
        "notificationDescription": MessageLookupByLibrary.simpleMessage(
            "Description de la notification"),
        "notificationTitle":
            MessageLookupByLibrary.simpleMessage("Titre de notification"),
        "notificationsPage":
            MessageLookupByLibrary.simpleMessage("Page de notifications"),
        "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion":
            MessageLookupByLibrary.simpleMessage(
                "Maintenant, vous vous connectez en tant qu\'administrateur en lecture seule. Toutes les modifications que vous apportez ne seront pas appliquées car il s\'agit d\'une version de test."),
        "off": MessageLookupByLibrary.simpleMessage("Désactivé"),
        "offline": MessageLookupByLibrary.simpleMessage("Hors ligne"),
        "ok": MessageLookupByLibrary.simpleMessage("D\'ACCORD"),
        "oldPassword":
            MessageLookupByLibrary.simpleMessage("Ancien mot de passe"),
        "on": MessageLookupByLibrary.simpleMessage("Sur"),
        "oneSeenMessage": MessageLookupByLibrary.simpleMessage("Un message vu"),
        "online": MessageLookupByLibrary.simpleMessage("En ligne"),
        "orLoginWith":
            MessageLookupByLibrary.simpleMessage("ou connecter avec"),
        "other": MessageLookupByLibrary.simpleMessage("Autre"),
        "otherCategoryDescription": MessageLookupByLibrary.simpleMessage(
            "Autre: cette catégorie fourre-tout peut être utilisée pour des violations qui ne s\'intègrent pas facilement dans les catégories ci-dessus.Il peut être utile d\'inclure une zone de texte pour que les utilisateurs fournissent des détails supplémentaires."),
        "otpCode": MessageLookupByLibrary.simpleMessage("Code OTP"),
        "password": MessageLookupByLibrary.simpleMessage("Mot de passe"),
        "passwordHasBeenChanged": MessageLookupByLibrary.simpleMessage(
            "Le mot de passe a été modifié"),
        "passwordIsRequired":
            MessageLookupByLibrary.simpleMessage("Le mot de passe est requis"),
        "passwordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Le mot de passe doit avoir une valeur"),
        "passwordNotMatch": MessageLookupByLibrary.simpleMessage(
            "Le mot de passe ne correspond pas"),
        "peerUserDeviceOffline": MessageLookupByLibrary.simpleMessage(
            "Appareil utilisateur de pairs hors ligne"),
        "peerUserInCallNow": MessageLookupByLibrary.simpleMessage(
            "Utilisateur en appel maintenant"),
        "pending": MessageLookupByLibrary.simpleMessage("En attente"),
        "phone": MessageLookupByLibrary.simpleMessage("Téléphone"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage(
            "Politique de confidentialité"),
        "privacyUrl":
            MessageLookupByLibrary.simpleMessage("URL de confidentialité"),
        "profile": MessageLookupByLibrary.simpleMessage("Profil"),
        "promotedToAdminBy":
            MessageLookupByLibrary.simpleMessage("Promu administrer par"),
        "public": MessageLookupByLibrary.simpleMessage("Publique"),
        "read": MessageLookupByLibrary.simpleMessage("Lire"),
        "recentUpdate":
            MessageLookupByLibrary.simpleMessage("Mise à jour récente"),
        "recentUpdates":
            MessageLookupByLibrary.simpleMessage("Mises à jour récentes"),
        "recording": MessageLookupByLibrary.simpleMessage("Enregistrement..."),
        "register": MessageLookupByLibrary.simpleMessage("Registre"),
        "registerMethod":
            MessageLookupByLibrary.simpleMessage("Méthode d\'enregistrement"),
        "registerStatus":
            MessageLookupByLibrary.simpleMessage("État de l\'enregistrement"),
        "rejected": MessageLookupByLibrary.simpleMessage("Rejeté"),
        "repliedToYourSelf":
            MessageLookupByLibrary.simpleMessage("Répondu à toi-même"),
        "reply": MessageLookupByLibrary.simpleMessage("Répondre"),
        "replyToYourSelf":
            MessageLookupByLibrary.simpleMessage("Répondre à vous-même"),
        "report": MessageLookupByLibrary.simpleMessage("Rapport"),
        "reportHasBeenSubmitted":
            MessageLookupByLibrary.simpleMessage("Votre rapport a été soumis"),
        "reportUser":
            MessageLookupByLibrary.simpleMessage("Signaler l\'utilisateur"),
        "reports": MessageLookupByLibrary.simpleMessage("Rapports"),
        "resetPassword": MessageLookupByLibrary.simpleMessage(
            "Réinitialiser le mot de passe"),
        "retry": MessageLookupByLibrary.simpleMessage("Réessayer"),
        "ring": MessageLookupByLibrary.simpleMessage("Anneau"),
        "roomAlreadyInCall":
            MessageLookupByLibrary.simpleMessage("Chambre déjà en appel"),
        "roomCounter": MessageLookupByLibrary.simpleMessage("Comptoir"),
        "saveLogin":
            MessageLookupByLibrary.simpleMessage("Enregistrer la connexion"),
        "search": MessageLookupByLibrary.simpleMessage("Recherche"),
        "seconds": MessageLookupByLibrary.simpleMessage("Secondes"),
        "send": MessageLookupByLibrary.simpleMessage("Envoyer"),
        "sendCodeToMyEmail": MessageLookupByLibrary.simpleMessage(
            "Envoyer du code à mon e-mail"),
        "sendMessage":
            MessageLookupByLibrary.simpleMessage("Envoyer un message"),
        "sessionEnd": MessageLookupByLibrary.simpleMessage("Fin de session"),
        "setMaxBroadcastMembers": MessageLookupByLibrary.simpleMessage(
            "Définir les membres de la diffusion Max"),
        "setMaxGroupMembers": MessageLookupByLibrary.simpleMessage(
            "Définir les membres du groupe Max"),
        "setMaxMessageForwardAndShare": MessageLookupByLibrary.simpleMessage(
            "Set Max Message Forward and Share"),
        "setNewPrivacyPolicyUrl": MessageLookupByLibrary.simpleMessage(
            "Définir une nouvelle URL de politique de confidentialité"),
        "setToAdmin":
            MessageLookupByLibrary.simpleMessage("Réglé sur l\'administration"),
        "settings": MessageLookupByLibrary.simpleMessage("Paramètres"),
        "share": MessageLookupByLibrary.simpleMessage("Partager"),
        "shareMediaAndLocation": MessageLookupByLibrary.simpleMessage(
            "Partager les médias et l\'emplacement"),
        "shareYourStatus":
            MessageLookupByLibrary.simpleMessage("Partagez votre statut"),
        "showHistory":
            MessageLookupByLibrary.simpleMessage("Montrer l\'histoire"),
        "showMedia": MessageLookupByLibrary.simpleMessage("Montrer les médias"),
        "soon": MessageLookupByLibrary.simpleMessage("Bientôt"),
        "spamOrScamDescription": MessageLookupByLibrary.simpleMessage(
            "Spam ou arnaque: cette option serait que les utilisateurs signalent des comptes qui envoient des messages de spam, des publicités non sollicitées ou tentent d\'arracher d\'autres."),
        "star": MessageLookupByLibrary.simpleMessage("Étoile"),
        "starMessage": MessageLookupByLibrary.simpleMessage("Message de star"),
        "starredMessage":
            MessageLookupByLibrary.simpleMessage("Message étoilé"),
        "starredMessages":
            MessageLookupByLibrary.simpleMessage("Messages joués"),
        "startChat":
            MessageLookupByLibrary.simpleMessage("Commencer à discuter"),
        "startNewChatWithYou": MessageLookupByLibrary.simpleMessage(
            "Démarrez une nouvelle conversation avec vous"),
        "status": MessageLookupByLibrary.simpleMessage("Statut"),
        "storageAndData":
            MessageLookupByLibrary.simpleMessage("Stockage et données"),
        "stories": MessageLookupByLibrary.simpleMessage("Histoires"),
        "storyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Histoire créée avec succès"),
        "success": MessageLookupByLibrary.simpleMessage("Succès"),
        "successfullyDownloadedIn":
            MessageLookupByLibrary.simpleMessage("Téléchargé avec succès dans"),
        "supportChatSoon":
            MessageLookupByLibrary.simpleMessage("Soutenir le chat (bientôt)"),
        "tapADeviceToEditOrLogOut": MessageLookupByLibrary.simpleMessage(
            "Appuyez sur un appareil pour modifier ou déconnectez-vous."),
        "tapForPhoto":
            MessageLookupByLibrary.simpleMessage("Taper pour la photo"),
        "tapToSelectAnIcon": MessageLookupByLibrary.simpleMessage(
            "Appuyez pour sélectionner une icône"),
        "tellAFriend": MessageLookupByLibrary.simpleMessage("Dire à un ami"),
        "textFieldHint":
            MessageLookupByLibrary.simpleMessage("Tapez un message ..."),
        "textMessages": MessageLookupByLibrary.simpleMessage("SMS"),
        "thereIsFileHasSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "Il y a un fichier plus grand que la taille autorisée"),
        "thereIsVideoSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "Il y a une taille de vidéo plus grande que la taille autorisée"),
        "timeout": MessageLookupByLibrary.simpleMessage("Temps mort"),
        "titleIsRequired":
            MessageLookupByLibrary.simpleMessage("Le titre est requis"),
        "today": MessageLookupByLibrary.simpleMessage("Aujourd\'hui"),
        "total": MessageLookupByLibrary.simpleMessage("Total"),
        "totalMessages":
            MessageLookupByLibrary.simpleMessage("Messages totaux"),
        "totalRooms":
            MessageLookupByLibrary.simpleMessage("Nombre total de chambres"),
        "totalVisits": MessageLookupByLibrary.simpleMessage("Visites totales"),
        "typing": MessageLookupByLibrary.simpleMessage("Dactylographie..."),
        "unBlock": MessageLookupByLibrary.simpleMessage("Bloc de l\'ONU"),
        "unBlockUser": MessageLookupByLibrary.simpleMessage("Débloquer"),
        "unMute": MessageLookupByLibrary.simpleMessage(
            "Réactiver à être confronté à"),
        "unStar": MessageLookupByLibrary.simpleMessage("Star de l\'ONU"),
        "update": MessageLookupByLibrary.simpleMessage("Mise à jour"),
        "updateBroadcastTitle": MessageLookupByLibrary.simpleMessage(
            "Mettre à jour le titre de diffusion"),
        "updateFeedBackEmail": MessageLookupByLibrary.simpleMessage(
            "Mettre à jour le courrier électronique des commentaires"),
        "updateGroupDescription": MessageLookupByLibrary.simpleMessage(
            "Mettre à jour la description du groupe"),
        "updateGroupDescriptionWillUpdateAllGroupMembers":
            MessageLookupByLibrary.simpleMessage(
                "Mettre à jour la description du groupe mettra à jour tous les membres du groupe"),
        "updateGroupTitle": MessageLookupByLibrary.simpleMessage(
            "Mettre à jour le titre du groupe"),
        "updateImage":
            MessageLookupByLibrary.simpleMessage("Mettre à jour l\'image"),
        "updateNickname":
            MessageLookupByLibrary.simpleMessage("Mettre à jour le surnom"),
        "updateTitle":
            MessageLookupByLibrary.simpleMessage("Mettre à jour le titre"),
        "updateTitleTo":
            MessageLookupByLibrary.simpleMessage("Mettre à jour le titre de"),
        "updateYourBio":
            MessageLookupByLibrary.simpleMessage("Mettez à jour votre bio"),
        "updateYourName":
            MessageLookupByLibrary.simpleMessage("Mettez à jour votre nom"),
        "updateYourPassword": MessageLookupByLibrary.simpleMessage(
            "Mettez à jour votre mot de passe"),
        "updateYourProfile":
            MessageLookupByLibrary.simpleMessage("Mettez à jour votre profil"),
        "updatedAt": MessageLookupByLibrary.simpleMessage("Mis à jour à"),
        "upgradeToAdmin":
            MessageLookupByLibrary.simpleMessage("Passer à l\'administrateur"),
        "userAction":
            MessageLookupByLibrary.simpleMessage("Action de l\'utilisateur"),
        "userAlreadyRegister": MessageLookupByLibrary.simpleMessage(
            "L\'utilisateur s\'inscrit déjà"),
        "userDeviceSessionEndDeviceDeleted": MessageLookupByLibrary.simpleMessage(
            "Appareil de la session du périphérique de session de la session supprimée"),
        "userEmailNotFound": MessageLookupByLibrary.simpleMessage(
            "E-mail utilisateur introuvable"),
        "userInfo": MessageLookupByLibrary.simpleMessage(
            "Informations sur l\'utilisateur"),
        "userPage": MessageLookupByLibrary.simpleMessage("Page utilisateur"),
        "userProfile":
            MessageLookupByLibrary.simpleMessage("Profil de l\'utilisateur"),
        "userRegisterStatusNotAcceptedYet":
            MessageLookupByLibrary.simpleMessage(
                "État du registre des utilisateurs non accepté"),
        "users": MessageLookupByLibrary.simpleMessage("Utilisateurs"),
        "usersAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Les utilisateurs ont ajouté avec succès"),
        "vMessageInfoTrans":
            MessageLookupByLibrary.simpleMessage("Informations sur le message"),
        "vMessagesInfoTrans": MessageLookupByLibrary.simpleMessage(
            "Informations sur les messages"),
        "verified": MessageLookupByLibrary.simpleMessage("Vérifié"),
        "verifiedAt": MessageLookupByLibrary.simpleMessage("Vérifié à"),
        "video": MessageLookupByLibrary.simpleMessage("Vidéo"),
        "videoCallMessages":
            MessageLookupByLibrary.simpleMessage("Messages d\'appel vidéo"),
        "videoMessages": MessageLookupByLibrary.simpleMessage("Messages vidéo"),
        "visits": MessageLookupByLibrary.simpleMessage("Visites"),
        "voiceCallMessage":
            MessageLookupByLibrary.simpleMessage("Message d\'appel vocal"),
        "voiceCallMessages":
            MessageLookupByLibrary.simpleMessage("Messages d\'appel vocal"),
        "voiceMessages":
            MessageLookupByLibrary.simpleMessage("Messages vocaux"),
        "wait2MinutesToSendMail": MessageLookupByLibrary.simpleMessage(
            "Attendez 2 minutes pour envoyer le courrier"),
        "waitingList": MessageLookupByLibrary.simpleMessage("Liste d\'attente"),
        "weHighRecommendToDownloadThisUpdate":
            MessageLookupByLibrary.simpleMessage(
                "Nous recommandons haut de télécharger cette mise à jour"),
        "web": MessageLookupByLibrary.simpleMessage("Web"),
        "welcome": MessageLookupByLibrary.simpleMessage("Welcome"),
        "whenUsingMobileData": MessageLookupByLibrary.simpleMessage(
            "Lorsque vous utilisez des données mobiles"),
        "whenUsingWifi": MessageLookupByLibrary.simpleMessage(
            "Lorsque vous utilisez le Wi-Fi"),
        "whileAuthCanFindYou": MessageLookupByLibrary.simpleMessage(
            "Bien que l\'authentification ne puisse pas vous trouver"),
        "windows": MessageLookupByLibrary.simpleMessage("Fenêtre"),
        "writeACaption":
            MessageLookupByLibrary.simpleMessage("Écrivez une légende..."),
        "yes": MessageLookupByLibrary.simpleMessage("Oui"),
        "yesterday": MessageLookupByLibrary.simpleMessage("Hier"),
        "you": MessageLookupByLibrary.simpleMessage("Toi"),
        "youAreAboutToDeleteThisUserFromYourList":
            MessageLookupByLibrary.simpleMessage(
                "Vous êtes sur le point de supprimer cet utilisateur de votre liste"),
        "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList":
            MessageLookupByLibrary.simpleMessage(
                "Vous êtes sur le point de supprimer votre compte, votre compte n\'apparaîtra plus dans la liste des utilisateurs"),
        "youAreAboutToDismissesToMember": MessageLookupByLibrary.simpleMessage(
            "Vous êtes sur le point de rejeter au membre"),
        "youAreAboutToKick": MessageLookupByLibrary.simpleMessage(
            "Tu es sur le point de donner des coups de pied"),
        "youAreAboutToUpgradeToAdmin": MessageLookupByLibrary.simpleMessage(
            "Vous êtes sur le point de passer à l\'administrateur"),
        "youDontHaveAccess":
            MessageLookupByLibrary.simpleMessage("Vous n\'avez pas accès"),
        "youInPublicSearch": MessageLookupByLibrary.simpleMessage(
            "Vous dans la recherche publique"),
        "youNotParticipantInThisGroup": MessageLookupByLibrary.simpleMessage(
            "Vous ne participiez pas à ce groupe"),
        "yourAccountBlocked":
            MessageLookupByLibrary.simpleMessage("Votre compte a été banni"),
        "yourAccountDeleted":
            MessageLookupByLibrary.simpleMessage("Votre compte a été supprimé"),
        "yourAccountIsUnderReview": MessageLookupByLibrary.simpleMessage(
            "Votre compte est en cours d\'examen"),
        "yourAreAboutToLogoutFromThisAccount":
            MessageLookupByLibrary.simpleMessage(
                "Vous êtes sur le point de vous connecter à partir de ce compte"),
        "yourLastSeen":
            MessageLookupByLibrary.simpleMessage("Votre dernière vue"),
        "yourLastSeenInChats": MessageLookupByLibrary.simpleMessage(
            "Votre dernière vue dans les chats"),
        "yourProfileAppearsInPublicSearchAndAddingForGroups":
            MessageLookupByLibrary.simpleMessage(
                "Votre profil apparaît dans la recherche publique et l\'ajout de groupes"),
        "yourSessionIsEndedPleaseLoginAgain":
            MessageLookupByLibrary.simpleMessage(
                "Votre session est terminée, veuillez vous connecter à nouveau!"),
        "yourStory": MessageLookupByLibrary.simpleMessage("Votre histoire")
      };
}
