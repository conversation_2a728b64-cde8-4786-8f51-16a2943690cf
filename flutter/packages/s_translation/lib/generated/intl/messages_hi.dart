// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a hi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'hi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Dashboard": MessageLookupByLibrary.simpleMessage("डैशबोर्ड"),
        "about": MessageLookupByLibrary.simpleMessage("के बारे में"),
        "aboutToBlockUserWithConsequences": MessageLookupByLibrary.simpleMessage(
            "आप इस उपयोगकर्ता को ब्लॉक करने के बारे में हैं। आप उसे चैट नहीं भेज सकते और उसे समूहों या प्रसारण में नहीं जोड़ सकते हैं!"),
        "accepted": MessageLookupByLibrary.simpleMessage("स्वीकृत"),
        "account": MessageLookupByLibrary.simpleMessage("खाता"),
        "actions": MessageLookupByLibrary.simpleMessage("क्रियाएँ"),
        "addMembers": MessageLookupByLibrary.simpleMessage("सदस्य जोड़ें"),
        "addNewStory": MessageLookupByLibrary.simpleMessage("नई कहानी जोड़ें"),
        "addParticipants":
            MessageLookupByLibrary.simpleMessage("सहभागियों को जोड़ें"),
        "addedYouToNewBroadcast": MessageLookupByLibrary.simpleMessage(
            "नए प्रसारण में आपको जोड़ दिया गया"),
        "admin": MessageLookupByLibrary.simpleMessage("एडमिन"),
        "adminNotification":
            MessageLookupByLibrary.simpleMessage("एडमिन अधिसूचना"),
        "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself":
            MessageLookupByLibrary.simpleMessage(
                "सभी डेटा का बैकअप कर लिया गया है, आपको स्वयं डेटा को प्रबंधित करने की आवश्यकता नहीं है! यदि आप लॉगआउट करते हैं और फिर से लॉगिन करते हैं, तो आपको सभी चैट्स दिखाई देंगे, वेब संस्करण के लिए भी समान"),
        "allDeletedMessages":
            MessageLookupByLibrary.simpleMessage("सभी हटाएँ गई संदेश"),
        "allowAds": MessageLookupByLibrary.simpleMessage("विज्ञापन अनुमति दें"),
        "allowCalls": MessageLookupByLibrary.simpleMessage("कॉल्स अनुमति दें"),
        "allowCreateBroadcast":
            MessageLookupByLibrary.simpleMessage("प्रसारण बनाने की अनुमति दें"),
        "allowCreateGroups":
            MessageLookupByLibrary.simpleMessage("समूह बनाने की अनुमति दें"),
        "allowDesktopLogin":
            MessageLookupByLibrary.simpleMessage("डेस्कटॉप लॉगिन अनुमति दें"),
        "allowMobileLogin":
            MessageLookupByLibrary.simpleMessage("मोबाइल लॉगिन अनुमति दें"),
        "allowSendMedia":
            MessageLookupByLibrary.simpleMessage("मीडिया भेजने की अनुमति दें"),
        "allowWebLogin":
            MessageLookupByLibrary.simpleMessage("वेब लॉगिन अनुमति दें"),
        "alreadyHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("पहले से ही खाता है?"),
        "android": MessageLookupByLibrary.simpleMessage("एंड्रॉयड"),
        "appMembers": MessageLookupByLibrary.simpleMessage("ऐप सदस्य"),
        "appleStoreAppUrl":
            MessageLookupByLibrary.simpleMessage("Apple स्टोर ऐप URL"),
        "areYouSure":
            MessageLookupByLibrary.simpleMessage("क्या आपको यकीन है?"),
        "areYouSureToBlock": MessageLookupByLibrary.simpleMessage(
            "क्या आप निश्चित हैं कि आप ब्लॉक करना चाहते हैं"),
        "areYouSureToLeaveThisGroupThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "क्या आप इस समूह को छोड़ने के लिए निश्चित हैं? यह कार्रवाई पूर्वरूप में नहीं ली जा सकती"),
        "areYouSureToPermitYourCopyThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "क्या आप निश्चित हैं कि आप अपनी प्रतिलिपि की अनुमति देना चाहते हैं? यह कार्रवाई पूर्वरूप में नहीं ली जा सकती"),
        "areYouSureToReportUserToAdmin": MessageLookupByLibrary.simpleMessage(
            "क्या आप इस उपयोगकर्ता के खिलाफ रिपोर्ट प्रस्तुत करने के लिए सुनिश्चित हैं?"),
        "areYouSureToUnBlock": MessageLookupByLibrary.simpleMessage(
            "क्या आप सुनिश्चित हैं कि आप अनब्लॉक करना चाहते हैं"),
        "areYouWantToMakeVideoCall": MessageLookupByLibrary.simpleMessage(
            "क्या आप वीडियो कॉल करना चाहते हैं?"),
        "areYouWantToMakeVoiceCall": MessageLookupByLibrary.simpleMessage(
            "क्या आप वॉयस कॉल करना चाहते हैं?"),
        "audio": MessageLookupByLibrary.simpleMessage("ऑडियो"),
        "audioCall": MessageLookupByLibrary.simpleMessage("ऑडियो कॉल"),
        "back": MessageLookupByLibrary.simpleMessage("वापस"),
        "banAt": MessageLookupByLibrary.simpleMessage("बैन हुआ था"),
        "banTo": MessageLookupByLibrary.simpleMessage("तक बैन किया गया है"),
        "bio": MessageLookupByLibrary.simpleMessage("जीवनी"),
        "block": MessageLookupByLibrary.simpleMessage("ब्लॉक"),
        "blockUser":
            MessageLookupByLibrary.simpleMessage("उपयोगकर्ता को ब्लॉक करें"),
        "blocked": MessageLookupByLibrary.simpleMessage("ब्लॉक किया गया"),
        "blockedUsers":
            MessageLookupByLibrary.simpleMessage("ब्लॉक किए गए उपयोगकर्ता"),
        "broadcast": MessageLookupByLibrary.simpleMessage("प्रसारण"),
        "broadcastInfo":
            MessageLookupByLibrary.simpleMessage("प्रसारण जानकारी"),
        "broadcastMembers":
            MessageLookupByLibrary.simpleMessage("प्रसारण सदस्य"),
        "broadcastName": MessageLookupByLibrary.simpleMessage("प्रसारण नाम"),
        "broadcastParticipants":
            MessageLookupByLibrary.simpleMessage("प्रसारण सहभागियों"),
        "broadcastSettings":
            MessageLookupByLibrary.simpleMessage("प्रसारण सेटिंग्स"),
        "callNotAllowed":
            MessageLookupByLibrary.simpleMessage("कॉल अनुमत नहीं है"),
        "callTimeoutInSeconds":
            MessageLookupByLibrary.simpleMessage("कॉल टाइमआउट (सेकंड में)"),
        "calls": MessageLookupByLibrary.simpleMessage("कॉल्स"),
        "camera": MessageLookupByLibrary.simpleMessage("कैमरा"),
        "cancel": MessageLookupByLibrary.simpleMessage("रद्द करें"),
        "canceled": MessageLookupByLibrary.simpleMessage("रद्द किया गया"),
        "changeSubject": MessageLookupByLibrary.simpleMessage("विषय बदलें"),
        "chat": MessageLookupByLibrary.simpleMessage("चैट"),
        "chats": MessageLookupByLibrary.simpleMessage("चैट्स"),
        "checkForUpdates":
            MessageLookupByLibrary.simpleMessage("अपडेट की जाँच करें"),
        "chooseAtLestOneMember":
            MessageLookupByLibrary.simpleMessage("कम से कम एक सदस्य चुनें"),
        "chooseHowAutomaticDownloadWorks": MessageLookupByLibrary.simpleMessage(
            "चुनें कि स्वचालित डाउनलोड कैसे काम करता है"),
        "chooseRoom": MessageLookupByLibrary.simpleMessage("कमरा चुनें"),
        "clear": MessageLookupByLibrary.simpleMessage("साफ़ करें"),
        "clearCallsConfirm": MessageLookupByLibrary.simpleMessage(
            "कॉल साफ़ करने की पुष्टि करें"),
        "clearChat": MessageLookupByLibrary.simpleMessage("चैट साफ करें"),
        "clickToAddGroupDescription": MessageLookupByLibrary.simpleMessage(
            "समूह विवरण जोड़ने के लिए क्लिक करें"),
        "clickToSee":
            MessageLookupByLibrary.simpleMessage("देखने के लिए क्लिक करें"),
        "clickToSeeAllUserCountries": MessageLookupByLibrary.simpleMessage(
            "उपयोगकर्ता देशों को देखने के लिए क्लिक करें"),
        "clickToSeeAllUserDevicesDetails": MessageLookupByLibrary.simpleMessage(
            "उपयोगकर्ता डिवाइस विवरण देखने के लिए क्लिक करें"),
        "clickToSeeAllUserInformations": MessageLookupByLibrary.simpleMessage(
            "उपयोगकर्ता जानकारी देखने के लिए क्लिक करें"),
        "clickToSeeAllUserMessagesDetails":
            MessageLookupByLibrary.simpleMessage(
                "उपयोगकर्ता संदेश विवरण देखने के लिए क्लिक करें"),
        "clickToSeeAllUserReports": MessageLookupByLibrary.simpleMessage(
            "उपयोगकर्ता रिपोर्ट्स देखने के लिए क्लिक करें"),
        "clickToSeeAllUserRoomsDetails": MessageLookupByLibrary.simpleMessage(
            "उपयोगकर्ता कक्ष विवरण देखने के लिए क्लिक करें"),
        "close": MessageLookupByLibrary.simpleMessage("बंद करें"),
        "codeHasBeenExpired": MessageLookupByLibrary.simpleMessage(
            "कोड की समय सीमा समाप्त हो गई है"),
        "codeMustEqualToSixNumbers": MessageLookupByLibrary.simpleMessage(
            "कोड छः अंकों के बराबर होना चाहिए"),
        "configureYourAccountPrivacy": MessageLookupByLibrary.simpleMessage(
            "अपने खाते की गोपनीयता कॉन्फ़िगर करें"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("पासवर्ड की पुष्टि करें"),
        "confirmPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "पासवर्ड की पुष्टि में मूल्य होना चाहिए"),
        "congregationsYourAccountHasBeenAccepted":
            MessageLookupByLibrary.simpleMessage(
                "समूह की ओर से आपका खाता स्वीकृत किया गया है"),
        "connecting":
            MessageLookupByLibrary.simpleMessage("कनेक्ट हो रहा है..."),
        "contactInfo": MessageLookupByLibrary.simpleMessage("संपर्क जानकारी"),
        "contactUs": MessageLookupByLibrary.simpleMessage("हमसे संपर्क करें"),
        "copy": MessageLookupByLibrary.simpleMessage("कॉपी"),
        "countries": MessageLookupByLibrary.simpleMessage("देश"),
        "country": MessageLookupByLibrary.simpleMessage("देश"),
        "create": MessageLookupByLibrary.simpleMessage("बनाएं"),
        "createBroadcast":
            MessageLookupByLibrary.simpleMessage("प्रसारण बनाएं"),
        "createGroup": MessageLookupByLibrary.simpleMessage("समूह बनाएं"),
        "createMediaStory":
            MessageLookupByLibrary.simpleMessage("मीडिया कहानी बनाएं"),
        "createStory": MessageLookupByLibrary.simpleMessage("कहानी बनाएं"),
        "createTextStory":
            MessageLookupByLibrary.simpleMessage("टेक्स्ट कहानी बनाएं"),
        "createYourStory":
            MessageLookupByLibrary.simpleMessage("अपनी कहानी बनाएं"),
        "createdAt": MessageLookupByLibrary.simpleMessage("बनाया गया"),
        "creator": MessageLookupByLibrary.simpleMessage("निर्माता"),
        "currentDevice": MessageLookupByLibrary.simpleMessage("वर्तमान डिवाइस"),
        "dashboard": MessageLookupByLibrary.simpleMessage("डैशबोर्ड"),
        "dataPrivacy": MessageLookupByLibrary.simpleMessage("डेटा गोपनीयता"),
        "delete": MessageLookupByLibrary.simpleMessage("हटाएं"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("चैट हटाएं"),
        "deleteFromAll": MessageLookupByLibrary.simpleMessage("सभी से हटाएं"),
        "deleteFromMe": MessageLookupByLibrary.simpleMessage("मुझसे हटाएं"),
        "deleteMember": MessageLookupByLibrary.simpleMessage("सदस्य हटाएं"),
        "deleteMyAccount":
            MessageLookupByLibrary.simpleMessage("मेरा खाता हटाएं"),
        "deleteThisDeviceDesc": MessageLookupByLibrary.simpleMessage(
            "इस डिवाइस को हटाने से इसे तुरंत लॉगआउट कर दिया जाता है"),
        "deleteUser": MessageLookupByLibrary.simpleMessage("उपयोगकर्ता हटाएं"),
        "deleteYouCopy":
            MessageLookupByLibrary.simpleMessage("अपनी प्रतिलिपि मिटाएं"),
        "deleted": MessageLookupByLibrary.simpleMessage("हटाया गया"),
        "deletedAt": MessageLookupByLibrary.simpleMessage("हटाया गया है"),
        "delivered": MessageLookupByLibrary.simpleMessage("पहुँचाया गया"),
        "description": MessageLookupByLibrary.simpleMessage("विवरण"),
        "descriptionIsRequired":
            MessageLookupByLibrary.simpleMessage("विवरण आवश्यक है"),
        "desktopAndOtherDevices":
            MessageLookupByLibrary.simpleMessage("डेस्कटॉप और अन्य डिवाइसेस"),
        "deviceHasBeenLogoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage(
                "उपकरण को सभी उपकरणों से लॉगआउट कर दिया गया है"),
        "deviceStatus": MessageLookupByLibrary.simpleMessage("डिवाइस स्थिति"),
        "devices": MessageLookupByLibrary.simpleMessage("डिवाइसेस"),
        "directChat": MessageLookupByLibrary.simpleMessage("स्थान संदेश"),
        "directRooms": MessageLookupByLibrary.simpleMessage("सीधे कक्ष"),
        "dismissedToMemberBy":
            MessageLookupByLibrary.simpleMessage("के द्वारा सदस्य बनाया गया"),
        "dismissesToMember":
            MessageLookupByLibrary.simpleMessage("सदस्य को निष्कासित करें"),
        "docs": MessageLookupByLibrary.simpleMessage("दस्तावेज़"),
        "done": MessageLookupByLibrary.simpleMessage("किया हुआ"),
        "download": MessageLookupByLibrary.simpleMessage("डाउनलोड"),
        "downloading":
            MessageLookupByLibrary.simpleMessage("डाउनलोड हो रहा है..."),
        "edit": MessageLookupByLibrary.simpleMessage("संपादित करें"),
        "email": MessageLookupByLibrary.simpleMessage("ईमेल"),
        "emailMustBeValid":
            MessageLookupByLibrary.simpleMessage("ईमेल मान्य होनी चाहिए"),
        "emailNotValid":
            MessageLookupByLibrary.simpleMessage("ईमेल मान्य नहीं है"),
        "enterNameAndAddOptionalProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "अपना नाम दर्ज करें और ऐप्शनल प्रोफ़ाइल चित्र जोड़ें"),
        "error": MessageLookupByLibrary.simpleMessage("त्रुटि"),
        "exitGroup":
            MessageLookupByLibrary.simpleMessage("समूह से बाहर निकलें"),
        "explainWhatHappens":
            MessageLookupByLibrary.simpleMessage("यहां बताएं कि क्या होता है"),
        "feedBackEmail": MessageLookupByLibrary.simpleMessage("फीडबैक ईमेल"),
        "fileHasBeenSavedTo":
            MessageLookupByLibrary.simpleMessage("फ़ाइल को सहेज लिया गया है"),
        "fileMessages": MessageLookupByLibrary.simpleMessage("फ़ाइल संदेश"),
        "files": MessageLookupByLibrary.simpleMessage("फ़ाइलें"),
        "finished": MessageLookupByLibrary.simpleMessage("समाप्त"),
        "forRequest": MessageLookupByLibrary.simpleMessage("अनुरोध के लिए"),
        "forgetPassword":
            MessageLookupByLibrary.simpleMessage("पासवर्ड भूल गए"),
        "forgetPasswordExpireTime": MessageLookupByLibrary.simpleMessage(
            "पासवर्ड भूल जाने का समय समाप्त हो गया है"),
        "forward": MessageLookupByLibrary.simpleMessage("फ़ॉरवर्ड"),
        "fullName": MessageLookupByLibrary.simpleMessage("पूरा नाम"),
        "gallery": MessageLookupByLibrary.simpleMessage("गैलरी"),
        "globalSearch": MessageLookupByLibrary.simpleMessage("वैश्विक खोज"),
        "googlePlayAppUrl":
            MessageLookupByLibrary.simpleMessage("Google Play ऐप URL"),
        "group": MessageLookupByLibrary.simpleMessage("ग्रुप"),
        "groupCreatedBy":
            MessageLookupByLibrary.simpleMessage("समूह द्वारा बनाया गया"),
        "groupDescription": MessageLookupByLibrary.simpleMessage("समूह विवरण"),
        "groupIcon": MessageLookupByLibrary.simpleMessage("समूह प्रतीक"),
        "groupInfo": MessageLookupByLibrary.simpleMessage("समूह जानकारी"),
        "groupMembers": MessageLookupByLibrary.simpleMessage("समूह सदस्य"),
        "groupName": MessageLookupByLibrary.simpleMessage("समूह नाम"),
        "groupParticipants":
            MessageLookupByLibrary.simpleMessage("समूह सहभागी"),
        "groupSettings": MessageLookupByLibrary.simpleMessage("समूह सेटिंग्स"),
        "groupWith": MessageLookupByLibrary.simpleMessage("समूह के साथ"),
        "harassmentOrBullyingDescription": MessageLookupByLibrary.simpleMessage(
            "उपेक्षा या बुल्लिंग: इस विकल्प के तहत उपयोगकर्ता उन्हें या दूसरों को तंग करने वाले संदेशों, धमकियों या अन्य प्रकार के बुल्लिंग के संदेशों के खिलाफ रिपोर्ट कर सकते हैं।"),
        "help": MessageLookupByLibrary.simpleMessage("सहायता"),
        "hiIamUse":
            MessageLookupByLibrary.simpleMessage("हाय मैं उपयोग करता हूँ"),
        "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "इस विकल्प को अक्षम किया गया है, तो चैट प्रसारण ब्लॉक किया जाएगा"),
        "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "इस विकल्प को अक्षम किया गया है, तो चैट समूह ब्लॉक किया जाएगा"),
        "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "इस विकल्प को अक्षम किया गया है, तो डेस्कटॉप लॉगिन या पंजीकरण (Windows और macOS) ब्लॉक किया जाएगा"),
        "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly":
            MessageLookupByLibrary.simpleMessage(
                "इस विकल्प को सक्षम किया गया है, तो गूगल ऐड्स बैनर चैटों में प्रदर्शित होंगे"),
        "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "इस विकल्प को अक्षम किया गया है, तो चैट फ़ाइल्स, इमेज, वीडियो और स्थान भेजने की अनुमति नहीं होगी"),
        "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "इस विकल्प को अक्षम किया गया है, तो वेब लॉगिन या पंजीकरण ब्लॉक किया जाएगा"),
        "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed":
            MessageLookupByLibrary.simpleMessage(
                "इस विकल्प को सक्षम किया गया है, तो वीडियो और वॉयस कॉल की अनुमति होगी"),
        "image": MessageLookupByLibrary.simpleMessage("चित्र"),
        "imageMessages": MessageLookupByLibrary.simpleMessage("छवि संदेश"),
        "images": MessageLookupByLibrary.simpleMessage("चित्र"),
        "inAppAlerts": MessageLookupByLibrary.simpleMessage("ऐप अलर्ट्स"),
        "inCall": MessageLookupByLibrary.simpleMessage("कॉल में"),
        "inappropriateContentDescription": MessageLookupByLibrary.simpleMessage(
            "अनुचित सामग्री: उपयोगकर्ता इस विकल्प को चुनकर किसी भी यौनता स्पष्ट सामग्री, घृणा भाषा या अन्य सामुदायिक मानकों का उल्लंघन करने वाली सामग्री की रिपोर्ट कर सकते हैं।"),
        "info": MessageLookupByLibrary.simpleMessage("जानकारी"),
        "infoMessages": MessageLookupByLibrary.simpleMessage("सूचना संदेश"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("अमान्य कोड"),
        "invalidLoginData":
            MessageLookupByLibrary.simpleMessage("अमान्य लॉगिन डेटा"),
        "ios": MessageLookupByLibrary.simpleMessage("iOS"),
        "joinedAt": MessageLookupByLibrary.simpleMessage("शामिल हुए"),
        "joinedBy": MessageLookupByLibrary.simpleMessage("के द्वारा जुड़े"),
        "kickMember": MessageLookupByLibrary.simpleMessage("सदस्य को निकालें"),
        "kickedBy": MessageLookupByLibrary.simpleMessage("के द्वारा किया गया"),
        "language": MessageLookupByLibrary.simpleMessage("भाषा"),
        "lastActiveFrom":
            MessageLookupByLibrary.simpleMessage("पिछले सक्रिय से"),
        "leaveGroup": MessageLookupByLibrary.simpleMessage("समूह छोड़ें"),
        "leaveGroupAndDeleteYourMessageCopy":
            MessageLookupByLibrary.simpleMessage(
                "समूह छोड़कर अपनी संदेश प्रतिलिपि को हटाएं"),
        "leftTheGroup": MessageLookupByLibrary.simpleMessage("समूह छोड़ दिया"),
        "linkADeviceSoon": MessageLookupByLibrary.simpleMessage(
            "एक डिवाइस को लिंक करें (जल्द ही)"),
        "linkByQrCode":
            MessageLookupByLibrary.simpleMessage("क्यूआर कोड द्वारा लिंक करें"),
        "linkedDevices":
            MessageLookupByLibrary.simpleMessage("लिंक की गई डिवाइसेस"),
        "links": MessageLookupByLibrary.simpleMessage("लिंक्स"),
        "loading": MessageLookupByLibrary.simpleMessage("लोड हो रहा है ..."),
        "location": MessageLookupByLibrary.simpleMessage("स्थान"),
        "locationMessages": MessageLookupByLibrary.simpleMessage("स्थान संदेश"),
        "logOut": MessageLookupByLibrary.simpleMessage("लॉग आउट"),
        "login": MessageLookupByLibrary.simpleMessage("लॉगिन"),
        "loginAgain":
            MessageLookupByLibrary.simpleMessage("फिर से लॉगिन करें!"),
        "loginNowAllowedNowPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "वर्तमान में लॉगिन अनुमति नहीं है। कृपया बाद में पुनः प्रयास करें।"),
        "logoutFromAllDevices": MessageLookupByLibrary.simpleMessage(
            "सभी डिवाइसों से लॉगआउट करें?"),
        "macOs": MessageLookupByLibrary.simpleMessage("macOS"),
        "makeCall": MessageLookupByLibrary.simpleMessage("कॉल करें"),
        "media": MessageLookupByLibrary.simpleMessage("मीडिया"),
        "mediaLinksAndDocs": MessageLookupByLibrary.simpleMessage(
            "मीडिया, लिंक्स, और दस्तावेज़"),
        "member": MessageLookupByLibrary.simpleMessage("सदस्य"),
        "members": MessageLookupByLibrary.simpleMessage("सदस्य"),
        "messageCounter": MessageLookupByLibrary.simpleMessage("संदेश गणना"),
        "messageHasBeenDeleted":
            MessageLookupByLibrary.simpleMessage("संदेश हटा दिया गया है"),
        "messageHasBeenViewed":
            MessageLookupByLibrary.simpleMessage("संदेश देखा गया है"),
        "messageInfo": MessageLookupByLibrary.simpleMessage("संदेश जानकारी"),
        "messages": MessageLookupByLibrary.simpleMessage("संदेश"),
        "microphoneAndCameraPermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone and camera permission must be accepted"),
        "microphonePermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone permission must be accepted"),
        "minutes": MessageLookupByLibrary.simpleMessage("मिनट"),
        "more": MessageLookupByLibrary.simpleMessage("अधिक"),
        "mute": MessageLookupByLibrary.simpleMessage("म्यूट"),
        "muteNotifications":
            MessageLookupByLibrary.simpleMessage("सूचनाओं को म्यूट करें"),
        "myPrivacy": MessageLookupByLibrary.simpleMessage("मेरी गोपनीयता"),
        "name": MessageLookupByLibrary.simpleMessage("नाम"),
        "nameMustHaveValue":
            MessageLookupByLibrary.simpleMessage("नाम में मूल्य होना चाहिए"),
        "needNewAccount":
            MessageLookupByLibrary.simpleMessage("नया खाता चाहिए?"),
        "newBroadcast": MessageLookupByLibrary.simpleMessage("नई प्रसारण"),
        "newGroup": MessageLookupByLibrary.simpleMessage("नया समूह"),
        "newPassword": MessageLookupByLibrary.simpleMessage("नया पासवर्ड"),
        "newPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "नया पासवर्ड में मूल्य होना चाहिए"),
        "newUpdateIsAvailable":
            MessageLookupByLibrary.simpleMessage("नई अपडेट उपलब्ध है"),
        "next": MessageLookupByLibrary.simpleMessage("आगे"),
        "nickname": MessageLookupByLibrary.simpleMessage("उपनाम"),
        "no": MessageLookupByLibrary.simpleMessage("नहीं"),
        "noBio": MessageLookupByLibrary.simpleMessage("कोई जीवनी नहीं"),
        "noCodeHasBeenSendToYouToVerifyYourEmail":
            MessageLookupByLibrary.simpleMessage(
                "आपको अपने ईमेल की पुष्टि के लिए कोई कोड नहीं भेजा गया है"),
        "noUpdatesAvailableNow":
            MessageLookupByLibrary.simpleMessage("अब कोई अपडेट उपलब्ध नहीं है"),
        "none": MessageLookupByLibrary.simpleMessage("कोई नहीं"),
        "notAccepted": MessageLookupByLibrary.simpleMessage("स्वीकृत नहीं"),
        "notification": MessageLookupByLibrary.simpleMessage("सूचना"),
        "notificationDescription":
            MessageLookupByLibrary.simpleMessage("सूचना विवरण"),
        "notificationTitle":
            MessageLookupByLibrary.simpleMessage("सूचना शीर्षक"),
        "notificationsPage":
            MessageLookupByLibrary.simpleMessage("सूचना पृष्ठ"),
        "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion":
            MessageLookupByLibrary.simpleMessage(
                "अब आप केवल पठनीय व्यवस्थापक के रूप में लॉगिन कर रहे हैं। आपके द्वारा किए गए सभी संपादन इसके लिए लागू नहीं होंगे क्योंकि यह एक परीक्षण संस्करण है।"),
        "off": MessageLookupByLibrary.simpleMessage("बंद"),
        "offline": MessageLookupByLibrary.simpleMessage("ऑफ़लाइन"),
        "ok": MessageLookupByLibrary.simpleMessage("ठीक है"),
        "oldPassword": MessageLookupByLibrary.simpleMessage("पुराना पासवर्ड"),
        "on": MessageLookupByLibrary.simpleMessage("चालू"),
        "oneSeenMessage":
            MessageLookupByLibrary.simpleMessage("एक बार देखा गया संदेश"),
        "online": MessageLookupByLibrary.simpleMessage("ऑनलाइन"),
        "orLoginWith": MessageLookupByLibrary.simpleMessage("या लॉगिन करें"),
        "other": MessageLookupByLibrary.simpleMessage("अन्य"),
        "otherCategoryDescription": MessageLookupByLibrary.simpleMessage(
            "अन्य: यह एक ऐसा श्रेणी है जिसे उपर्युक्त श्रेणियों में आसानी से नहीं डाला जा सकता है। उपयोगकर्ताओं को अतिरिक्त विवरण प्रदान करने के लिए एक पाठ बॉक्स शामिल करने के लिए सहायक हो सकता है।"),
        "otpCode": MessageLookupByLibrary.simpleMessage("OTP कोड"),
        "password": MessageLookupByLibrary.simpleMessage("पासवर्ड"),
        "passwordHasBeenChanged":
            MessageLookupByLibrary.simpleMessage("पासवर्ड बदल दिया गया है"),
        "passwordIsRequired":
            MessageLookupByLibrary.simpleMessage("पासवर्ड आवश्यक है"),
        "passwordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "पासवर्ड में मूल्य होना चाहिए"),
        "passwordNotMatch":
            MessageLookupByLibrary.simpleMessage("पासवर्ड मेल नहीं खाता"),
        "peerUserDeviceOffline": MessageLookupByLibrary.simpleMessage(
            "पीयर उपयोगकर्ता डिवाइस ऑफ़लाइन है"),
        "peerUserInCallNow":
            MessageLookupByLibrary.simpleMessage("उपयोगकर्ता अब कॉल में है"),
        "pending": MessageLookupByLibrary.simpleMessage("अपूर्ण"),
        "phone": MessageLookupByLibrary.simpleMessage("फ़ोन"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("गोपनीयता नीति"),
        "privacyUrl": MessageLookupByLibrary.simpleMessage("गोपनीयता URL"),
        "profile": MessageLookupByLibrary.simpleMessage("प्रोफ़ाइल"),
        "promotedToAdminBy":
            MessageLookupByLibrary.simpleMessage("के द्वारा प्रशासक बनाया गया"),
        "public": MessageLookupByLibrary.simpleMessage("सार्वजनिक"),
        "read": MessageLookupByLibrary.simpleMessage("पढ़ा गया"),
        "recentUpdate": MessageLookupByLibrary.simpleMessage("हाल का अपडेट"),
        "recentUpdates": MessageLookupByLibrary.simpleMessage("हाल की अपडेट्स"),
        "recording":
            MessageLookupByLibrary.simpleMessage("रेकॉर्ड हो रहा है..."),
        "register": MessageLookupByLibrary.simpleMessage("रजिस्टर करें"),
        "registerMethod": MessageLookupByLibrary.simpleMessage("पंजीकरण विधि"),
        "registerStatus":
            MessageLookupByLibrary.simpleMessage("पंजीकरण स्थिति"),
        "rejected": MessageLookupByLibrary.simpleMessage("अस्वीकृत"),
        "repliedToYourSelf":
            MessageLookupByLibrary.simpleMessage("अपने आप को जवाब दिया"),
        "reply": MessageLookupByLibrary.simpleMessage("जवाब दें"),
        "replyToYourSelf":
            MessageLookupByLibrary.simpleMessage("अपने आप को जवाब दें"),
        "report": MessageLookupByLibrary.simpleMessage("रिपोर्ट"),
        "reportHasBeenSubmitted": MessageLookupByLibrary.simpleMessage(
            "आपकी रिपोर्ट प्रस्तुत की गई है"),
        "reportUser":
            MessageLookupByLibrary.simpleMessage("उपयोगकर्ता की रिपोर्ट करें"),
        "reports": MessageLookupByLibrary.simpleMessage("रिपोर्ट्स"),
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("पासवर्ड रीसेट करें"),
        "retry": MessageLookupByLibrary.simpleMessage("पुन: प्रयास करें"),
        "ring": MessageLookupByLibrary.simpleMessage("रिंग"),
        "roomAlreadyInCall":
            MessageLookupByLibrary.simpleMessage("कमरा पहले से ही कॉल में है"),
        "roomCounter": MessageLookupByLibrary.simpleMessage("कक्ष गणना"),
        "saveLogin": MessageLookupByLibrary.simpleMessage("लॉगिन सहेजें"),
        "search": MessageLookupByLibrary.simpleMessage("खोज"),
        "seconds": MessageLookupByLibrary.simpleMessage("सेकंड्स"),
        "send": MessageLookupByLibrary.simpleMessage("भेजें"),
        "sendCodeToMyEmail":
            MessageLookupByLibrary.simpleMessage("मेरे ईमेल पर कोड भेजें"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("संदेश भेजें"),
        "sessionEnd": MessageLookupByLibrary.simpleMessage("सत्र समाप्ति"),
        "setMaxBroadcastMembers": MessageLookupByLibrary.simpleMessage(
            "अधिकतम प्रसारण सदस्य सेट करें"),
        "setMaxGroupMembers":
            MessageLookupByLibrary.simpleMessage("अधिकतम समूह सदस्य सेट करें"),
        "setMaxMessageForwardAndShare": MessageLookupByLibrary.simpleMessage(
            "अधिकतम संदेश आगे भेजने और साझा करने की सेट करें"),
        "setNewPrivacyPolicyUrl": MessageLookupByLibrary.simpleMessage(
            "नई गोपनीयता नीति URL सेट करें"),
        "setToAdmin":
            MessageLookupByLibrary.simpleMessage("प्रशासक के रूप में सेट करें"),
        "settings": MessageLookupByLibrary.simpleMessage("सेटिंग्स"),
        "share": MessageLookupByLibrary.simpleMessage("शेयर"),
        "shareMediaAndLocation":
            MessageLookupByLibrary.simpleMessage("मीडिया और स्थान साझा करें"),
        "shareYourStatus":
            MessageLookupByLibrary.simpleMessage("अपनी स्थिति साझा करें"),
        "showHistory": MessageLookupByLibrary.simpleMessage("इतिहास दिखाएं"),
        "showMedia": MessageLookupByLibrary.simpleMessage("मीडिया दिखाएं"),
        "soon": MessageLookupByLibrary.simpleMessage("जल्दी ही"),
        "spamOrScamDescription": MessageLookupByLibrary.simpleMessage(
            "स्पैम या धोखाधड़ी: इस विकल्प का उपयोग उन खातों के खिलाफ किया जा सकता है जो स्पैम संदेश, अनगढ़े विज्ञापन या दूसरों को धोखाधड़ी करने का प्रयास कर रहे हैं।"),
        "star": MessageLookupByLibrary.simpleMessage("स्टार"),
        "starMessage":
            MessageLookupByLibrary.simpleMessage("संदेश को स्टार करें"),
        "starredMessage":
            MessageLookupByLibrary.simpleMessage("स्टार दिया गया संदेश"),
        "starredMessages":
            MessageLookupByLibrary.simpleMessage("स्टार दिए गए संदेश"),
        "startChat": MessageLookupByLibrary.simpleMessage("चैट शुरू करें"),
        "startNewChatWithYou":
            MessageLookupByLibrary.simpleMessage("आपके साथ नई चैट शुरू करें"),
        "status": MessageLookupByLibrary.simpleMessage("स्थिति"),
        "storageAndData":
            MessageLookupByLibrary.simpleMessage("स्टोरेज और डेटा"),
        "stories": MessageLookupByLibrary.simpleMessage("कहानियाँ"),
        "storyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("कहानी सफलतापूर्वक बनाई गई"),
        "success": MessageLookupByLibrary.simpleMessage("सफलता"),
        "successfullyDownloadedIn": MessageLookupByLibrary.simpleMessage(
            "सफलतापूर्वक डाउनलोड किया गया है"),
        "supportChatSoon":
            MessageLookupByLibrary.simpleMessage("समर्थन चैट (जल्द ही)"),
        "tapADeviceToEditOrLogOut": MessageLookupByLibrary.simpleMessage(
            "संपादित करने या लॉगआउट करने के लिए एक डिवाइस पर टैप करें।"),
        "tapForPhoto":
            MessageLookupByLibrary.simpleMessage("फ़ोटो के लिए टैप करें"),
        "tapToSelectAnIcon": MessageLookupByLibrary.simpleMessage(
            "एक प्रतीक का चयन करने के लिए टैप करें"),
        "tellAFriend": MessageLookupByLibrary.simpleMessage("दोस्त को बताएं"),
        "textFieldHint":
            MessageLookupByLibrary.simpleMessage("संदेश टाइप करें..."),
        "textMessages": MessageLookupByLibrary.simpleMessage("टेक्स्ट संदेश"),
        "thereIsFileHasSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "फ़ाइल का आकार अनुमत आकार से बड़ा है"),
        "thereIsVideoSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "वीडियो का आकार अनुमत आकार से बड़ा है"),
        "timeout": MessageLookupByLibrary.simpleMessage("समय समाप्ति"),
        "titleIsRequired":
            MessageLookupByLibrary.simpleMessage("शीर्षक आवश्यक है"),
        "today": MessageLookupByLibrary.simpleMessage("आज"),
        "total": MessageLookupByLibrary.simpleMessage("कुल"),
        "totalMessages": MessageLookupByLibrary.simpleMessage("कुल संदेश"),
        "totalRooms": MessageLookupByLibrary.simpleMessage("कुल कक्ष"),
        "totalVisits": MessageLookupByLibrary.simpleMessage("कुल दौरे"),
        "typing": MessageLookupByLibrary.simpleMessage("लिख रहा है..."),
        "unBlock": MessageLookupByLibrary.simpleMessage("अनब्लॉक"),
        "unBlockUser":
            MessageLookupByLibrary.simpleMessage("उपयोगकर्ता को अनब्लॉक करें"),
        "unMute": MessageLookupByLibrary.simpleMessage("अनम्यूट"),
        "unStar": MessageLookupByLibrary.simpleMessage("स्टार हटाएं"),
        "update": MessageLookupByLibrary.simpleMessage("अपडेट"),
        "updateBroadcastTitle":
            MessageLookupByLibrary.simpleMessage("प्रसारण शीर्षक अपडेट करें"),
        "updateFeedBackEmail":
            MessageLookupByLibrary.simpleMessage("फीडबैक ईमेल अपडेट करें"),
        "updateGroupDescription":
            MessageLookupByLibrary.simpleMessage("समूह विवरण अपडेट करें"),
        "updateGroupDescriptionWillUpdateAllGroupMembers":
            MessageLookupByLibrary.simpleMessage(
                "समूह विवरण अपडेट करने से सभी समूह सदस्य अपडेट हो जाएंगे"),
        "updateGroupTitle":
            MessageLookupByLibrary.simpleMessage("समूह शीर्षक अपडेट करें"),
        "updateImage": MessageLookupByLibrary.simpleMessage("छवि अपडेट करें"),
        "updateNickname":
            MessageLookupByLibrary.simpleMessage("उपनाम अपडेट करें"),
        "updateTitle":
            MessageLookupByLibrary.simpleMessage("शीर्षक अपडेट करें"),
        "updateTitleTo":
            MessageLookupByLibrary.simpleMessage("शीर्षक अपडेट करें"),
        "updateYourBio":
            MessageLookupByLibrary.simpleMessage("अपना बायो अपडेट करें"),
        "updateYourName":
            MessageLookupByLibrary.simpleMessage("अपना नाम अपडेट करें"),
        "updateYourPassword":
            MessageLookupByLibrary.simpleMessage("अपना पासवर्ड अपडेट करें"),
        "updateYourProfile":
            MessageLookupByLibrary.simpleMessage("अपना प्रोफ़ाइल अपडेट करें"),
        "updatedAt": MessageLookupByLibrary.simpleMessage("अपडेट किया गया"),
        "upgradeToAdmin": MessageLookupByLibrary.simpleMessage(
            "व्यवस्थापक के रूप में अपग्रेड करें"),
        "userAction": MessageLookupByLibrary.simpleMessage("उपयोगकर्ता क्रिया"),
        "userAlreadyRegister": MessageLookupByLibrary.simpleMessage(
            "उपयोगकर्ता पहले से ही पंजीकृत है"),
        "userDeviceSessionEndDeviceDeleted":
            MessageLookupByLibrary.simpleMessage(
                "उपयोगकर्ता डिवाइस सत्र समाप्त हुआ डिवाइस हटा दिया गया है"),
        "userEmailNotFound":
            MessageLookupByLibrary.simpleMessage("उपयोगकर्ता ईमेल नहीं मिला"),
        "userInfo": MessageLookupByLibrary.simpleMessage("उपयोगकर्ता जानकारी"),
        "userPage": MessageLookupByLibrary.simpleMessage("उपयोगकर्ता पेज"),
        "userProfile":
            MessageLookupByLibrary.simpleMessage("उपयोगकर्ता प्रोफ़ाइल"),
        "userRegisterStatusNotAcceptedYet":
            MessageLookupByLibrary.simpleMessage(
                "उपयोगकर्ता पंजीकरण स्थिति अभी तक स्वीकृत नहीं हुई है"),
        "users": MessageLookupByLibrary.simpleMessage("उपयोगकर्ता"),
        "usersAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "उपयोगकर्ता सफलतापूर्वक जोड़े गए हैं"),
        "vMessageInfoTrans":
            MessageLookupByLibrary.simpleMessage("संदेश जानकारी"),
        "vMessagesInfoTrans":
            MessageLookupByLibrary.simpleMessage("संदेश जानकारी"),
        "verified": MessageLookupByLibrary.simpleMessage("सत्यापित"),
        "verifiedAt": MessageLookupByLibrary.simpleMessage("सत्यापित किया गया"),
        "video": MessageLookupByLibrary.simpleMessage("वीडियो"),
        "videoCallMessages":
            MessageLookupByLibrary.simpleMessage("वीडियो कॉल संदेश"),
        "videoMessages": MessageLookupByLibrary.simpleMessage("वीडियो संदेश"),
        "visits": MessageLookupByLibrary.simpleMessage("यात्राएँ"),
        "voiceCallMessage":
            MessageLookupByLibrary.simpleMessage("वॉयस कॉल संदेश"),
        "voiceCallMessages":
            MessageLookupByLibrary.simpleMessage("वॉयस कॉल संदेश"),
        "voiceMessages": MessageLookupByLibrary.simpleMessage("आवाज संदेश"),
        "wait2MinutesToSendMail": MessageLookupByLibrary.simpleMessage(
            "मेल भेजने के लिए 2 मिनट का इंतजार करें"),
        "waitingList": MessageLookupByLibrary.simpleMessage("प्रतीक्षा सूची"),
        "weHighRecommendToDownloadThisUpdate":
            MessageLookupByLibrary.simpleMessage(
                "हम इस अपडेट को डाउनलोड करने की उच्च सिफारिश करते हैं"),
        "web": MessageLookupByLibrary.simpleMessage("वेब"),
        "welcome": MessageLookupByLibrary.simpleMessage("स्वागत है"),
        "whenUsingMobileData": MessageLookupByLibrary.simpleMessage(
            "मोबाइल डेटा का उपयोग करते समय"),
        "whenUsingWifi":
            MessageLookupByLibrary.simpleMessage("वाई-फाई का उपयोग करते समय"),
        "whileAuthCanFindYou": MessageLookupByLibrary.simpleMessage(
            "प्रमाणीकरण के दौरान आपको नहीं मिल सकता"),
        "windows": MessageLookupByLibrary.simpleMessage("विंडोज"),
        "writeACaption":
            MessageLookupByLibrary.simpleMessage("कैप्शन लिखें..."),
        "yes": MessageLookupByLibrary.simpleMessage("हाँ"),
        "yesterday": MessageLookupByLibrary.simpleMessage("कल"),
        "you": MessageLookupByLibrary.simpleMessage("आप"),
        "youAreAboutToDeleteThisUserFromYourList":
            MessageLookupByLibrary.simpleMessage(
                "आप अपनी सूची से इस उपयोगकर्ता को हटाने के बारे में हैं"),
        "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList":
            MessageLookupByLibrary.simpleMessage(
                "आप अपना खाता हटाने के बारे में हैं, आपका खाता फिर से उपयोगकर्ताओं की सूची में दिखाई नहीं देगा"),
        "youAreAboutToDismissesToMember": MessageLookupByLibrary.simpleMessage(
            "आप सदस्य को निष्कासित करने के बारे में हैं"),
        "youAreAboutToKick":
            MessageLookupByLibrary.simpleMessage("आप किक करने के बारे में हैं"),
        "youAreAboutToUpgradeToAdmin": MessageLookupByLibrary.simpleMessage(
            "आप प्रशासक बनाने के बारे में हैं"),
        "youDontHaveAccess": MessageLookupByLibrary.simpleMessage(
            "आपके पास पहुँचने का अधिकार नहीं है"),
        "youInPublicSearch":
            MessageLookupByLibrary.simpleMessage("आप सार्वजनिक खोज में"),
        "youNotParticipantInThisGroup": MessageLookupByLibrary.simpleMessage(
            "आप इस समूह में प्रतिभागी नहीं हैं"),
        "yourAccountBlocked": MessageLookupByLibrary.simpleMessage(
            "आपका खाता ब्लॉक कर दिया गया है"),
        "yourAccountDeleted":
            MessageLookupByLibrary.simpleMessage("आपका खाता हटा दिया गया है"),
        "yourAccountIsUnderReview": MessageLookupByLibrary.simpleMessage(
            "आपका खाता समीक्षा के अंदर है"),
        "yourAreAboutToLogoutFromThisAccount":
            MessageLookupByLibrary.simpleMessage(
                "आप इस खाते से लॉगआउट करने के बारे में हैं"),
        "yourLastSeen":
            MessageLookupByLibrary.simpleMessage("आपका अंतिम देखा गया"),
        "yourLastSeenInChats":
            MessageLookupByLibrary.simpleMessage("चैट में आपका अंतिम देखा गया"),
        "yourProfileAppearsInPublicSearchAndAddingForGroups":
            MessageLookupByLibrary.simpleMessage(
                "आपका प्रोफ़ाइल सार्वजनिक खोज में दिखाई देता है और समूहों में जोड़ने के लिए उपलब्ध होता है"),
        "yourSessionIsEndedPleaseLoginAgain":
            MessageLookupByLibrary.simpleMessage(
                "आपका सत्र समाप्त हो गया है, कृपया फिर से लॉगिन करें!"),
        "yourStory": MessageLookupByLibrary.simpleMessage("आपकी कहानी")
      };
}
