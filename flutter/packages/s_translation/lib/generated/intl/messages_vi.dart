// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Dashboard": MessageLookupByLibrary.simpleMessage("Bảng điều khiển"),
        "about": MessageLookupByLibrary.simpleMessage("Về chúng tôi"),
        "aboutToBlockUserWithConsequences": MessageLookupByLibrary.simpleMessage(
            "Bạn đang chuẩn bị chặn người dùng này. Bạn sẽ không thể gửi tin nhắn cho họ và không thể thêm họ vào các nhóm hoặc phát sóng!"),
        "accepted": MessageLookupByLibrary.simpleMessage("Đã chấp nhận"),
        "account": MessageLookupByLibrary.simpleMessage("Tài khoản"),
        "actions": MessageLookupByLibrary.simpleMessage("Hành động"),
        "addMembers": MessageLookupByLibrary.simpleMessage("Thêm thành viên"),
        "addNewStory":
            MessageLookupByLibrary.simpleMessage("Thêm câu chuyện mới"),
        "addParticipants":
            MessageLookupByLibrary.simpleMessage("Thêm người tham gia"),
        "addedYouToNewBroadcast": MessageLookupByLibrary.simpleMessage(
            "Đã thêm bạn vào phát sóng mới"),
        "admin": MessageLookupByLibrary.simpleMessage("Quản trị viên"),
        "adminNotification":
            MessageLookupByLibrary.simpleMessage("Thông báo quản trị"),
        "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself":
            MessageLookupByLibrary.simpleMessage(
                "Tất cả dữ liệu đã được sao lưu, bạn không cần quản lý và lưu trữ dữ liệu bằng chính mình! Nếu bạn đăng xuất và đăng nhập lại, bạn sẽ thấy tất cả cuộc trò chuyện giống như phiên bản web"),
        "allDeletedMessages":
            MessageLookupByLibrary.simpleMessage("Tất cả tin nhắn đã xóa"),
        "allowAds": MessageLookupByLibrary.simpleMessage("Cho phép quảng cáo"),
        "allowCalls": MessageLookupByLibrary.simpleMessage("Cho phép cuộc gọi"),
        "allowCreateBroadcast":
            MessageLookupByLibrary.simpleMessage("Cho phép tạo phát sóng"),
        "allowCreateGroups":
            MessageLookupByLibrary.simpleMessage("Cho phép tạo nhóm"),
        "allowDesktopLogin": MessageLookupByLibrary.simpleMessage(
            "Cho phép đăng nhập từ máy tính để bàn"),
        "allowMobileLogin":
            MessageLookupByLibrary.simpleMessage("Cho phép đăng nhập di động"),
        "allowSendMedia": MessageLookupByLibrary.simpleMessage(
            "Cho phép gửi phương tiện truyền thông"),
        "allowWebLogin":
            MessageLookupByLibrary.simpleMessage("Cho phép đăng nhập web"),
        "alreadyHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Đã có tài khoản?"),
        "android": MessageLookupByLibrary.simpleMessage("Android"),
        "appMembers":
            MessageLookupByLibrary.simpleMessage("Thành viên ứng dụng"),
        "appleStoreAppUrl":
            MessageLookupByLibrary.simpleMessage("URL cửa hàng Apple App"),
        "areYouSure": MessageLookupByLibrary.simpleMessage("Bạn có chắc chắn?"),
        "areYouSureToBlock": MessageLookupByLibrary.simpleMessage(
            "Bạn có chắc chắn muốn chặn không"),
        "areYouSureToLeaveThisGroupThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "Bạn có chắc chắn muốn rời khỏi nhóm này? Hành động này không thể hoàn tác"),
        "areYouSureToPermitYourCopyThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "Bạn có chắc chắn muốn cho phép bản sao của bạn? Hành động này không thể hoàn tác"),
        "areYouSureToReportUserToAdmin": MessageLookupByLibrary.simpleMessage(
            "Bạn có chắc chắn muốn báo cáo người dùng này cho quản trị viên?"),
        "areYouSureToUnBlock": MessageLookupByLibrary.simpleMessage(
            "Bạn có chắc chắn muốn bỏ chặn"),
        "areYouWantToMakeVideoCall": MessageLookupByLibrary.simpleMessage(
            "Bạn có muốn thực hiện cuộc gọi video không?"),
        "areYouWantToMakeVoiceCall": MessageLookupByLibrary.simpleMessage(
            "Bạn có muốn thực hiện cuộc gọi thoại không?"),
        "audio": MessageLookupByLibrary.simpleMessage("Âm thanh"),
        "audioCall": MessageLookupByLibrary.simpleMessage("Cuộc gọi âm thanh"),
        "back": MessageLookupByLibrary.simpleMessage("Quay lại"),
        "banAt": MessageLookupByLibrary.simpleMessage("Bị chặn vào ngày"),
        "banTo": MessageLookupByLibrary.simpleMessage("Bị chặn đến ngày"),
        "bio": MessageLookupByLibrary.simpleMessage("Giới thiệu bản thân"),
        "block": MessageLookupByLibrary.simpleMessage("Chặn"),
        "blockUser": MessageLookupByLibrary.simpleMessage("Chặn người dùng"),
        "blocked": MessageLookupByLibrary.simpleMessage("Bị chặn"),
        "blockedUsers":
            MessageLookupByLibrary.simpleMessage("Người dùng đã bị chặn"),
        "broadcast": MessageLookupByLibrary.simpleMessage("Truyền hình"),
        "broadcastInfo":
            MessageLookupByLibrary.simpleMessage("Thông tin phát sóng"),
        "broadcastMembers":
            MessageLookupByLibrary.simpleMessage("Thành viên phát sóng"),
        "broadcastName": MessageLookupByLibrary.simpleMessage("Tên phát sóng"),
        "broadcastParticipants":
            MessageLookupByLibrary.simpleMessage("Người tham gia phát sóng"),
        "broadcastSettings":
            MessageLookupByLibrary.simpleMessage("Cài đặt phát sóng"),
        "callNotAllowed":
            MessageLookupByLibrary.simpleMessage("Cuộc gọi không được phép"),
        "callTimeoutInSeconds": MessageLookupByLibrary.simpleMessage(
            "Thời gian chờ cuộc gọi (giây)"),
        "calls": MessageLookupByLibrary.simpleMessage("Cuộc gọi"),
        "camera": MessageLookupByLibrary.simpleMessage("Máy ảnh"),
        "cancel": MessageLookupByLibrary.simpleMessage("Hủy"),
        "canceled": MessageLookupByLibrary.simpleMessage("Đã hủy"),
        "changeSubject":
            MessageLookupByLibrary.simpleMessage("Thay đổi chủ đề"),
        "chat": MessageLookupByLibrary.simpleMessage("Trò chuyện"),
        "chats": MessageLookupByLibrary.simpleMessage("CUỘC TRÒ CHUYỆN"),
        "checkForUpdates":
            MessageLookupByLibrary.simpleMessage("Kiểm tra cập nhật"),
        "chooseAtLestOneMember":
            MessageLookupByLibrary.simpleMessage("Chọn ít nhất một thành viên"),
        "chooseHowAutomaticDownloadWorks": MessageLookupByLibrary.simpleMessage(
            "Chọn cách hoạt động của tải xuống tự động"),
        "chooseRoom": MessageLookupByLibrary.simpleMessage("Chọn phòng"),
        "clear": MessageLookupByLibrary.simpleMessage("Xóa"),
        "clearCallsConfirm":
            MessageLookupByLibrary.simpleMessage("Xác nhận xóa cuộc gọi"),
        "clearChat": MessageLookupByLibrary.simpleMessage("Xóa trò chuyện"),
        "clickToAddGroupDescription":
            MessageLookupByLibrary.simpleMessage("Nhấp để thêm mô tả nhóm"),
        "clickToSee": MessageLookupByLibrary.simpleMessage("Nhấp để xem"),
        "clickToSeeAllUserCountries": MessageLookupByLibrary.simpleMessage(
            "Nhấn để xem tất cả quốc gia của người dùng"),
        "clickToSeeAllUserDevicesDetails": MessageLookupByLibrary.simpleMessage(
            "Nhấn để xem chi tiết tất cả thiết bị của người dùng"),
        "clickToSeeAllUserInformations": MessageLookupByLibrary.simpleMessage(
            "Nhấn để xem tất cả thông tin người dùng"),
        "clickToSeeAllUserMessagesDetails":
            MessageLookupByLibrary.simpleMessage(
                "Nhấn để xem tất cả chi tiết tin nhắn của người dùng"),
        "clickToSeeAllUserReports": MessageLookupByLibrary.simpleMessage(
            "Nhấn để xem tất cả báo cáo của người dùng"),
        "clickToSeeAllUserRoomsDetails": MessageLookupByLibrary.simpleMessage(
            "Nhấn để xem tất cả chi tiết phòng của người dùng"),
        "close": MessageLookupByLibrary.simpleMessage("Đóng"),
        "codeHasBeenExpired":
            MessageLookupByLibrary.simpleMessage("Mã đã hết hạn"),
        "codeMustEqualToSixNumbers":
            MessageLookupByLibrary.simpleMessage("Mã phải bằng sáu chữ số"),
        "configureYourAccountPrivacy": MessageLookupByLibrary.simpleMessage(
            "Cấu hình quyền riêng tư tài khoản"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Xác nhận mật khẩu"),
        "confirmPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Xác nhận mật khẩu phải có giá trị"),
        "congregationsYourAccountHasBeenAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Chúc mừng tài khoản của bạn đã được chấp nhận"),
        "connecting": MessageLookupByLibrary.simpleMessage("Đang kết nối..."),
        "contactInfo":
            MessageLookupByLibrary.simpleMessage("Thông tin liên hệ"),
        "contactUs": MessageLookupByLibrary.simpleMessage("Liên hệ chúng tôi"),
        "copy": MessageLookupByLibrary.simpleMessage("Sao chép"),
        "countries": MessageLookupByLibrary.simpleMessage("Quốc gia"),
        "country": MessageLookupByLibrary.simpleMessage("Quốc gia"),
        "create": MessageLookupByLibrary.simpleMessage("Tạo"),
        "createBroadcast":
            MessageLookupByLibrary.simpleMessage("Tạo phát sóng"),
        "createGroup": MessageLookupByLibrary.simpleMessage("Tạo nhóm"),
        "createMediaStory": MessageLookupByLibrary.simpleMessage(
            "Tạo câu chuyện đa phương tiện"),
        "createStory": MessageLookupByLibrary.simpleMessage("Tạo câu chuyện"),
        "createTextStory":
            MessageLookupByLibrary.simpleMessage("Tạo câu chuyện văn bản"),
        "createYourStory":
            MessageLookupByLibrary.simpleMessage("Tạo câu chuyện của bạn"),
        "createdAt": MessageLookupByLibrary.simpleMessage("Đã tạo vào ngày"),
        "creator": MessageLookupByLibrary.simpleMessage("Người tạo"),
        "currentDevice":
            MessageLookupByLibrary.simpleMessage("Thiết bị hiện tại"),
        "dashboard": MessageLookupByLibrary.simpleMessage("Bảng điều khiển"),
        "dataPrivacy":
            MessageLookupByLibrary.simpleMessage("Quyền riêng tư dữ liệu"),
        "delete": MessageLookupByLibrary.simpleMessage("Xóa"),
        "deleteChat":
            MessageLookupByLibrary.simpleMessage("Xóa cuộc trò chuyện"),
        "deleteFromAll":
            MessageLookupByLibrary.simpleMessage("Xóa khỏi tất cả"),
        "deleteFromMe": MessageLookupByLibrary.simpleMessage("Xóa khỏi tôi"),
        "deleteMember": MessageLookupByLibrary.simpleMessage("Xóa thành viên"),
        "deleteMyAccount":
            MessageLookupByLibrary.simpleMessage("Xóa tài khoản của tôi"),
        "deleteThisDeviceDesc": MessageLookupByLibrary.simpleMessage(
            "Xóa thiết bị này có nghĩa là đăng xuất ngay lập tức khỏi thiết bị này"),
        "deleteUser": MessageLookupByLibrary.simpleMessage("Xóa người dùng"),
        "deleteYouCopy":
            MessageLookupByLibrary.simpleMessage("Xóa bản sao của bạn"),
        "deleted": MessageLookupByLibrary.simpleMessage("Đã xóa"),
        "deletedAt": MessageLookupByLibrary.simpleMessage("Đã xóa vào ngày"),
        "delivered": MessageLookupByLibrary.simpleMessage("Đã gửi"),
        "description": MessageLookupByLibrary.simpleMessage("Mô tả"),
        "descriptionIsRequired":
            MessageLookupByLibrary.simpleMessage("Yêu cầu mô tả"),
        "desktopAndOtherDevices": MessageLookupByLibrary.simpleMessage(
            "Máy tính để bàn và các thiết bị khác"),
        "deviceHasBeenLogoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage(
                "Thiết bị đã đăng xuất khỏi tất cả các thiết bị"),
        "deviceStatus":
            MessageLookupByLibrary.simpleMessage("Trạng thái thiết bị"),
        "devices": MessageLookupByLibrary.simpleMessage("Thiết bị"),
        "directChat":
            MessageLookupByLibrary.simpleMessage("Trò chuyện trực tiếp"),
        "directRooms":
            MessageLookupByLibrary.simpleMessage("Phòng trò chuyện trực tiếp"),
        "dismissedToMemberBy": MessageLookupByLibrary.simpleMessage(
            "Bị giáng chức thành thành viên bởi"),
        "dismissesToMember":
            MessageLookupByLibrary.simpleMessage("Giáng chức thành thành viên"),
        "docs": MessageLookupByLibrary.simpleMessage("Tài liệu"),
        "done": MessageLookupByLibrary.simpleMessage("Xong"),
        "download": MessageLookupByLibrary.simpleMessage("Tải xuống"),
        "downloading":
            MessageLookupByLibrary.simpleMessage("Đang tải xuống..."),
        "edit": MessageLookupByLibrary.simpleMessage("Chỉnh sửa"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "emailMustBeValid":
            MessageLookupByLibrary.simpleMessage("Email phải hợp lệ"),
        "emailNotValid":
            MessageLookupByLibrary.simpleMessage("Email không hợp lệ"),
        "enterNameAndAddOptionalProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Nhập tên của bạn và thêm một hình đại diện tùy chọn"),
        "error": MessageLookupByLibrary.simpleMessage("Lỗi"),
        "exitGroup": MessageLookupByLibrary.simpleMessage("Thoát khỏi nhóm"),
        "explainWhatHappens": MessageLookupByLibrary.simpleMessage(
            "Hãy giải thích điều gì xảy ra ở đây"),
        "feedBackEmail": MessageLookupByLibrary.simpleMessage("Email phản hồi"),
        "fileHasBeenSavedTo":
            MessageLookupByLibrary.simpleMessage("Tệp đã được lưu tại"),
        "fileMessages": MessageLookupByLibrary.simpleMessage("Tin nhắn tệp"),
        "files": MessageLookupByLibrary.simpleMessage("Tệp"),
        "finished": MessageLookupByLibrary.simpleMessage("Hoàn thành"),
        "forRequest": MessageLookupByLibrary.simpleMessage("Theo yêu cầu"),
        "forgetPassword": MessageLookupByLibrary.simpleMessage("Quên mật khẩu"),
        "forgetPasswordExpireTime": MessageLookupByLibrary.simpleMessage(
            "Thời gian hết hạn quên mật khẩu"),
        "forward": MessageLookupByLibrary.simpleMessage("Chuyển tiếp"),
        "fullName": MessageLookupByLibrary.simpleMessage("Họ và tên"),
        "gallery": MessageLookupByLibrary.simpleMessage("Thư viện ảnh"),
        "globalSearch":
            MessageLookupByLibrary.simpleMessage("Tìm kiếm toàn cầu"),
        "googlePlayAppUrl":
            MessageLookupByLibrary.simpleMessage("URL cửa hàng Google Play"),
        "group": MessageLookupByLibrary.simpleMessage("Nhóm"),
        "groupCreatedBy":
            MessageLookupByLibrary.simpleMessage("Nhóm được tạo bởi"),
        "groupDescription": MessageLookupByLibrary.simpleMessage("Mô tả nhóm"),
        "groupIcon": MessageLookupByLibrary.simpleMessage("Biểu tượng nhóm"),
        "groupInfo": MessageLookupByLibrary.simpleMessage("Thông tin nhóm"),
        "groupMembers": MessageLookupByLibrary.simpleMessage("Thành viên nhóm"),
        "groupName": MessageLookupByLibrary.simpleMessage("Tên nhóm"),
        "groupParticipants":
            MessageLookupByLibrary.simpleMessage("Người tham gia nhóm"),
        "groupSettings": MessageLookupByLibrary.simpleMessage("Cài đặt nhóm"),
        "groupWith": MessageLookupByLibrary.simpleMessage("Nhóm với"),
        "harassmentOrBullyingDescription": MessageLookupByLibrary.simpleMessage(
            "Quấy rối hoặc bắt nạt: Tùy chọn này cho phép người dùng báo cáo những người đang nhắm mục tiêu họ hoặc người khác bằng các tin nhắn quấy rối, đe dọa hoặc các hình thức khác của bắt nạt."),
        "help": MessageLookupByLibrary.simpleMessage("Trợ giúp"),
        "hiIamUse":
            MessageLookupByLibrary.simpleMessage("Xin chào, tôi sử dụng"),
        "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Nếu tùy chọn này bị vô hiệu hóa, việc tạo phát sóng trong trò chuyện sẽ bị chặn"),
        "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Nếu tùy chọn này bị vô hiệu hóa, việc tạo nhóm trò chuyện sẽ bị chặn"),
        "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Nếu tùy chọn này bị vô hiệu hóa, việc đăng nhập hoặc đăng ký trên máy tính để bàn (Windows và macOS) sẽ bị chặn"),
        "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly":
            MessageLookupByLibrary.simpleMessage(
                "Nếu tùy chọn này được bật, quảng cáo Google sẽ xuất hiện trong trò chuyện"),
        "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Nếu tùy chọn này bị vô hiệu hóa, việc gửi tệp tin, hình ảnh, video và vị trí trong trò chuyện sẽ bị chặn"),
        "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Nếu tùy chọn này bị vô hiệu hóa, việc đăng nhập hoặc đăng ký trực tuyến sẽ bị chặn"),
        "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed":
            MessageLookupByLibrary.simpleMessage(
                "Nếu tùy chọn này được bật, cuộc gọi video và thoại sẽ được phép"),
        "image": MessageLookupByLibrary.simpleMessage("Hình ảnh"),
        "imageMessages":
            MessageLookupByLibrary.simpleMessage("Tin nhắn hình ảnh"),
        "images": MessageLookupByLibrary.simpleMessage("Hình ảnh"),
        "inAppAlerts":
            MessageLookupByLibrary.simpleMessage("Thông báo trong ứng dụng"),
        "inCall": MessageLookupByLibrary.simpleMessage("Trong cuộc gọi"),
        "inappropriateContentDescription": MessageLookupByLibrary.simpleMessage(
            "Nội dung không thích hợp: Người dùng có thể chọn tùy chọn này để báo cáo bất kỳ nội dung nào có tính chất tình dục, lời nói căm thù hoặc nội dung khác vi phạm các tiêu chuẩn của cộng đồng."),
        "info": MessageLookupByLibrary.simpleMessage("Thông tin"),
        "infoMessages":
            MessageLookupByLibrary.simpleMessage("Tin nhắn thông tin"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("Mã không hợp lệ"),
        "invalidLoginData": MessageLookupByLibrary.simpleMessage(
            "Dữ liệu đăng nhập không hợp lệ"),
        "ios": MessageLookupByLibrary.simpleMessage("iOS"),
        "joinedAt": MessageLookupByLibrary.simpleMessage("Tham gia vào ngày"),
        "joinedBy": MessageLookupByLibrary.simpleMessage("Tham gia bởi"),
        "kickMember": MessageLookupByLibrary.simpleMessage("Đá ra khỏi nhóm"),
        "kickedBy":
            MessageLookupByLibrary.simpleMessage("Bị đá ra khỏi nhóm bởi"),
        "language": MessageLookupByLibrary.simpleMessage("Ngôn ngữ"),
        "lastActiveFrom":
            MessageLookupByLibrary.simpleMessage("Hoạt động lần cuối từ"),
        "leaveGroup": MessageLookupByLibrary.simpleMessage("Rời nhóm"),
        "leaveGroupAndDeleteYourMessageCopy":
            MessageLookupByLibrary.simpleMessage(
                "Rời khỏi nhóm và xóa bản sao tin nhắn của bạn"),
        "leftTheGroup": MessageLookupByLibrary.simpleMessage("Rời khỏi nhóm"),
        "linkADeviceSoon":
            MessageLookupByLibrary.simpleMessage("Liên kết thiết bị (Sắp tới)"),
        "linkByQrCode":
            MessageLookupByLibrary.simpleMessage("Liên kết bằng mã QR"),
        "linkedDevices":
            MessageLookupByLibrary.simpleMessage("Thiết bị đã liên kết"),
        "links": MessageLookupByLibrary.simpleMessage("Liên kết"),
        "loading": MessageLookupByLibrary.simpleMessage("Đang tải ..."),
        "location": MessageLookupByLibrary.simpleMessage("Vị trí"),
        "locationMessages":
            MessageLookupByLibrary.simpleMessage("Tin nhắn vị trí"),
        "logOut": MessageLookupByLibrary.simpleMessage("Đăng xuất"),
        "login": MessageLookupByLibrary.simpleMessage("Đăng nhập"),
        "loginAgain": MessageLookupByLibrary.simpleMessage("Đăng nhập lại!"),
        "loginNowAllowedNowPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Hiện tại đăng nhập đã được phép. Vui lòng thử lại sau."),
        "logoutFromAllDevices": MessageLookupByLibrary.simpleMessage(
            "Đăng xuất khỏi tất cả thiết bị?"),
        "macOs": MessageLookupByLibrary.simpleMessage("macOS"),
        "makeCall": MessageLookupByLibrary.simpleMessage("Thực hiện cuộc gọi"),
        "media": MessageLookupByLibrary.simpleMessage("Phương tiện"),
        "mediaLinksAndDocs": MessageLookupByLibrary.simpleMessage(
            "Phương tiện, Liên kết và Tài liệu"),
        "member": MessageLookupByLibrary.simpleMessage("Thành viên"),
        "members": MessageLookupByLibrary.simpleMessage("Thành viên"),
        "messageCounter":
            MessageLookupByLibrary.simpleMessage("Bộ đếm tin nhắn"),
        "messageHasBeenDeleted":
            MessageLookupByLibrary.simpleMessage("Tin nhắn đã bị xóa"),
        "messageHasBeenViewed":
            MessageLookupByLibrary.simpleMessage("Tin nhắn đã được xem"),
        "messageInfo":
            MessageLookupByLibrary.simpleMessage("Thông tin tin nhắn"),
        "messages": MessageLookupByLibrary.simpleMessage("Tin nhắn"),
        "microphoneAndCameraPermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone and camera permission must be accepted"),
        "microphonePermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone permission must be accepted"),
        "minutes": MessageLookupByLibrary.simpleMessage("Phút"),
        "more": MessageLookupByLibrary.simpleMessage("Thêm"),
        "mute": MessageLookupByLibrary.simpleMessage("Tắt thông báo"),
        "muteNotifications":
            MessageLookupByLibrary.simpleMessage("Tắt thông báo"),
        "myPrivacy":
            MessageLookupByLibrary.simpleMessage("Quyền riêng tư của tôi"),
        "name": MessageLookupByLibrary.simpleMessage("Tên"),
        "nameMustHaveValue":
            MessageLookupByLibrary.simpleMessage("Tên phải có giá trị"),
        "needNewAccount":
            MessageLookupByLibrary.simpleMessage("Cần tạo tài khoản mới?"),
        "newBroadcast": MessageLookupByLibrary.simpleMessage("Phát sóng mới"),
        "newGroup": MessageLookupByLibrary.simpleMessage("Nhóm mới"),
        "newPassword": MessageLookupByLibrary.simpleMessage("Mật khẩu mới"),
        "newPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu mới phải có giá trị"),
        "newUpdateIsAvailable":
            MessageLookupByLibrary.simpleMessage("Có bản cập nhật mới"),
        "next": MessageLookupByLibrary.simpleMessage("Tiếp theo"),
        "nickname": MessageLookupByLibrary.simpleMessage("Biệt danh"),
        "no": MessageLookupByLibrary.simpleMessage("Không"),
        "noBio": MessageLookupByLibrary.simpleMessage(
            "Không có giới thiệu bản thân"),
        "noCodeHasBeenSendToYouToVerifyYourEmail":
            MessageLookupByLibrary.simpleMessage(
                "Không có mã đã được gửi cho bạn để xác minh email của bạn"),
        "noUpdatesAvailableNow": MessageLookupByLibrary.simpleMessage(
            "Không có cập nhật nào có sẵn bây giờ"),
        "none": MessageLookupByLibrary.simpleMessage("Không"),
        "notAccepted": MessageLookupByLibrary.simpleMessage("Không chấp nhận"),
        "notification": MessageLookupByLibrary.simpleMessage("Thông báo"),
        "notificationDescription":
            MessageLookupByLibrary.simpleMessage("Mô tả thông báo"),
        "notificationTitle":
            MessageLookupByLibrary.simpleMessage("Tiêu đề thông báo"),
        "notificationsPage":
            MessageLookupByLibrary.simpleMessage("Trang thông báo"),
        "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion":
            MessageLookupByLibrary.simpleMessage(
                "Bây giờ bạn đăng nhập dưới dạng quản trị viên chỉ đọc. Tất cả chỉnh sửa bạn thực hiện sẽ không được áp dụng vì đây là phiên bản thử nghiệm."),
        "off": MessageLookupByLibrary.simpleMessage("Tắt"),
        "offline": MessageLookupByLibrary.simpleMessage("Ngoại tuyến"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "oldPassword": MessageLookupByLibrary.simpleMessage("Mật khẩu cũ"),
        "on": MessageLookupByLibrary.simpleMessage("Bật"),
        "oneSeenMessage":
            MessageLookupByLibrary.simpleMessage("Một tin nhắn đã xem"),
        "online": MessageLookupByLibrary.simpleMessage("Trực tuyến"),
        "orLoginWith":
            MessageLookupByLibrary.simpleMessage("Hoặc đăng nhập bằng"),
        "other": MessageLookupByLibrary.simpleMessage("Khác"),
        "otherCategoryDescription": MessageLookupByLibrary.simpleMessage(
            "Khác: Danh mục tổng hợp này có thể được sử dụng cho vi phạm không dễ dàng rơi vào các danh mục trên. Có thể hữu ích nếu bao gồm một ô văn bản cho người dùng cung cấp thông tin bổ sung."),
        "otpCode": MessageLookupByLibrary.simpleMessage("Mã OTP"),
        "password": MessageLookupByLibrary.simpleMessage("Mật khẩu"),
        "passwordHasBeenChanged":
            MessageLookupByLibrary.simpleMessage("Mật khẩu đã được thay đổi"),
        "passwordIsRequired":
            MessageLookupByLibrary.simpleMessage("Yêu cầu mật khẩu"),
        "passwordMustHaveValue":
            MessageLookupByLibrary.simpleMessage("Mật khẩu phải có giá trị"),
        "passwordNotMatch":
            MessageLookupByLibrary.simpleMessage("Mật khẩu không khớp"),
        "peerUserDeviceOffline": MessageLookupByLibrary.simpleMessage(
            "Thiết bị của người dùng đối tác ngoại tuyến"),
        "peerUserInCallNow": MessageLookupByLibrary.simpleMessage(
            "Người dùng đang trong cuộc gọi"),
        "pending": MessageLookupByLibrary.simpleMessage("Đang chờ"),
        "phone": MessageLookupByLibrary.simpleMessage("Điện thoại"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Chính sách quyền riêng tư"),
        "privacyUrl":
            MessageLookupByLibrary.simpleMessage("URL chính sách bảo mật"),
        "profile": MessageLookupByLibrary.simpleMessage("Hồ sơ"),
        "promotedToAdminBy": MessageLookupByLibrary.simpleMessage(
            "Được thăng cấp thành quản trị viên bởi"),
        "public": MessageLookupByLibrary.simpleMessage("Công khai"),
        "read": MessageLookupByLibrary.simpleMessage("Đã đọc"),
        "recentUpdate":
            MessageLookupByLibrary.simpleMessage("Cập nhật gần đây"),
        "recentUpdates":
            MessageLookupByLibrary.simpleMessage("Cập nhật gần đây"),
        "recording": MessageLookupByLibrary.simpleMessage("Đang ghi âm..."),
        "register": MessageLookupByLibrary.simpleMessage("Đăng ký"),
        "registerMethod":
            MessageLookupByLibrary.simpleMessage("Phương thức đăng ký"),
        "registerStatus":
            MessageLookupByLibrary.simpleMessage("Trạng thái đăng ký"),
        "rejected": MessageLookupByLibrary.simpleMessage("Đã từ chối"),
        "repliedToYourSelf":
            MessageLookupByLibrary.simpleMessage("Đã trả lời cho chính bạn"),
        "reply": MessageLookupByLibrary.simpleMessage("Trả lời"),
        "replyToYourSelf":
            MessageLookupByLibrary.simpleMessage("Trả lời cho chính bạn"),
        "report": MessageLookupByLibrary.simpleMessage("Báo cáo"),
        "reportHasBeenSubmitted":
            MessageLookupByLibrary.simpleMessage("Báo cáo của bạn đã được gửi"),
        "reportUser":
            MessageLookupByLibrary.simpleMessage("Báo cáo người dùng"),
        "reports": MessageLookupByLibrary.simpleMessage("Báo cáo"),
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("Đặt lại mật khẩu"),
        "retry": MessageLookupByLibrary.simpleMessage("Thử lại"),
        "ring": MessageLookupByLibrary.simpleMessage("Nhạc chuông"),
        "roomAlreadyInCall":
            MessageLookupByLibrary.simpleMessage("Phòng đã trong cuộc gọi"),
        "roomCounter": MessageLookupByLibrary.simpleMessage("Bộ đếm phòng"),
        "saveLogin": MessageLookupByLibrary.simpleMessage("Lưu đăng nhập"),
        "search": MessageLookupByLibrary.simpleMessage("Tìm kiếm"),
        "seconds": MessageLookupByLibrary.simpleMessage("giây"),
        "send": MessageLookupByLibrary.simpleMessage("Gửi"),
        "sendCodeToMyEmail":
            MessageLookupByLibrary.simpleMessage("Gửi mã đến email của tôi"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Gửi tin nhắn"),
        "sessionEnd": MessageLookupByLibrary.simpleMessage("Kết thúc phiên"),
        "setMaxBroadcastMembers": MessageLookupByLibrary.simpleMessage(
            "Thiết lập số lượng tối đa thành viên truyền hình"),
        "setMaxGroupMembers": MessageLookupByLibrary.simpleMessage(
            "Thiết lập số lượng tối đa thành viên nhóm"),
        "setMaxMessageForwardAndShare": MessageLookupByLibrary.simpleMessage(
            "Thiết lập số lượng tối đa tin nhắn chuyển tiếp và chia sẻ"),
        "setNewPrivacyPolicyUrl": MessageLookupByLibrary.simpleMessage(
            "Thiết lập URL chính sách bảo mật mới"),
        "setToAdmin":
            MessageLookupByLibrary.simpleMessage("Thiết lập là quản trị viên"),
        "settings": MessageLookupByLibrary.simpleMessage("Cài đặt"),
        "share": MessageLookupByLibrary.simpleMessage("Chia sẻ"),
        "shareMediaAndLocation": MessageLookupByLibrary.simpleMessage(
            "Chia sẻ phương tiện và vị trí"),
        "shareYourStatus":
            MessageLookupByLibrary.simpleMessage("Chia sẻ trạng thái của bạn"),
        "showHistory": MessageLookupByLibrary.simpleMessage("Hiển thị lịch sử"),
        "showMedia":
            MessageLookupByLibrary.simpleMessage("Hiển thị phương tiện"),
        "soon": MessageLookupByLibrary.simpleMessage("Sớm"),
        "spamOrScamDescription": MessageLookupByLibrary.simpleMessage(
            "Spam hoặc Lừa đảo: Tùy chọn này dành cho người dùng báo cáo các tài khoản đang gửi tin nhắn rác, quảng cáo không mời hoặc đang cố gắng lừa đảo người khác."),
        "star": MessageLookupByLibrary.simpleMessage("Đánh dấu"),
        "starMessage":
            MessageLookupByLibrary.simpleMessage("Đánh dấu tin nhắn"),
        "starredMessage":
            MessageLookupByLibrary.simpleMessage("Tin nhắn được đánh dấu"),
        "starredMessages":
            MessageLookupByLibrary.simpleMessage("Tin nhắn được đánh dấu"),
        "startChat": MessageLookupByLibrary.simpleMessage("Bắt đầu trò chuyện"),
        "startNewChatWithYou": MessageLookupByLibrary.simpleMessage(
            "Bắt đầu trò chuyện mới với bạn"),
        "status": MessageLookupByLibrary.simpleMessage("Trạng thái"),
        "storageAndData":
            MessageLookupByLibrary.simpleMessage("Lưu trữ và Dữ liệu"),
        "stories": MessageLookupByLibrary.simpleMessage("Câu chuyện"),
        "storyCreatedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Câu chuyện đã được tạo thành công"),
        "success": MessageLookupByLibrary.simpleMessage("Thành công"),
        "successfullyDownloadedIn":
            MessageLookupByLibrary.simpleMessage("Tải xuống thành công trong"),
        "supportChatSoon":
            MessageLookupByLibrary.simpleMessage("Trò chuyện hỗ trợ (Sắp tới)"),
        "tapADeviceToEditOrLogOut": MessageLookupByLibrary.simpleMessage(
            "Chạm để chỉnh sửa hoặc đăng xuất khỏi thiết bị."),
        "tapForPhoto": MessageLookupByLibrary.simpleMessage("Chạm để chụp ảnh"),
        "tapToSelectAnIcon":
            MessageLookupByLibrary.simpleMessage("Chạm để chọn một biểu tượng"),
        "tellAFriend": MessageLookupByLibrary.simpleMessage("Kể cho bạn"),
        "textFieldHint":
            MessageLookupByLibrary.simpleMessage("Nhập tin nhắn ..."),
        "textMessages":
            MessageLookupByLibrary.simpleMessage("Tin nhắn văn bản"),
        "thereIsFileHasSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "Có tệp có kích thước lớn hơn kích thước cho phép"),
        "thereIsVideoSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "Có video có kích thước lớn hơn kích thước cho phép"),
        "timeout": MessageLookupByLibrary.simpleMessage("Hết thời gian"),
        "titleIsRequired":
            MessageLookupByLibrary.simpleMessage("Tiêu đề là bắt buộc"),
        "today": MessageLookupByLibrary.simpleMessage("Hôm nay"),
        "total": MessageLookupByLibrary.simpleMessage("Tổng cộng"),
        "totalMessages":
            MessageLookupByLibrary.simpleMessage("Tổng số tin nhắn"),
        "totalRooms": MessageLookupByLibrary.simpleMessage("Tổng số phòng"),
        "totalVisits":
            MessageLookupByLibrary.simpleMessage("Tổng số lượt truy cập"),
        "typing": MessageLookupByLibrary.simpleMessage("Đang nhập..."),
        "unBlock": MessageLookupByLibrary.simpleMessage("Bỏ chặn"),
        "unBlockUser":
            MessageLookupByLibrary.simpleMessage("Bỏ chặn người dùng"),
        "unMute": MessageLookupByLibrary.simpleMessage("Bật thông báo"),
        "unStar": MessageLookupByLibrary.simpleMessage("Bỏ đánh dấu"),
        "update": MessageLookupByLibrary.simpleMessage("Cập nhật"),
        "updateBroadcastTitle":
            MessageLookupByLibrary.simpleMessage("Cập nhật tiêu đề phát sóng"),
        "updateFeedBackEmail":
            MessageLookupByLibrary.simpleMessage("Cập nhật Email phản hồi"),
        "updateGroupDescription":
            MessageLookupByLibrary.simpleMessage("Cập nhật mô tả nhóm"),
        "updateGroupDescriptionWillUpdateAllGroupMembers":
            MessageLookupByLibrary.simpleMessage(
                "Cập nhật mô tả nhóm sẽ cập nhật cho tất cả thành viên nhóm"),
        "updateGroupTitle":
            MessageLookupByLibrary.simpleMessage("Cập nhật tiêu đề nhóm"),
        "updateImage": MessageLookupByLibrary.simpleMessage("Cập nhật ảnh"),
        "updateNickname":
            MessageLookupByLibrary.simpleMessage("Cập nhật biệt danh"),
        "updateTitle": MessageLookupByLibrary.simpleMessage("Cập nhật tiêu đề"),
        "updateTitleTo":
            MessageLookupByLibrary.simpleMessage("Cập nhật tiêu đề thành"),
        "updateYourBio":
            MessageLookupByLibrary.simpleMessage("Cập nhật tiểu sử của bạn"),
        "updateYourName":
            MessageLookupByLibrary.simpleMessage("Cập nhật tên của bạn"),
        "updateYourPassword":
            MessageLookupByLibrary.simpleMessage("Cập nhật mật khẩu của bạn"),
        "updateYourProfile":
            MessageLookupByLibrary.simpleMessage("Cập nhật hồ sơ của bạn"),
        "updatedAt":
            MessageLookupByLibrary.simpleMessage("Đã cập nhật vào ngày"),
        "upgradeToAdmin": MessageLookupByLibrary.simpleMessage(
            "Nâng cấp thành quản trị viên"),
        "userAction":
            MessageLookupByLibrary.simpleMessage("Hành động của người dùng"),
        "userAlreadyRegister":
            MessageLookupByLibrary.simpleMessage("Người dùng đã đăng ký"),
        "userDeviceSessionEndDeviceDeleted":
            MessageLookupByLibrary.simpleMessage(
                "Phiên thiết bị người dùng đã kết thúc - Thiết bị đã bị xóa"),
        "userEmailNotFound": MessageLookupByLibrary.simpleMessage(
            "Không tìm thấy email người dùng"),
        "userInfo":
            MessageLookupByLibrary.simpleMessage("Thông tin người dùng"),
        "userPage": MessageLookupByLibrary.simpleMessage("Trang người dùng"),
        "userProfile": MessageLookupByLibrary.simpleMessage("Hồ sơ người dùng"),
        "userRegisterStatusNotAcceptedYet":
            MessageLookupByLibrary.simpleMessage(
                "Trạng thái đăng ký của người dùng chưa được chấp nhận"),
        "users": MessageLookupByLibrary.simpleMessage("Người dùng"),
        "usersAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Người dùng được thêm thành công"),
        "vMessageInfoTrans":
            MessageLookupByLibrary.simpleMessage("Thông tin tin nhắn"),
        "vMessagesInfoTrans":
            MessageLookupByLibrary.simpleMessage("Thông tin tin nhắn"),
        "verified": MessageLookupByLibrary.simpleMessage("Đã xác minh"),
        "verifiedAt": MessageLookupByLibrary.simpleMessage("Đã xác minh"),
        "video": MessageLookupByLibrary.simpleMessage("Video"),
        "videoCallMessages":
            MessageLookupByLibrary.simpleMessage("Tin nhắn cuộc gọi video"),
        "videoMessages": MessageLookupByLibrary.simpleMessage("Tin nhắn video"),
        "visits": MessageLookupByLibrary.simpleMessage("Lượt truy cập"),
        "voiceCallMessage":
            MessageLookupByLibrary.simpleMessage("Tin nhắn cuộc gọi thoại"),
        "voiceCallMessages":
            MessageLookupByLibrary.simpleMessage("Tin nhắn cuộc gọi thoại"),
        "voiceMessages": MessageLookupByLibrary.simpleMessage("Tin nhắn thoại"),
        "wait2MinutesToSendMail":
            MessageLookupByLibrary.simpleMessage("Chờ 2 phút để gửi mail"),
        "waitingList": MessageLookupByLibrary.simpleMessage("Danh sách chờ"),
        "weHighRecommendToDownloadThisUpdate":
            MessageLookupByLibrary.simpleMessage(
                "Chúng tôi rất khuyến nghị tải xuống bản cập nhật này"),
        "web": MessageLookupByLibrary.simpleMessage("Web"),
        "welcome": MessageLookupByLibrary.simpleMessage("Chào mừng"),
        "whenUsingMobileData":
            MessageLookupByLibrary.simpleMessage("Khi sử dụng dữ liệu di động"),
        "whenUsingWifi":
            MessageLookupByLibrary.simpleMessage("Khi sử dụng Wi-Fi"),
        "whileAuthCanFindYou": MessageLookupByLibrary.simpleMessage(
            "Trong quá trình xác thực không thể tìm thấy bạn"),
        "windows": MessageLookupByLibrary.simpleMessage("Windows"),
        "writeACaption":
            MessageLookupByLibrary.simpleMessage("Viết chú thích..."),
        "yes": MessageLookupByLibrary.simpleMessage("Có"),
        "yesterday": MessageLookupByLibrary.simpleMessage("Hôm qua"),
        "you": MessageLookupByLibrary.simpleMessage("Bạn"),
        "youAreAboutToDeleteThisUserFromYourList":
            MessageLookupByLibrary.simpleMessage(
                "Bạn đang chuẩn bị xóa người dùng này khỏi danh sách của bạn"),
        "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList":
            MessageLookupByLibrary.simpleMessage(
                "Bạn đang sắp xóa tài khoản của bạn. Tài khoản của bạn sẽ không xuất hiện lại trong danh sách người dùng"),
        "youAreAboutToDismissesToMember": MessageLookupByLibrary.simpleMessage(
            "Bạn đang chuẩn bị giáng chức thành viên"),
        "youAreAboutToKick": MessageLookupByLibrary.simpleMessage(
            "Bạn đang chuẩn bị đá ra khỏi nhóm"),
        "youAreAboutToUpgradeToAdmin": MessageLookupByLibrary.simpleMessage(
            "Bạn đang chuẩn bị thăng cấp thành quản trị viên"),
        "youDontHaveAccess":
            MessageLookupByLibrary.simpleMessage("Bạn không có quyền truy cập"),
        "youInPublicSearch": MessageLookupByLibrary.simpleMessage(
            "Bạn trong tìm kiếm công khai"),
        "youNotParticipantInThisGroup": MessageLookupByLibrary.simpleMessage(
            "Bạn không phải là thành viên trong nhóm này"),
        "yourAccountBlocked": MessageLookupByLibrary.simpleMessage(
            "Tài khoản của bạn đã bị chặn"),
        "yourAccountDeleted":
            MessageLookupByLibrary.simpleMessage("Tài khoản của bạn đã bị xóa"),
        "yourAccountIsUnderReview": MessageLookupByLibrary.simpleMessage(
            "Tài khoản của bạn đang được xem xét"),
        "yourAreAboutToLogoutFromThisAccount":
            MessageLookupByLibrary.simpleMessage(
                "Bạn sắp đăng xuất khỏi tài khoản này"),
        "yourLastSeen":
            MessageLookupByLibrary.simpleMessage("Lần cuối nhìn thấy"),
        "yourLastSeenInChats": MessageLookupByLibrary.simpleMessage(
            "Lần cuối nhìn thấy trong trò chuyện"),
        "yourProfileAppearsInPublicSearchAndAddingForGroups":
            MessageLookupByLibrary.simpleMessage(
                "Hồ sơ của bạn xuất hiện trong tìm kiếm công khai và thêm vào nhóm"),
        "yourSessionIsEndedPleaseLoginAgain":
            MessageLookupByLibrary.simpleMessage(
                "Phiên của bạn đã kết thúc, vui lòng đăng nhập lại!"),
        "yourStory": MessageLookupByLibrary.simpleMessage("Câu chuyện của bạn")
      };
}
