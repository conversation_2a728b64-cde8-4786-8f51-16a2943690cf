// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Dashboard": MessageLookupByLibrary.simpleMessage("Dashboard"),
        "about": MessageLookupByLibrary.simpleMessage("About"),
        "aboutToBlockUserWithConsequences": MessageLookupByLibrary.simpleMessage(
            "You are about to block this user. You can\'t send him chats and can\'t add him to groups or broadcast!"),
        "accepted": MessageLookupByLibrary.simpleMessage("Accepted"),
        "account": MessageLookupByLibrary.simpleMessage("Account"),
        "actions": MessageLookupByLibrary.simpleMessage("Actions"),
        "addMembers": MessageLookupByLibrary.simpleMessage("Add Members"),
        "addNewStory": MessageLookupByLibrary.simpleMessage("Add new story"),
        "addParticipants":
            MessageLookupByLibrary.simpleMessage("Add Participants"),
        "addedYouToNewBroadcast":
            MessageLookupByLibrary.simpleMessage("Added you to new broadcast"),
        "admin": MessageLookupByLibrary.simpleMessage("Admin"),
        "adminNotification":
            MessageLookupByLibrary.simpleMessage("Admin notification"),
        "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself":
            MessageLookupByLibrary.simpleMessage(
                "All data has been backup you don\\\'t want need to manage save the data by your self! if you logout and login again you will see all chats same for web version"),
        "allDeletedMessages":
            MessageLookupByLibrary.simpleMessage("All Deleted Messages"),
        "allowAds": MessageLookupByLibrary.simpleMessage("Allow Ads"),
        "allowCalls": MessageLookupByLibrary.simpleMessage("Allow Calls"),
        "allowCreateBroadcast":
            MessageLookupByLibrary.simpleMessage("Allow Create Broadcast"),
        "allowCreateGroups":
            MessageLookupByLibrary.simpleMessage("Allow Create Groups"),
        "allowDesktopLogin":
            MessageLookupByLibrary.simpleMessage("Allow Desktop Login"),
        "allowMobileLogin":
            MessageLookupByLibrary.simpleMessage("Allow Mobile Login"),
        "allowSendMedia":
            MessageLookupByLibrary.simpleMessage("Allow Send Media"),
        "allowWebLogin":
            MessageLookupByLibrary.simpleMessage("Allow Web Login"),
        "alreadyHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Already have an account?"),
        "android": MessageLookupByLibrary.simpleMessage("Android"),
        "appMembers": MessageLookupByLibrary.simpleMessage("App members"),
        "appleStoreAppUrl":
            MessageLookupByLibrary.simpleMessage("Apple Store App URL"),
        "areYouSure": MessageLookupByLibrary.simpleMessage("Are you sure?"),
        "areYouSureToBlock":
            MessageLookupByLibrary.simpleMessage("Are you sure to block"),
        "areYouSureToLeaveThisGroupThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "Are you sure to leave this group? This action can\'t undo"),
        "areYouSureToPermitYourCopyThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "Are you sure to permit your copy? This action can\'t undo"),
        "areYouSureToReportUserToAdmin": MessageLookupByLibrary.simpleMessage(
            "Are you sure to submit report about this user to the admin?"),
        "areYouSureToUnBlock":
            MessageLookupByLibrary.simpleMessage("Are you sure to un block"),
        "areYouWantToMakeVideoCall": MessageLookupByLibrary.simpleMessage(
            "Are you want to make video call?"),
        "areYouWantToMakeVoiceCall": MessageLookupByLibrary.simpleMessage(
            "Are you want to make voice call?"),
        "audio": MessageLookupByLibrary.simpleMessage("Audio"),
        "audioCall": MessageLookupByLibrary.simpleMessage("Audio call"),
        "back": MessageLookupByLibrary.simpleMessage("Back"),
        "banAt": MessageLookupByLibrary.simpleMessage("Ban at"),
        "banTo": MessageLookupByLibrary.simpleMessage("Ban To"),
        "bio": MessageLookupByLibrary.simpleMessage("Bio"),
        "block": MessageLookupByLibrary.simpleMessage("Block"),
        "blockUser": MessageLookupByLibrary.simpleMessage("Block user"),
        "blocked": MessageLookupByLibrary.simpleMessage("Blocked"),
        "blockedUsers": MessageLookupByLibrary.simpleMessage("Blocked users"),
        "broadcast": MessageLookupByLibrary.simpleMessage("Broadcast"),
        "broadcastInfo": MessageLookupByLibrary.simpleMessage("Broadcast info"),
        "broadcastMembers":
            MessageLookupByLibrary.simpleMessage("Broadcast Members"),
        "broadcastName": MessageLookupByLibrary.simpleMessage("Broadcast name"),
        "broadcastParticipants":
            MessageLookupByLibrary.simpleMessage("Broadcast Participants"),
        "broadcastSettings":
            MessageLookupByLibrary.simpleMessage("Broadcast settings"),
        "callNotAllowed":
            MessageLookupByLibrary.simpleMessage("Call not allowed"),
        "callTimeoutInSeconds":
            MessageLookupByLibrary.simpleMessage("Call Timeout in Seconds"),
        "calls": MessageLookupByLibrary.simpleMessage("Calls"),
        "camera": MessageLookupByLibrary.simpleMessage("Camera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "canceled": MessageLookupByLibrary.simpleMessage("Canceled"),
        "changeSubject": MessageLookupByLibrary.simpleMessage("Change subject"),
        "chat": MessageLookupByLibrary.simpleMessage("Chat"),
        "chats": MessageLookupByLibrary.simpleMessage("CHATS"),
        "checkForUpdates":
            MessageLookupByLibrary.simpleMessage("Check for updates"),
        "chooseAtLestOneMember":
            MessageLookupByLibrary.simpleMessage("Choose at lest one member"),
        "chooseHowAutomaticDownloadWorks": MessageLookupByLibrary.simpleMessage(
            "Choose how automatic download works"),
        "chooseRoom": MessageLookupByLibrary.simpleMessage("Choose room"),
        "clear": MessageLookupByLibrary.simpleMessage("Clear"),
        "clearCallsConfirm":
            MessageLookupByLibrary.simpleMessage("Clear calls confirm"),
        "clearChat": MessageLookupByLibrary.simpleMessage("Clear chat"),
        "clickToAddGroupDescription": MessageLookupByLibrary.simpleMessage(
            "Click to add group description"),
        "clickToSee": MessageLookupByLibrary.simpleMessage("Click to see"),
        "clickToSeeAllUserCountries": MessageLookupByLibrary.simpleMessage(
            "Click to see all user countries"),
        "clickToSeeAllUserDevicesDetails": MessageLookupByLibrary.simpleMessage(
            "Click to see all user devices details"),
        "clickToSeeAllUserInformations": MessageLookupByLibrary.simpleMessage(
            "Click to see all user information"),
        "clickToSeeAllUserMessagesDetails":
            MessageLookupByLibrary.simpleMessage(
                "Click to see all user messages details"),
        "clickToSeeAllUserReports": MessageLookupByLibrary.simpleMessage(
            "Click to see all user reports"),
        "clickToSeeAllUserRoomsDetails": MessageLookupByLibrary.simpleMessage(
            "Click to see all user rooms details"),
        "close": MessageLookupByLibrary.simpleMessage("Close"),
        "codeHasBeenExpired":
            MessageLookupByLibrary.simpleMessage("Code has been expired"),
        "codeMustEqualToSixNumbers": MessageLookupByLibrary.simpleMessage(
            "Code must equal to six numbers"),
        "configureYourAccountPrivacy": MessageLookupByLibrary.simpleMessage(
            "Configure your account privacy"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirm password"),
        "confirmPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Confirm password must have value"),
        "congregationsYourAccountHasBeenAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Congregations your account has been accepted"),
        "connecting": MessageLookupByLibrary.simpleMessage("Connecting..."),
        "contactInfo": MessageLookupByLibrary.simpleMessage("Contact info"),
        "contactUs": MessageLookupByLibrary.simpleMessage("Contact Us"),
        "copy": MessageLookupByLibrary.simpleMessage("Copy"),
        "countries": MessageLookupByLibrary.simpleMessage("Countries"),
        "country": MessageLookupByLibrary.simpleMessage("Country"),
        "create": MessageLookupByLibrary.simpleMessage("Create"),
        "createBroadcast":
            MessageLookupByLibrary.simpleMessage("Create Broadcast"),
        "createGroup": MessageLookupByLibrary.simpleMessage("Create Group"),
        "createMediaStory":
            MessageLookupByLibrary.simpleMessage("Create Media Story"),
        "createStory": MessageLookupByLibrary.simpleMessage("Create Story"),
        "createTextStory":
            MessageLookupByLibrary.simpleMessage("Create Text Story"),
        "createYourStory":
            MessageLookupByLibrary.simpleMessage("Create your story"),
        "createdAt": MessageLookupByLibrary.simpleMessage("Created At"),
        "creator": MessageLookupByLibrary.simpleMessage("Creator"),
        "currentDevice": MessageLookupByLibrary.simpleMessage("Current device"),
        "dashboard": MessageLookupByLibrary.simpleMessage("Dashboard"),
        "dataPrivacy": MessageLookupByLibrary.simpleMessage("Data privacy"),
        "delete": MessageLookupByLibrary.simpleMessage("Delete"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("Delete chat"),
        "deleteFromAll":
            MessageLookupByLibrary.simpleMessage("Delete from all"),
        "deleteFromMe": MessageLookupByLibrary.simpleMessage("Delete from me"),
        "deleteMember": MessageLookupByLibrary.simpleMessage("Delete member"),
        "deleteMyAccount":
            MessageLookupByLibrary.simpleMessage("Delete my account"),
        "deleteThisDeviceDesc": MessageLookupByLibrary.simpleMessage(
            "Deleting this device means instantly logout this device"),
        "deleteUser": MessageLookupByLibrary.simpleMessage("Delete user"),
        "deleteYouCopy":
            MessageLookupByLibrary.simpleMessage("Delete your copy"),
        "deleted": MessageLookupByLibrary.simpleMessage("Deleted"),
        "deletedAt": MessageLookupByLibrary.simpleMessage("Deleted At"),
        "delivered": MessageLookupByLibrary.simpleMessage("Delivered"),
        "description": MessageLookupByLibrary.simpleMessage("Description"),
        "descriptionIsRequired":
            MessageLookupByLibrary.simpleMessage("Description is required"),
        "desktopAndOtherDevices":
            MessageLookupByLibrary.simpleMessage("Desktop, and other devices"),
        "deviceHasBeenLogoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage(
                "Device has been logout from all devices"),
        "deviceStatus": MessageLookupByLibrary.simpleMessage("Device status"),
        "devices": MessageLookupByLibrary.simpleMessage("Devices"),
        "directChat": MessageLookupByLibrary.simpleMessage("Direct Chat"),
        "directRooms": MessageLookupByLibrary.simpleMessage("Direct Rooms"),
        "dismissedToMemberBy":
            MessageLookupByLibrary.simpleMessage("Dismissed to member by"),
        "dismissesToMember":
            MessageLookupByLibrary.simpleMessage("Dismisses to member"),
        "docs": MessageLookupByLibrary.simpleMessage("Docs"),
        "done": MessageLookupByLibrary.simpleMessage("Done"),
        "download": MessageLookupByLibrary.simpleMessage("Download"),
        "downloading": MessageLookupByLibrary.simpleMessage("Downloading..."),
        "edit": MessageLookupByLibrary.simpleMessage("Edit"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "emailMustBeValid":
            MessageLookupByLibrary.simpleMessage("Email must be valid"),
        "emailNotValid":
            MessageLookupByLibrary.simpleMessage("Email not valid"),
        "enterNameAndAddOptionalProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Enter your name and add an optional profile picture"),
        "error": MessageLookupByLibrary.simpleMessage("Error"),
        "exitGroup": MessageLookupByLibrary.simpleMessage("Exit Group"),
        "explainWhatHappens":
            MessageLookupByLibrary.simpleMessage("Explain here what happens"),
        "feedBackEmail": MessageLookupByLibrary.simpleMessage("Feedback Email"),
        "fileHasBeenSavedTo":
            MessageLookupByLibrary.simpleMessage("File has been saved to"),
        "fileMessages": MessageLookupByLibrary.simpleMessage("File Messages"),
        "files": MessageLookupByLibrary.simpleMessage("Files"),
        "finished": MessageLookupByLibrary.simpleMessage("Finished"),
        "forRequest": MessageLookupByLibrary.simpleMessage("For request"),
        "forgetPassword":
            MessageLookupByLibrary.simpleMessage("Forget Password?"),
        "forgetPasswordExpireTime":
            MessageLookupByLibrary.simpleMessage("Forget Password Expire Time"),
        "forward": MessageLookupByLibrary.simpleMessage("Forward"),
        "fullName": MessageLookupByLibrary.simpleMessage("Full Name"),
        "gallery": MessageLookupByLibrary.simpleMessage("Gallery"),
        "globalSearch": MessageLookupByLibrary.simpleMessage("Global Search"),
        "googlePlayAppUrl":
            MessageLookupByLibrary.simpleMessage("Google Play App URL"),
        "group": MessageLookupByLibrary.simpleMessage("Group"),
        "groupCreatedBy":
            MessageLookupByLibrary.simpleMessage("Group created by"),
        "groupDescription":
            MessageLookupByLibrary.simpleMessage("Group description"),
        "groupIcon": MessageLookupByLibrary.simpleMessage("Group icon"),
        "groupInfo": MessageLookupByLibrary.simpleMessage("Group info"),
        "groupMembers": MessageLookupByLibrary.simpleMessage("Group Members"),
        "groupName": MessageLookupByLibrary.simpleMessage("group name"),
        "groupParticipants":
            MessageLookupByLibrary.simpleMessage("Group Participants"),
        "groupSettings": MessageLookupByLibrary.simpleMessage("Group settings"),
        "groupWith": MessageLookupByLibrary.simpleMessage("Group with"),
        "harassmentOrBullyingDescription": MessageLookupByLibrary.simpleMessage(
            "Harassment or Bullying: This option allows users to report individuals who are targeting them or others with harassing messages, threats, or other forms of bullying."),
        "help": MessageLookupByLibrary.simpleMessage("Help"),
        "hiIamUse": MessageLookupByLibrary.simpleMessage("Hi iam using"),
        "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "If this option is disabled, creating chat broadcast will be blocked."),
        "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "If this option is disabled, creating chat groups will be blocked."),
        "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "If this option is disabled, the desktop login or register (Windows, Mac) will be blocked."),
        "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly":
            MessageLookupByLibrary.simpleMessage(
                "If this option is disabled, the mobile login or register will be blocked on Android and iOS only."),
        "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "If this option is disabled, sending chat files, images, videos, and location will be blocked."),
        "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "If this option is disabled, the web login or register will be blocked."),
        "ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats":
            MessageLookupByLibrary.simpleMessage(
                "If this option is enabled, the Google Ads banner will appear in chats."),
        "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed":
            MessageLookupByLibrary.simpleMessage(
                "If this option is enabled, the video and voice call will be allowed."),
        "image": MessageLookupByLibrary.simpleMessage("Image"),
        "imageMessages": MessageLookupByLibrary.simpleMessage("Image Messages"),
        "images": MessageLookupByLibrary.simpleMessage("Images"),
        "inAppAlerts": MessageLookupByLibrary.simpleMessage("In app alerts"),
        "inCall": MessageLookupByLibrary.simpleMessage("In call"),
        "inappropriateContentDescription": MessageLookupByLibrary.simpleMessage(
            "Inappropriate Content: Users can select this option to report any sexually explicit material, hate speech, or other content that violates community standards."),
        "info": MessageLookupByLibrary.simpleMessage("Info"),
        "infoMessages": MessageLookupByLibrary.simpleMessage("Info Messages"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("Invalid code"),
        "invalidLoginData":
            MessageLookupByLibrary.simpleMessage("Invalid login data"),
        "ios": MessageLookupByLibrary.simpleMessage("iOS"),
        "joinedAt": MessageLookupByLibrary.simpleMessage("Joined At"),
        "joinedBy": MessageLookupByLibrary.simpleMessage("Joined by"),
        "kickMember": MessageLookupByLibrary.simpleMessage("Kick member"),
        "kickedBy": MessageLookupByLibrary.simpleMessage("Kicked by"),
        "language": MessageLookupByLibrary.simpleMessage("Language"),
        "lastActiveFrom":
            MessageLookupByLibrary.simpleMessage("Last active from"),
        "leaveGroup": MessageLookupByLibrary.simpleMessage("Leave group"),
        "leaveGroupAndDeleteYourMessageCopy":
            MessageLookupByLibrary.simpleMessage(
                "Leave group and delete your message copy"),
        "leftTheGroup": MessageLookupByLibrary.simpleMessage("Left the group"),
        "linkADeviceSoon":
            MessageLookupByLibrary.simpleMessage("Link a Device (Soon)"),
        "linkByQrCode": MessageLookupByLibrary.simpleMessage("Link By Qr Code"),
        "linkedDevices": MessageLookupByLibrary.simpleMessage("Linked Devices"),
        "links": MessageLookupByLibrary.simpleMessage("Links"),
        "loading": MessageLookupByLibrary.simpleMessage("Loading ..."),
        "location": MessageLookupByLibrary.simpleMessage("Location"),
        "locationMessages":
            MessageLookupByLibrary.simpleMessage("Location Messages"),
        "logOut": MessageLookupByLibrary.simpleMessage("Log out"),
        "login": MessageLookupByLibrary.simpleMessage("Login"),
        "loginAgain": MessageLookupByLibrary.simpleMessage("Login again!"),
        "loginNowAllowedNowPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Login now allowed. Please try again later."),
        "logoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage("Logout from all devices?"),
        "macOs": MessageLookupByLibrary.simpleMessage("macOS"),
        "makeCall": MessageLookupByLibrary.simpleMessage("Make call"),
        "media": MessageLookupByLibrary.simpleMessage("Media"),
        "mediaLinksAndDocs":
            MessageLookupByLibrary.simpleMessage("Media, Links, and Docs"),
        "member": MessageLookupByLibrary.simpleMessage("Member"),
        "members": MessageLookupByLibrary.simpleMessage("Members"),
        "messageCounter":
            MessageLookupByLibrary.simpleMessage("Message Counter"),
        "messageHasBeenDeleted":
            MessageLookupByLibrary.simpleMessage("Message has been deleted"),
        "messageHasBeenViewed":
            MessageLookupByLibrary.simpleMessage("Message has been viewed"),
        "messageInfo": MessageLookupByLibrary.simpleMessage("Message info"),
        "messages": MessageLookupByLibrary.simpleMessage("Messages"),
        "microphoneAndCameraPermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone and camera permission must be accepted"),
        "microphonePermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone permission must be accepted"),
        "minutes": MessageLookupByLibrary.simpleMessage("Minutes"),
        "more": MessageLookupByLibrary.simpleMessage("More"),
        "mute": MessageLookupByLibrary.simpleMessage("Mute"),
        "muteNotifications":
            MessageLookupByLibrary.simpleMessage("Mute notifications"),
        "myPrivacy": MessageLookupByLibrary.simpleMessage("My Privacy"),
        "name": MessageLookupByLibrary.simpleMessage("Name"),
        "nameMustHaveValue":
            MessageLookupByLibrary.simpleMessage("Name must have value"),
        "needNewAccount":
            MessageLookupByLibrary.simpleMessage("Need new account?"),
        "newBroadcast": MessageLookupByLibrary.simpleMessage("New broadcast"),
        "newGroup": MessageLookupByLibrary.simpleMessage("New group"),
        "newPassword": MessageLookupByLibrary.simpleMessage("New password"),
        "newPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "New password must have value"),
        "newUpdateIsAvailable":
            MessageLookupByLibrary.simpleMessage("New update is available"),
        "next": MessageLookupByLibrary.simpleMessage("Next"),
        "nickname": MessageLookupByLibrary.simpleMessage("Nickname"),
        "no": MessageLookupByLibrary.simpleMessage("No"),
        "noBio": MessageLookupByLibrary.simpleMessage("No Bio"),
        "noCodeHasBeenSendToYouToVerifyYourEmail":
            MessageLookupByLibrary.simpleMessage(
                "No code has been send to you to verify your email"),
        "noUpdatesAvailableNow":
            MessageLookupByLibrary.simpleMessage("No updates available now"),
        "none": MessageLookupByLibrary.simpleMessage("None"),
        "notAccepted": MessageLookupByLibrary.simpleMessage("Not Accepted"),
        "notification": MessageLookupByLibrary.simpleMessage("Notification"),
        "notificationDescription":
            MessageLookupByLibrary.simpleMessage("Notification Description"),
        "notificationTitle":
            MessageLookupByLibrary.simpleMessage("Notification Title"),
        "notificationsPage":
            MessageLookupByLibrary.simpleMessage("Notifications Page"),
        "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion":
            MessageLookupByLibrary.simpleMessage(
                "Now you login as read-only admin. All edits you make will not be applied due to this being a test version."),
        "off": MessageLookupByLibrary.simpleMessage("Off"),
        "offline": MessageLookupByLibrary.simpleMessage("Offline"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "oldPassword": MessageLookupByLibrary.simpleMessage("Old password"),
        "on": MessageLookupByLibrary.simpleMessage("On"),
        "oneSeenMessage":
            MessageLookupByLibrary.simpleMessage("One seen message"),
        "online": MessageLookupByLibrary.simpleMessage("Online"),
        "orLoginWith": MessageLookupByLibrary.simpleMessage("or login with"),
        "other": MessageLookupByLibrary.simpleMessage("Other"),
        "otherCategoryDescription": MessageLookupByLibrary.simpleMessage(
            "Other: This catch-all category can be used for violations that don\'t easily fit into the above categories. It might be helpful to include a text box for users to provide additional details."),
        "otpCode": MessageLookupByLibrary.simpleMessage("OTP Code"),
        "password": MessageLookupByLibrary.simpleMessage("Password"),
        "passwordHasBeenChanged":
            MessageLookupByLibrary.simpleMessage("Password has been changed"),
        "passwordIsRequired":
            MessageLookupByLibrary.simpleMessage("Password is required"),
        "passwordMustHaveValue":
            MessageLookupByLibrary.simpleMessage("Password must have value"),
        "passwordNotMatch":
            MessageLookupByLibrary.simpleMessage("Password not match"),
        "peerUserDeviceOffline":
            MessageLookupByLibrary.simpleMessage("Peer user device offline"),
        "peerUserInCallNow":
            MessageLookupByLibrary.simpleMessage("User in call now"),
        "pending": MessageLookupByLibrary.simpleMessage("Pending"),
        "phone": MessageLookupByLibrary.simpleMessage("Phone"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy policy"),
        "privacyUrl": MessageLookupByLibrary.simpleMessage("Privacy URL"),
        "profile": MessageLookupByLibrary.simpleMessage("Profile"),
        "promotedToAdminBy":
            MessageLookupByLibrary.simpleMessage("Promoted to admin by"),
        "public": MessageLookupByLibrary.simpleMessage("Public"),
        "read": MessageLookupByLibrary.simpleMessage("Read"),
        "recentUpdate": MessageLookupByLibrary.simpleMessage("Recent update"),
        "recentUpdates": MessageLookupByLibrary.simpleMessage("Recent updates"),
        "recording": MessageLookupByLibrary.simpleMessage("Recording..."),
        "register": MessageLookupByLibrary.simpleMessage("Register"),
        "registerMethod":
            MessageLookupByLibrary.simpleMessage("Register Method"),
        "registerStatus":
            MessageLookupByLibrary.simpleMessage("Register Status"),
        "rejected": MessageLookupByLibrary.simpleMessage("Rejected"),
        "repliedToYourSelf":
            MessageLookupByLibrary.simpleMessage("Replied to your self"),
        "reply": MessageLookupByLibrary.simpleMessage("Reply"),
        "replyToYourSelf":
            MessageLookupByLibrary.simpleMessage("Reply to your self"),
        "report": MessageLookupByLibrary.simpleMessage("Report"),
        "reportHasBeenSubmitted": MessageLookupByLibrary.simpleMessage(
            "Your report has been submitted"),
        "reportUser": MessageLookupByLibrary.simpleMessage("Report user"),
        "reports": MessageLookupByLibrary.simpleMessage("Reports"),
        "resetPassword": MessageLookupByLibrary.simpleMessage("Reset password"),
        "retry": MessageLookupByLibrary.simpleMessage("Retry"),
        "ring": MessageLookupByLibrary.simpleMessage("Ring"),
        "roomAlreadyInCall":
            MessageLookupByLibrary.simpleMessage("Room already in call"),
        "roomCounter": MessageLookupByLibrary.simpleMessage("Room Counter"),
        "saveLogin": MessageLookupByLibrary.simpleMessage("Save Login"),
        "search": MessageLookupByLibrary.simpleMessage("Search"),
        "seconds": MessageLookupByLibrary.simpleMessage("Seconds"),
        "send": MessageLookupByLibrary.simpleMessage("Send"),
        "sendCodeToMyEmail":
            MessageLookupByLibrary.simpleMessage("Send code to my email"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Send message"),
        "sessionEnd": MessageLookupByLibrary.simpleMessage("Session end"),
        "setMaxBroadcastMembers":
            MessageLookupByLibrary.simpleMessage("Set Max Broadcast Members"),
        "setMaxGroupMembers":
            MessageLookupByLibrary.simpleMessage("Set Max Group Members"),
        "setMaxMessageForwardAndShare": MessageLookupByLibrary.simpleMessage(
            "Set Max Message Forward and Share"),
        "setNewPrivacyPolicyUrl":
            MessageLookupByLibrary.simpleMessage("Set New Privacy Policy URL"),
        "setToAdmin": MessageLookupByLibrary.simpleMessage("Set to admin"),
        "settings": MessageLookupByLibrary.simpleMessage("Settings"),
        "share": MessageLookupByLibrary.simpleMessage("Share"),
        "shareMediaAndLocation":
            MessageLookupByLibrary.simpleMessage("Share media and location"),
        "shareYourStatus":
            MessageLookupByLibrary.simpleMessage("Share your status"),
        "showHistory": MessageLookupByLibrary.simpleMessage("Show history"),
        "showMedia": MessageLookupByLibrary.simpleMessage("Show media"),
        "soon": MessageLookupByLibrary.simpleMessage("Soon"),
        "spamOrScamDescription": MessageLookupByLibrary.simpleMessage(
            "Spam or Scam: This option would be for users to report accounts that are sending spam messages, unsolicited advertisements, or are attempting to scam others."),
        "star": MessageLookupByLibrary.simpleMessage("Star"),
        "starMessage": MessageLookupByLibrary.simpleMessage("Star message"),
        "starredMessage":
            MessageLookupByLibrary.simpleMessage("Starred message"),
        "starredMessages":
            MessageLookupByLibrary.simpleMessage("Starred Messages"),
        "startChat": MessageLookupByLibrary.simpleMessage("Start chat"),
        "startNewChatWithYou":
            MessageLookupByLibrary.simpleMessage("Start new chat with you"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "storageAndData":
            MessageLookupByLibrary.simpleMessage("Storage and Data"),
        "stories": MessageLookupByLibrary.simpleMessage("Stories"),
        "storyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Story Created Successfully"),
        "success": MessageLookupByLibrary.simpleMessage("Success"),
        "successfullyDownloadedIn":
            MessageLookupByLibrary.simpleMessage("Successfully downloaded in"),
        "supportChatSoon":
            MessageLookupByLibrary.simpleMessage("Support chat (Soon)"),
        "tapADeviceToEditOrLogOut": MessageLookupByLibrary.simpleMessage(
            "Tap a device to edit or log out."),
        "tapForPhoto": MessageLookupByLibrary.simpleMessage("Tap for photo"),
        "tapToSelectAnIcon":
            MessageLookupByLibrary.simpleMessage("Tap to select an icon"),
        "tellAFriend": MessageLookupByLibrary.simpleMessage("Tell a friend"),
        "textFieldHint":
            MessageLookupByLibrary.simpleMessage("Type a message ..."),
        "textMessages": MessageLookupByLibrary.simpleMessage("Text Messages"),
        "thereIsFileHasSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "There is file has size bigger than allowed size"),
        "thereIsVideoSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "There is video size bigger than allowed size"),
        "timeout": MessageLookupByLibrary.simpleMessage("Timeout"),
        "titleIsRequired":
            MessageLookupByLibrary.simpleMessage("Title is required"),
        "today": MessageLookupByLibrary.simpleMessage("Today"),
        "total": MessageLookupByLibrary.simpleMessage("Total"),
        "totalMessages": MessageLookupByLibrary.simpleMessage("Total Messages"),
        "totalRooms": MessageLookupByLibrary.simpleMessage("Total Rooms"),
        "totalVisits": MessageLookupByLibrary.simpleMessage("Total Visits"),
        "typing": MessageLookupByLibrary.simpleMessage("Typing..."),
        "unBlock": MessageLookupByLibrary.simpleMessage("Un Block"),
        "unBlockUser": MessageLookupByLibrary.simpleMessage("Un block user"),
        "unMute": MessageLookupByLibrary.simpleMessage("Un mute"),
        "unStar": MessageLookupByLibrary.simpleMessage("Un star"),
        "update": MessageLookupByLibrary.simpleMessage("Update"),
        "updateBroadcastTitle":
            MessageLookupByLibrary.simpleMessage("Update broadcast title"),
        "updateFeedBackEmail":
            MessageLookupByLibrary.simpleMessage("Update Feedback Email"),
        "updateGroupDescription":
            MessageLookupByLibrary.simpleMessage("Update group description"),
        "updateGroupDescriptionWillUpdateAllGroupMembers":
            MessageLookupByLibrary.simpleMessage(
                "Update group description will update all group members"),
        "updateGroupTitle":
            MessageLookupByLibrary.simpleMessage("Update group title"),
        "updateImage": MessageLookupByLibrary.simpleMessage("Update image"),
        "updateNickname":
            MessageLookupByLibrary.simpleMessage("Update nickname"),
        "updateTitle": MessageLookupByLibrary.simpleMessage("Update title"),
        "updateTitleTo":
            MessageLookupByLibrary.simpleMessage("Update title to"),
        "updateYourBio":
            MessageLookupByLibrary.simpleMessage("Update your bio"),
        "updateYourName":
            MessageLookupByLibrary.simpleMessage("Update your name"),
        "updateYourPassword":
            MessageLookupByLibrary.simpleMessage("Update your password"),
        "updateYourProfile":
            MessageLookupByLibrary.simpleMessage("Update your profile"),
        "updatedAt": MessageLookupByLibrary.simpleMessage("Updated At"),
        "upgradeToAdmin":
            MessageLookupByLibrary.simpleMessage("Upgrade to admin"),
        "userAction": MessageLookupByLibrary.simpleMessage("User Action"),
        "userAlreadyRegister":
            MessageLookupByLibrary.simpleMessage("User already register"),
        "userDeviceSessionEndDeviceDeleted":
            MessageLookupByLibrary.simpleMessage(
                "User device session end device deleted"),
        "userEmailNotFound":
            MessageLookupByLibrary.simpleMessage("User email not found"),
        "userInfo": MessageLookupByLibrary.simpleMessage("User Info"),
        "userPage": MessageLookupByLibrary.simpleMessage("User page"),
        "userProfile": MessageLookupByLibrary.simpleMessage("User Profile"),
        "userRegisterStatusNotAcceptedYet":
            MessageLookupByLibrary.simpleMessage(
                "User register status not accepted yet"),
        "users": MessageLookupByLibrary.simpleMessage("Users"),
        "usersAddedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Users added successfully"),
        "vMessageInfoTrans":
            MessageLookupByLibrary.simpleMessage("Message info"),
        "vMessagesInfoTrans":
            MessageLookupByLibrary.simpleMessage("Messages info"),
        "verified": MessageLookupByLibrary.simpleMessage("Verified"),
        "verifiedAt": MessageLookupByLibrary.simpleMessage("Verified At"),
        "video": MessageLookupByLibrary.simpleMessage("Video"),
        "videoCallMessages":
            MessageLookupByLibrary.simpleMessage("Video Call Messages"),
        "videoMessages": MessageLookupByLibrary.simpleMessage("Video Messages"),
        "visits": MessageLookupByLibrary.simpleMessage("Visits"),
        "voiceCallMessage":
            MessageLookupByLibrary.simpleMessage("Voice Call Message"),
        "voiceCallMessages":
            MessageLookupByLibrary.simpleMessage("Voice Call Messages"),
        "voiceMessages": MessageLookupByLibrary.simpleMessage("Voice Messages"),
        "wait2MinutesToSendMail":
            MessageLookupByLibrary.simpleMessage("Wait 2 minutes to send mail"),
        "waitingList": MessageLookupByLibrary.simpleMessage("Waiting List"),
        "weHighRecommendToDownloadThisUpdate":
            MessageLookupByLibrary.simpleMessage(
                "We high recommend to download this update"),
        "web": MessageLookupByLibrary.simpleMessage("Web"),
        "welcome": MessageLookupByLibrary.simpleMessage("Welcome"),
        "whenUsingMobileData":
            MessageLookupByLibrary.simpleMessage("When using mobile data"),
        "whenUsingWifi":
            MessageLookupByLibrary.simpleMessage("When using Wi-Fi"),
        "whileAuthCanFindYou": MessageLookupByLibrary.simpleMessage(
            "While authentication cannot find you"),
        "windows": MessageLookupByLibrary.simpleMessage("Windows"),
        "writeACaption":
            MessageLookupByLibrary.simpleMessage("Write a caption..."),
        "yes": MessageLookupByLibrary.simpleMessage("Yes"),
        "yesterday": MessageLookupByLibrary.simpleMessage("Yesterday"),
        "you": MessageLookupByLibrary.simpleMessage("You"),
        "youAreAboutToDeleteThisUserFromYourList":
            MessageLookupByLibrary.simpleMessage(
                "You are about to delete this user from your list"),
        "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList":
            MessageLookupByLibrary.simpleMessage(
                "You are about to delete your account your account will not appears again in the users list"),
        "youAreAboutToDismissesToMember": MessageLookupByLibrary.simpleMessage(
            "You are about to dismisses to member"),
        "youAreAboutToKick":
            MessageLookupByLibrary.simpleMessage("You are about to kick"),
        "youAreAboutToUpgradeToAdmin": MessageLookupByLibrary.simpleMessage(
            "You are about to upgrade to admin"),
        "youDontHaveAccess":
            MessageLookupByLibrary.simpleMessage("You don\'t have access"),
        "youInPublicSearch":
            MessageLookupByLibrary.simpleMessage("You in public search"),
        "youNotParticipantInThisGroup": MessageLookupByLibrary.simpleMessage(
            "You not participant in this group"),
        "yourAccountBlocked":
            MessageLookupByLibrary.simpleMessage("Your account has been baned"),
        "yourAccountDeleted": MessageLookupByLibrary.simpleMessage(
            "Your account has been deleted"),
        "yourAccountIsUnderReview": MessageLookupByLibrary.simpleMessage(
            "Your account is under review"),
        "yourAreAboutToLogoutFromThisAccount":
            MessageLookupByLibrary.simpleMessage(
                "Your are about to logout from this account"),
        "yourLastSeen": MessageLookupByLibrary.simpleMessage("Your last seen"),
        "yourLastSeenInChats":
            MessageLookupByLibrary.simpleMessage("Your last seen in chats"),
        "yourProfileAppearsInPublicSearchAndAddingForGroups":
            MessageLookupByLibrary.simpleMessage(
                "Your profile appears in public search and adding for groups"),
        "yourSessionIsEndedPleaseLoginAgain":
            MessageLookupByLibrary.simpleMessage(
                "Your session is ended please login again!"),
        "yourStory": MessageLookupByLibrary.simpleMessage("Your story"),
        "shareProfile": MessageLookupByLibrary.simpleMessage("Share Profile")
      };
}
