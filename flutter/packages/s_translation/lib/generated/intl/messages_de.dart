// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a de locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'de';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Dashboard": MessageLookupByLibrary.simpleMessage("Dashboard"),
        "about": MessageLookupByLibrary.simpleMessage("Über"),
        "aboutToBlockUserWithConsequences": MessageLookupByLibrary.simpleMessage(
            "Sie stehen kurz davor, diesen Benutzer zu blockieren. Sie können ihm keine Nachrichten senden und ihn nicht zu Gruppen oder Rundsendungen hinzufügen!"),
        "accepted": MessageLookupByLibrary.simpleMessage("Akzeptiert"),
        "account": MessageLookupByLibrary.simpleMessage("Konto"),
        "actions": MessageLookupByLibrary.simpleMessage("Aktionen"),
        "addMembers":
            MessageLookupByLibrary.simpleMessage("Mitglieder hinzufügen"),
        "addNewStory":
            MessageLookupByLibrary.simpleMessage("Neue Story hinzufügen"),
        "addParticipants":
            MessageLookupByLibrary.simpleMessage("Teilnehmer hinzufügen"),
        "addedYouToNewBroadcast": MessageLookupByLibrary.simpleMessage(
            "Hat dich zu einer neuen Rundsendung hinzugefügt"),
        "admin": MessageLookupByLibrary.simpleMessage("Admin"),
        "adminNotification":
            MessageLookupByLibrary.simpleMessage("Admin-Benachrichtigung"),
        "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself":
            MessageLookupByLibrary.simpleMessage(
                "Alle Daten wurden gesichert. Sie müssen die Daten nicht selbst verwalten. Wenn Sie sich abmelden und erneut anmelden, werden alle Chats angezeigt, ebenso wie in der Webversion"),
        "allDeletedMessages":
            MessageLookupByLibrary.simpleMessage("Alle gelöschten Nachrichten"),
        "allowAds":
            MessageLookupByLibrary.simpleMessage("Werbeanzeigen erlauben"),
        "allowCalls": MessageLookupByLibrary.simpleMessage("Anrufe erlauben"),
        "allowCreateBroadcast": MessageLookupByLibrary.simpleMessage(
            "Erstellen von Rundsenden erlauben"),
        "allowCreateGroups": MessageLookupByLibrary.simpleMessage(
            "Erstellen von Gruppen erlauben"),
        "allowDesktopLogin":
            MessageLookupByLibrary.simpleMessage("Desktop-Anmeldung erlauben"),
        "allowMobileLogin":
            MessageLookupByLibrary.simpleMessage("Mobile Anmeldung erlauben"),
        "allowSendMedia":
            MessageLookupByLibrary.simpleMessage("Senden von Medien erlauben"),
        "allowWebLogin":
            MessageLookupByLibrary.simpleMessage("Webanmeldung erlauben"),
        "alreadyHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Bereits ein Konto?"),
        "android": MessageLookupByLibrary.simpleMessage("Android"),
        "appMembers": MessageLookupByLibrary.simpleMessage("App-Mitglieder"),
        "appleStoreAppUrl":
            MessageLookupByLibrary.simpleMessage("Apple Store App URL"),
        "areYouSure": MessageLookupByLibrary.simpleMessage("Sind Sie sicher?"),
        "areYouSureToBlock": MessageLookupByLibrary.simpleMessage(
            "Bist du sicher, dass du blockieren möchtest"),
        "areYouSureToLeaveThisGroupThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "Bist du sicher, dass du diese Gruppe verlassen möchtest? Diese Aktion kann nicht rückgängig gemacht werden"),
        "areYouSureToPermitYourCopyThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "Bist du sicher, dass du deine Kopie erlauben möchtest? Diese Aktion kann nicht rückgängig gemacht werden"),
        "areYouSureToReportUserToAdmin": MessageLookupByLibrary.simpleMessage(
            "Sind Sie sicher, dass Sie diesen Benutzer dem Administrator melden möchten?"),
        "areYouSureToUnBlock": MessageLookupByLibrary.simpleMessage(
            "Sind Sie sicher, dass Sie den Benutzer entsperren möchten"),
        "areYouWantToMakeVideoCall": MessageLookupByLibrary.simpleMessage(
            "Möchtest du einen Videoanruf tätigen?"),
        "areYouWantToMakeVoiceCall": MessageLookupByLibrary.simpleMessage(
            "Möchtest du einen Sprachanruf tätigen?"),
        "audio": MessageLookupByLibrary.simpleMessage("Audio"),
        "audioCall": MessageLookupByLibrary.simpleMessage("Audioanruf"),
        "back": MessageLookupByLibrary.simpleMessage("Zurück"),
        "banAt": MessageLookupByLibrary.simpleMessage("Gebannt am"),
        "banTo": MessageLookupByLibrary.simpleMessage("Gebannt bis"),
        "bio": MessageLookupByLibrary.simpleMessage("Biografie"),
        "block": MessageLookupByLibrary.simpleMessage("Blockieren"),
        "blockUser":
            MessageLookupByLibrary.simpleMessage("Benutzer blockieren"),
        "blocked": MessageLookupByLibrary.simpleMessage("Blockiert"),
        "blockedUsers":
            MessageLookupByLibrary.simpleMessage("Blockierte Benutzer"),
        "broadcast": MessageLookupByLibrary.simpleMessage("Rundsenden"),
        "broadcastInfo":
            MessageLookupByLibrary.simpleMessage("Rundsendungsinfo"),
        "broadcastMembers":
            MessageLookupByLibrary.simpleMessage("Rundsendungsteilnehmer"),
        "broadcastName":
            MessageLookupByLibrary.simpleMessage("Rundsendungsname"),
        "broadcastParticipants":
            MessageLookupByLibrary.simpleMessage("Rundsendungsteilnehmer"),
        "broadcastSettings":
            MessageLookupByLibrary.simpleMessage("Rundsendungseinstellungen"),
        "callNotAllowed":
            MessageLookupByLibrary.simpleMessage("Anruf nicht erlaubt"),
        "callTimeoutInSeconds": MessageLookupByLibrary.simpleMessage(
            "Anrufzeitüberschreitung (in Sekunden)"),
        "calls": MessageLookupByLibrary.simpleMessage("Anrufe"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Abbrechen"),
        "canceled": MessageLookupByLibrary.simpleMessage("Abgebrochen"),
        "changeSubject": MessageLookupByLibrary.simpleMessage("Thema ändern"),
        "chat": MessageLookupByLibrary.simpleMessage("Chat"),
        "chats": MessageLookupByLibrary.simpleMessage("CHATS"),
        "checkForUpdates":
            MessageLookupByLibrary.simpleMessage("Nach Updates suchen"),
        "chooseHowAutomaticDownloadWorks": MessageLookupByLibrary.simpleMessage(
            "Wählen Sie aus, wie der automatische Download funktioniert"),
        "chooseRoom": MessageLookupByLibrary.simpleMessage("Raum auswählen"),
        "clear": MessageLookupByLibrary.simpleMessage("Löschen"),
        "clearCallsConfirm":
            MessageLookupByLibrary.simpleMessage("Anrufe löschen bestätigen"),
        "clearChat": MessageLookupByLibrary.simpleMessage("Chat leeren"),
        "clickToAddGroupDescription": MessageLookupByLibrary.simpleMessage(
            "Klicken Sie hier, um eine Gruppenbeschreibung hinzuzufügen"),
        "clickToSee":
            MessageLookupByLibrary.simpleMessage("Klicken, um zu sehen"),
        "clickToSeeAllUserCountries": MessageLookupByLibrary.simpleMessage(
            "Klicken Sie hier, um alle Benutzländer anzuzeigen"),
        "clickToSeeAllUserDevicesDetails": MessageLookupByLibrary.simpleMessage(
            "Klicken Sie hier, um alle Benutzergerätedetails anzuzeigen"),
        "clickToSeeAllUserInformations": MessageLookupByLibrary.simpleMessage(
            "Klicken Sie hier, um alle Benutzerinformationen anzuzeigen"),
        "clickToSeeAllUserMessagesDetails": MessageLookupByLibrary.simpleMessage(
            "Klicken Sie hier, um alle Benutzernachrichtendetails anzuzeigen"),
        "clickToSeeAllUserReports": MessageLookupByLibrary.simpleMessage(
            "Klicken Sie hier, um alle Benutzerberichte anzuzeigen"),
        "clickToSeeAllUserRoomsDetails": MessageLookupByLibrary.simpleMessage(
            "Klicken Sie hier, um alle Benutzerraumdetails anzuzeigen"),
        "close": MessageLookupByLibrary.simpleMessage("Schließen"),
        "codeHasBeenExpired":
            MessageLookupByLibrary.simpleMessage("Code ist abgelaufen"),
        "codeMustEqualToSixNumbers": MessageLookupByLibrary.simpleMessage(
            "Code muss aus sechs Zahlen bestehen"),
        "configureYourAccountPrivacy": MessageLookupByLibrary.simpleMessage(
            "Konfigurieren Sie die Privatsphäre Ihres Kontos"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Passwort bestätigen"),
        "confirmPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Passwortbestätigung muss einen Wert haben"),
        "congregationsYourAccountHasBeenAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Glückwunsch, Ihr Konto wurde akzeptiert"),
        "connecting": MessageLookupByLibrary.simpleMessage("Verbinden..."),
        "contactInfo":
            MessageLookupByLibrary.simpleMessage("Kontaktinformationen"),
        "contactUs": MessageLookupByLibrary.simpleMessage("Kontaktiere uns"),
        "copy": MessageLookupByLibrary.simpleMessage("Kopieren"),
        "countries": MessageLookupByLibrary.simpleMessage("Länder"),
        "country": MessageLookupByLibrary.simpleMessage("Land"),
        "create": MessageLookupByLibrary.simpleMessage("Erstellen"),
        "createBroadcast":
            MessageLookupByLibrary.simpleMessage("Rundsendung erstellen"),
        "createGroup": MessageLookupByLibrary.simpleMessage("Gruppe erstellen"),
        "createMediaStory":
            MessageLookupByLibrary.simpleMessage("Mediengeschichte erstellen"),
        "createStory": MessageLookupByLibrary.simpleMessage("Story erstellen"),
        "createTextStory":
            MessageLookupByLibrary.simpleMessage("Text-Story erstellen"),
        "createYourStory":
            MessageLookupByLibrary.simpleMessage("Erstelle deine Geschichte"),
        "createdAt": MessageLookupByLibrary.simpleMessage("Erstellt am"),
        "creator": MessageLookupByLibrary.simpleMessage("Ersteller"),
        "currentDevice":
            MessageLookupByLibrary.simpleMessage("Aktuelles Gerät"),
        "dashboard": MessageLookupByLibrary.simpleMessage("Dashboard"),
        "dataPrivacy": MessageLookupByLibrary.simpleMessage("Datenschutz"),
        "delete": MessageLookupByLibrary.simpleMessage("Löschen"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("Chat löschen"),
        "deleteFromAll":
            MessageLookupByLibrary.simpleMessage("Von allen löschen"),
        "deleteFromMe": MessageLookupByLibrary.simpleMessage("Von mir löschen"),
        "deleteMember":
            MessageLookupByLibrary.simpleMessage("Mitglied löschen"),
        "deleteMyAccount":
            MessageLookupByLibrary.simpleMessage("Mein Konto löschen"),
        "deleteThisDeviceDesc": MessageLookupByLibrary.simpleMessage(
            "Das Löschen dieses Geräts führt zur sofortigen Abmeldung dieses Geräts"),
        "deleteUser": MessageLookupByLibrary.simpleMessage("Benutzer löschen"),
        "deleteYouCopy":
            MessageLookupByLibrary.simpleMessage("Deine Kopie löschen"),
        "deleted": MessageLookupByLibrary.simpleMessage("Gelöscht"),
        "deletedAt": MessageLookupByLibrary.simpleMessage("Gelöscht am"),
        "delivered": MessageLookupByLibrary.simpleMessage("Zugestellt"),
        "description": MessageLookupByLibrary.simpleMessage("Beschreibung"),
        "descriptionIsRequired": MessageLookupByLibrary.simpleMessage(
            "Beschreibung ist erforderlich"),
        "desktopAndOtherDevices":
            MessageLookupByLibrary.simpleMessage("Desktop und andere Geräte"),
        "deviceHasBeenLogoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage(
                "Gerät wurde von allen Geräten abgemeldet"),
        "deviceStatus": MessageLookupByLibrary.simpleMessage("Gerätestatus"),
        "devices": MessageLookupByLibrary.simpleMessage("Geräte"),
        "directChat": MessageLookupByLibrary.simpleMessage("Direkter Chat"),
        "directRooms": MessageLookupByLibrary.simpleMessage("Direkträume"),
        "dismissedToMemberBy":
            MessageLookupByLibrary.simpleMessage("Abgewiesen zu Mitglied von"),
        "dismissesToMember":
            MessageLookupByLibrary.simpleMessage("Abgewiesen zu Mitglied"),
        "docs": MessageLookupByLibrary.simpleMessage("Dokumente"),
        "done": MessageLookupByLibrary.simpleMessage("Fertig"),
        "download": MessageLookupByLibrary.simpleMessage("Herunterladen"),
        "downloading": MessageLookupByLibrary.simpleMessage("Herunterladen..."),
        "edit": MessageLookupByLibrary.simpleMessage("Bearbeiten"),
        "email": MessageLookupByLibrary.simpleMessage("E-Mail"),
        "emailMustBeValid":
            MessageLookupByLibrary.simpleMessage("E-Mail muss gültig sein"),
        "emailNotValid":
            MessageLookupByLibrary.simpleMessage("E-Mail ungültig"),
        "enterNameAndAddOptionalProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Geben Sie Ihren Namen ein und fügen Sie ein optionales Profilbild hinzu"),
        "error": MessageLookupByLibrary.simpleMessage("Fehler"),
        "exitGroup": MessageLookupByLibrary.simpleMessage("Gruppe verlassen"),
        "explainWhatHappens": MessageLookupByLibrary.simpleMessage(
            "Erklären Sie hier, was passiert"),
        "feedBackEmail":
            MessageLookupByLibrary.simpleMessage("Feedback-E-Mail"),
        "fileHasBeenSavedTo": MessageLookupByLibrary.simpleMessage(
            "Datei wurde gespeichert unter"),
        "fileMessages":
            MessageLookupByLibrary.simpleMessage("Dateinachrichten"),
        "files": MessageLookupByLibrary.simpleMessage("Dateien"),
        "finished": MessageLookupByLibrary.simpleMessage("Beendet"),
        "forRequest": MessageLookupByLibrary.simpleMessage("Auf Anfrage"),
        "forgetPassword":
            MessageLookupByLibrary.simpleMessage("Passwort vergessen"),
        "forgetPasswordExpireTime": MessageLookupByLibrary.simpleMessage(
            "Ablaufzeit für das Zurücksetzen des Passworts"),
        "forward": MessageLookupByLibrary.simpleMessage("Weiterleiten"),
        "fullName": MessageLookupByLibrary.simpleMessage("Vollständiger Name"),
        "gallery": MessageLookupByLibrary.simpleMessage("Galerie"),
        "globalSearch": MessageLookupByLibrary.simpleMessage("Globale Suche"),
        "googlePlayAppUrl":
            MessageLookupByLibrary.simpleMessage("Google Play App URL"),
        "group": MessageLookupByLibrary.simpleMessage("Gruppe"),
        "groupCreatedBy":
            MessageLookupByLibrary.simpleMessage("Gruppe erstellt von"),
        "groupDescription":
            MessageLookupByLibrary.simpleMessage("Gruppenbeschreibung"),
        "groupIcon": MessageLookupByLibrary.simpleMessage("Gruppensymbol"),
        "groupInfo": MessageLookupByLibrary.simpleMessage("Gruppeninfo"),
        "groupMembers":
            MessageLookupByLibrary.simpleMessage("Gruppenmitglieder"),
        "groupName": MessageLookupByLibrary.simpleMessage("Gruppenname"),
        "groupParticipants":
            MessageLookupByLibrary.simpleMessage("Gruppenteilnehmer"),
        "groupSettings":
            MessageLookupByLibrary.simpleMessage("Gruppeneinstellungen"),
        "groupWith": MessageLookupByLibrary.simpleMessage("Gruppe mit"),
        "harassmentOrBullyingDescription": MessageLookupByLibrary.simpleMessage(
            "Belästigung oder Mobbing: Diese Option ermöglicht es Benutzern, Personen zu melden, die sie oder andere mit belästigenden Nachrichten, Drohungen oder anderen Formen von Mobbing ansprechen."),
        "help": MessageLookupByLibrary.simpleMessage("Hilfe"),
        "hiIamUse": MessageLookupByLibrary.simpleMessage("Hallo, ich benutze"),
        "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Wenn diese Option deaktiviert ist, wird das Erstellen von Chat-Rundsenden blockiert"),
        "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Wenn diese Option deaktiviert ist, wird das Erstellen von Chatgruppen blockiert"),
        "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Wenn diese Option deaktiviert ist, wird die Desktop-Anmeldung oder -Registrierung (Windows und macOS) blockiert"),
        "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly":
            MessageLookupByLibrary.simpleMessage(
                "Wenn diese Option aktiviert ist, werden Google Ads-Banner in Chats angezeigt"),
        "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Wenn diese Option deaktiviert ist, werden das Senden von Chatdateien, Bildern, Videos und Standort blockiert"),
        "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Wenn diese Option deaktiviert ist, wird die Web-Anmeldung oder -Registrierung blockiert"),
        "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed":
            MessageLookupByLibrary.simpleMessage(
                "Wenn diese Option aktiviert ist, sind Video- und Sprachanrufe erlaubt"),
        "image": MessageLookupByLibrary.simpleMessage("Bild"),
        "imageMessages":
            MessageLookupByLibrary.simpleMessage("Bildnachrichten"),
        "images": MessageLookupByLibrary.simpleMessage("Bilder"),
        "inAppAlerts":
            MessageLookupByLibrary.simpleMessage("In-App-Benachrichtigungen"),
        "inCall": MessageLookupByLibrary.simpleMessage("Im Anruf"),
        "inappropriateContentDescription": MessageLookupByLibrary.simpleMessage(
            "Unangemessene Inhalte: Benutzer können diese Option auswählen, um sexuell explizites Material, Hassrede oder andere Inhalte zu melden, die gegen Gemeinschaftsstandards verstoßen."),
        "info": MessageLookupByLibrary.simpleMessage("Info"),
        "infoMessages":
            MessageLookupByLibrary.simpleMessage("Informationsnachrichten"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("Ungültiger Code"),
        "invalidLoginData":
            MessageLookupByLibrary.simpleMessage("Ungültige Anmeldedaten"),
        "ios": MessageLookupByLibrary.simpleMessage("iOS"),
        "joinedAt": MessageLookupByLibrary.simpleMessage("Beigetreten am"),
        "joinedBy": MessageLookupByLibrary.simpleMessage("Beigetreten von"),
        "kickMember":
            MessageLookupByLibrary.simpleMessage("Mitglied entfernen"),
        "kickedBy": MessageLookupByLibrary.simpleMessage("Rausgeworfen von"),
        "language": MessageLookupByLibrary.simpleMessage("Sprache"),
        "lastActiveFrom":
            MessageLookupByLibrary.simpleMessage("Zuletzt aktiv ab"),
        "leaveGroup": MessageLookupByLibrary.simpleMessage("Gruppe verlassen"),
        "leaveGroupAndDeleteYourMessageCopy":
            MessageLookupByLibrary.simpleMessage(
                "Gruppe verlassen und deine Nachrichten kopie löschen"),
        "leftTheGroup":
            MessageLookupByLibrary.simpleMessage("Hat die Gruppe verlassen"),
        "linkADeviceSoon":
            MessageLookupByLibrary.simpleMessage("Ein Gerät verknüpfen (bald)"),
        "linkByQrCode":
            MessageLookupByLibrary.simpleMessage("Über QR-Code verknüpfen"),
        "linkedDevices":
            MessageLookupByLibrary.simpleMessage("Verknüpfte Geräte"),
        "links": MessageLookupByLibrary.simpleMessage("Links"),
        "loading": MessageLookupByLibrary.simpleMessage("Laden ..."),
        "location": MessageLookupByLibrary.simpleMessage("Standort"),
        "locationMessages":
            MessageLookupByLibrary.simpleMessage("Ortsnachrichten"),
        "logOut": MessageLookupByLibrary.simpleMessage("Abmelden"),
        "login": MessageLookupByLibrary.simpleMessage("Anmelden"),
        "loginAgain": MessageLookupByLibrary.simpleMessage("Erneut anmelden!"),
        "loginNowAllowedNowPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Derzeit ist die Anmeldung nicht möglich. Bitte versuchen Sie es später erneut."),
        "logoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage("Von allen Geräten abmelden?"),
        "macOs": MessageLookupByLibrary.simpleMessage("macOS"),
        "makeCall": MessageLookupByLibrary.simpleMessage("Anruf tätigen"),
        "media": MessageLookupByLibrary.simpleMessage("Medien"),
        "mediaLinksAndDocs":
            MessageLookupByLibrary.simpleMessage("Medien, Links und Dokumente"),
        "member": MessageLookupByLibrary.simpleMessage("Mitglied"),
        "members": MessageLookupByLibrary.simpleMessage("Mitglieder"),
        "messageCounter":
            MessageLookupByLibrary.simpleMessage("Nachrichtenzähler"),
        "messageHasBeenDeleted": MessageLookupByLibrary.simpleMessage(
            "Die Nachricht wurde gelöscht"),
        "messageHasBeenViewed":
            MessageLookupByLibrary.simpleMessage("Nachricht wurde angezeigt"),
        "messageInfo": MessageLookupByLibrary.simpleMessage("Nachrichteninfo"),
        "messages": MessageLookupByLibrary.simpleMessage("Nachrichten"),
        "microphoneAndCameraPermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone and camera permission must be accepted"),
        "microphonePermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone permission must be accepted"),
        "minutes": MessageLookupByLibrary.simpleMessage("Minuten"),
        "more": MessageLookupByLibrary.simpleMessage("Mehr"),
        "mute": MessageLookupByLibrary.simpleMessage("Stumm"),
        "muteNotifications": MessageLookupByLibrary.simpleMessage(
            "Benachrichtigungen stummschalten"),
        "myPrivacy": MessageLookupByLibrary.simpleMessage("Meine Privatsphäre"),
        "name": MessageLookupByLibrary.simpleMessage("Name"),
        "nameMustHaveValue":
            MessageLookupByLibrary.simpleMessage("Name muss einen Wert haben"),
        "needNewAccount": MessageLookupByLibrary.simpleMessage(
            "Benötigen Sie ein neues Konto?"),
        "newBroadcast":
            MessageLookupByLibrary.simpleMessage("Neue Rundsendung"),
        "newGroup": MessageLookupByLibrary.simpleMessage("Neue Gruppe"),
        "newPassword": MessageLookupByLibrary.simpleMessage("Neues Passwort"),
        "newPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Neues Passwort muss einen Wert haben"),
        "newUpdateIsAvailable": MessageLookupByLibrary.simpleMessage(
            "Ein neues Update ist verfügbar"),
        "next": MessageLookupByLibrary.simpleMessage("Weiter"),
        "nickname": MessageLookupByLibrary.simpleMessage("Spitzname"),
        "no": MessageLookupByLibrary.simpleMessage("Nein"),
        "noBio": MessageLookupByLibrary.simpleMessage("Keine Biografie"),
        "noUpdatesAvailableNow": MessageLookupByLibrary.simpleMessage(
            "Derzeit sind keine Updates verfügbar"),
        "none": MessageLookupByLibrary.simpleMessage("Keiner"),
        "notAccepted": MessageLookupByLibrary.simpleMessage("Nicht akzeptiert"),
        "notification":
            MessageLookupByLibrary.simpleMessage("Benachrichtigung"),
        "notificationDescription": MessageLookupByLibrary.simpleMessage(
            "Benachrichtigungsbeschreibung"),
        "notificationTitle":
            MessageLookupByLibrary.simpleMessage("Benachrichtigungstitel"),
        "notificationsPage":
            MessageLookupByLibrary.simpleMessage("Benachrichtigungsseite"),
        "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion":
            MessageLookupByLibrary.simpleMessage(
                "Sie sind jetzt als schreibgeschützter Administrator angemeldet. Alle Änderungen, die Sie vornehmen, werden nicht übernommen, da dies eine Testversion ist."),
        "off": MessageLookupByLibrary.simpleMessage("Aus"),
        "offline": MessageLookupByLibrary.simpleMessage("Offline"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "oldPassword": MessageLookupByLibrary.simpleMessage("Altes Passwort"),
        "on": MessageLookupByLibrary.simpleMessage("Ein"),
        "oneSeenMessage":
            MessageLookupByLibrary.simpleMessage("Eine gesehene Nachricht"),
        "online": MessageLookupByLibrary.simpleMessage("Online"),
        "orLoginWith":
            MessageLookupByLibrary.simpleMessage("Oder anmelden mit"),
        "other": MessageLookupByLibrary.simpleMessage("Andere"),
        "otherCategoryDescription": MessageLookupByLibrary.simpleMessage(
            "Andere: Diese allgemeine Kategorie kann für Verstöße verwendet werden, die nicht leicht in die obigen Kategorien passen. Es könnte hilfreich sein, ein Textfeld bereitzustellen, in dem Benutzer weitere Details angeben können."),
        "otpCode": MessageLookupByLibrary.simpleMessage("OTP-Code"),
        "password": MessageLookupByLibrary.simpleMessage("Passwort"),
        "passwordHasBeenChanged":
            MessageLookupByLibrary.simpleMessage("Passwort wurde geändert"),
        "passwordIsRequired":
            MessageLookupByLibrary.simpleMessage("Passwort ist erforderlich"),
        "passwordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Passwort muss einen Wert haben"),
        "passwordNotMatch": MessageLookupByLibrary.simpleMessage(
            "Passwort stimmt nicht überein"),
        "peerUserDeviceOffline": MessageLookupByLibrary.simpleMessage(
            "Gerät des anderen Benutzers offline"),
        "peerUserInCallNow": MessageLookupByLibrary.simpleMessage(
            "Benutzer ist gerade im Anruf"),
        "pending": MessageLookupByLibrary.simpleMessage("Ausstehend"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Datenschutzrichtlinie"),
        "privacyUrl": MessageLookupByLibrary.simpleMessage("Datenschutz URL"),
        "profile": MessageLookupByLibrary.simpleMessage("Profil"),
        "promotedToAdminBy": MessageLookupByLibrary.simpleMessage(
            "Zum Administrator befördert von"),
        "public": MessageLookupByLibrary.simpleMessage("Öffentlich"),
        "read": MessageLookupByLibrary.simpleMessage("Gelesen"),
        "recentUpdate":
            MessageLookupByLibrary.simpleMessage("Letzte Aktualisierung"),
        "recentUpdates":
            MessageLookupByLibrary.simpleMessage("Aktualisierungen"),
        "recording": MessageLookupByLibrary.simpleMessage("Aufnahme..."),
        "register": MessageLookupByLibrary.simpleMessage("Registrieren"),
        "registerMethod":
            MessageLookupByLibrary.simpleMessage("Registrierungsmethode"),
        "registerStatus":
            MessageLookupByLibrary.simpleMessage("Registrierungsstatus"),
        "rejected": MessageLookupByLibrary.simpleMessage("Abgelehnt"),
        "repliedToYourSelf": MessageLookupByLibrary.simpleMessage(
            "Hat auf dich selbst geantwortet"),
        "reply": MessageLookupByLibrary.simpleMessage("Antworten"),
        "replyToYourSelf":
            MessageLookupByLibrary.simpleMessage("Antworte auf dich selbst"),
        "report": MessageLookupByLibrary.simpleMessage("Melden"),
        "reportHasBeenSubmitted":
            MessageLookupByLibrary.simpleMessage("Ihre Meldung wurde gesendet"),
        "reportUser": MessageLookupByLibrary.simpleMessage("Benutzer melden"),
        "reports": MessageLookupByLibrary.simpleMessage("Berichte"),
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("Passwort zurücksetzen"),
        "retry": MessageLookupByLibrary.simpleMessage("Erneut versuchen"),
        "ring": MessageLookupByLibrary.simpleMessage("Klingeln"),
        "roomAlreadyInCall":
            MessageLookupByLibrary.simpleMessage("Raum bereits im Anruf"),
        "roomCounter": MessageLookupByLibrary.simpleMessage("Raumzähler"),
        "saveLogin":
            MessageLookupByLibrary.simpleMessage("Anmeldung speichern"),
        "search": MessageLookupByLibrary.simpleMessage("Suche"),
        "seconds": MessageLookupByLibrary.simpleMessage("Sekunden"),
        "send": MessageLookupByLibrary.simpleMessage("Senden"),
        "sendCodeToMyEmail":
            MessageLookupByLibrary.simpleMessage("Code an meine E-Mail senden"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Nachricht senden"),
        "sessionEnd": MessageLookupByLibrary.simpleMessage("Sitzungsende"),
        "setMaxBroadcastMembers": MessageLookupByLibrary.simpleMessage(
            "Maximale Rundsendemitglieder festlegen"),
        "setMaxGroupMembers": MessageLookupByLibrary.simpleMessage(
            "Maximale Gruppenmitglieder festlegen"),
        "setMaxMessageForwardAndShare": MessageLookupByLibrary.simpleMessage(
            "Maximale Nachrichtenweiterleitung und -freigabe festlegen"),
        "setNewPrivacyPolicyUrl": MessageLookupByLibrary.simpleMessage(
            "Neue Datenschutzrichtlinien-URL festlegen"),
        "setToAdmin":
            MessageLookupByLibrary.simpleMessage("Als Administrator festlegen"),
        "settings": MessageLookupByLibrary.simpleMessage("Einstellungen"),
        "share": MessageLookupByLibrary.simpleMessage("Teilen"),
        "shareMediaAndLocation":
            MessageLookupByLibrary.simpleMessage("Medien und Standort teilen"),
        "shareYourStatus":
            MessageLookupByLibrary.simpleMessage("Teile deinen Status"),
        "showHistory": MessageLookupByLibrary.simpleMessage("Verlauf anzeigen"),
        "showMedia": MessageLookupByLibrary.simpleMessage("Medien anzeigen"),
        "soon": MessageLookupByLibrary.simpleMessage("Bald"),
        "spamOrScamDescription": MessageLookupByLibrary.simpleMessage(
            "Spam oder Betrug: Diese Option ist für Benutzer gedacht, die Konten melden möchten, die Spam-Nachrichten, unerwünschte Werbung senden oder versuchen, andere zu betrügen."),
        "star": MessageLookupByLibrary.simpleMessage("Markieren"),
        "starMessage":
            MessageLookupByLibrary.simpleMessage("Nachricht markieren"),
        "starredMessage":
            MessageLookupByLibrary.simpleMessage("Markierte Nachrichten"),
        "starredMessages":
            MessageLookupByLibrary.simpleMessage("Markierte Nachrichten"),
        "startChat": MessageLookupByLibrary.simpleMessage("Chat starten"),
        "startNewChatWithYou":
            MessageLookupByLibrary.simpleMessage("Neuen Chat mit dir beginnen"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "storageAndData":
            MessageLookupByLibrary.simpleMessage("Speicher und Daten"),
        "stories": MessageLookupByLibrary.simpleMessage("Geschichten"),
        "storyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Story erfolgreich erstellt"),
        "success": MessageLookupByLibrary.simpleMessage("Erfolg"),
        "successfullyDownloadedIn": MessageLookupByLibrary.simpleMessage(
            "Erfolgreich heruntergeladen in"),
        "supportChatSoon":
            MessageLookupByLibrary.simpleMessage("Support-Chat (bald)"),
        "tapADeviceToEditOrLogOut": MessageLookupByLibrary.simpleMessage(
            "Tippen Sie auf ein Gerät, um es zu bearbeiten oder abzumelden."),
        "tapForPhoto": MessageLookupByLibrary.simpleMessage("Für Foto tippen"),
        "tapToSelectAnIcon": MessageLookupByLibrary.simpleMessage(
            "Tippen Sie, um ein Symbol auszuwählen"),
        "tellAFriend": MessageLookupByLibrary.simpleMessage("Freund erzählen"),
        "textFieldHint":
            MessageLookupByLibrary.simpleMessage("Nachricht eingeben..."),
        "textMessages": MessageLookupByLibrary.simpleMessage("Textnachrichten"),
        "thereIsFileHasSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "Es gibt eine Datei, die die erlaubte Größe überschreitet"),
        "thereIsVideoSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "Es gibt ein Video, das die erlaubte Größe überschreitet"),
        "timeout": MessageLookupByLibrary.simpleMessage("Zeitüberschreitung"),
        "titleIsRequired":
            MessageLookupByLibrary.simpleMessage("Titel ist erforderlich"),
        "today": MessageLookupByLibrary.simpleMessage("Heute"),
        "total": MessageLookupByLibrary.simpleMessage("Gesamt"),
        "totalMessages":
            MessageLookupByLibrary.simpleMessage("Gesamtnachrichten"),
        "totalRooms": MessageLookupByLibrary.simpleMessage("Gesamträume"),
        "totalVisits": MessageLookupByLibrary.simpleMessage("Gesamtbesuche"),
        "typing": MessageLookupByLibrary.simpleMessage("Schreibt..."),
        "unBlock": MessageLookupByLibrary.simpleMessage("Entsperren"),
        "unBlockUser":
            MessageLookupByLibrary.simpleMessage("Benutzer entsperren"),
        "unMute": MessageLookupByLibrary.simpleMessage("Ton an"),
        "unStar": MessageLookupByLibrary.simpleMessage("Stern entfernen"),
        "update": MessageLookupByLibrary.simpleMessage("Aktualisieren"),
        "updateBroadcastTitle": MessageLookupByLibrary.simpleMessage(
            "Titel der Rundsendung aktualisieren"),
        "updateFeedBackEmail": MessageLookupByLibrary.simpleMessage(
            "Feedback-E-Mail aktualisieren"),
        "updateGroupDescription": MessageLookupByLibrary.simpleMessage(
            "Gruppenbeschreibung aktualisieren"),
        "updateGroupDescriptionWillUpdateAllGroupMembers":
            MessageLookupByLibrary.simpleMessage(
                "Die Aktualisierung der Gruppenbeschreibung wird alle Gruppenmitglieder aktualisieren"),
        "updateGroupTitle":
            MessageLookupByLibrary.simpleMessage("Gruppentitel aktualisieren"),
        "updateImage":
            MessageLookupByLibrary.simpleMessage("Bild aktualisieren"),
        "updateNickname":
            MessageLookupByLibrary.simpleMessage("Spitznamen aktualisieren"),
        "updateTitle":
            MessageLookupByLibrary.simpleMessage("Titel aktualisieren"),
        "updateTitleTo":
            MessageLookupByLibrary.simpleMessage("Titel aktualisieren auf"),
        "updateYourBio": MessageLookupByLibrary.simpleMessage(
            "Ihre Biografie aktualisieren"),
        "updateYourName":
            MessageLookupByLibrary.simpleMessage("Ihren Namen aktualisieren"),
        "updateYourPassword":
            MessageLookupByLibrary.simpleMessage("Ihr Passwort aktualisieren"),
        "updateYourProfile": MessageLookupByLibrary.simpleMessage(
            "Aktualisieren Sie Ihr Profil"),
        "updatedAt": MessageLookupByLibrary.simpleMessage("Aktualisiert am"),
        "upgradeToAdmin":
            MessageLookupByLibrary.simpleMessage("Zum Administrator befördern"),
        "userAction": MessageLookupByLibrary.simpleMessage("Benutzeraktion"),
        "userAlreadyRegister": MessageLookupByLibrary.simpleMessage(
            "Benutzer bereits registriert"),
        "userDeviceSessionEndDeviceDeleted":
            MessageLookupByLibrary.simpleMessage(
                "Sitzung des Benutzergeräts beendet, Gerät gelöscht"),
        "userEmailNotFound": MessageLookupByLibrary.simpleMessage(
            "Benutzer-E-Mail nicht gefunden"),
        "userInfo":
            MessageLookupByLibrary.simpleMessage("Benutzerinformationen"),
        "userPage": MessageLookupByLibrary.simpleMessage("Benutzerseite"),
        "userProfile": MessageLookupByLibrary.simpleMessage("Benutzerprofil"),
        "userRegisterStatusNotAcceptedYet":
            MessageLookupByLibrary.simpleMessage(
                "Benutzerregistrierungsstatus noch nicht akzeptiert"),
        "users": MessageLookupByLibrary.simpleMessage("Benutzer"),
        "usersAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Benutzer erfolgreich hinzugefügt"),
        "vMessageInfoTrans":
            MessageLookupByLibrary.simpleMessage("Nachrichteninfo"),
        "vMessagesInfoTrans":
            MessageLookupByLibrary.simpleMessage("Nachrichteninfo"),
        "verified": MessageLookupByLibrary.simpleMessage("Verifiziert"),
        "verifiedAt": MessageLookupByLibrary.simpleMessage("Verifiziert am"),
        "video": MessageLookupByLibrary.simpleMessage("Video"),
        "videoCallMessages":
            MessageLookupByLibrary.simpleMessage("Videoanrufnachrichten"),
        "videoMessages":
            MessageLookupByLibrary.simpleMessage("Videonachrichten"),
        "visits": MessageLookupByLibrary.simpleMessage("Besuche"),
        "voiceCallMessage":
            MessageLookupByLibrary.simpleMessage("Sprachanrufnachricht"),
        "voiceCallMessages":
            MessageLookupByLibrary.simpleMessage("Anrufnachrichten"),
        "voiceMessages":
            MessageLookupByLibrary.simpleMessage("Sprachnachrichten"),
        "wait2MinutesToSendMail": MessageLookupByLibrary.simpleMessage(
            "Warte 2 Minuten, um die E-Mail zu senden"),
        "waitingList": MessageLookupByLibrary.simpleMessage("Warteliste"),
        "web": MessageLookupByLibrary.simpleMessage("Web"),
        "welcome": MessageLookupByLibrary.simpleMessage("Willkommen"),
        "whenUsingMobileData": MessageLookupByLibrary.simpleMessage(
            "Bei Verwendung von mobilen Daten"),
        "whenUsingWifi":
            MessageLookupByLibrary.simpleMessage("Bei Verwendung von WLAN"),
        "windows": MessageLookupByLibrary.simpleMessage("Windows"),
        "writeACaption": MessageLookupByLibrary.simpleMessage(
            "Schreibe eine Bildunterschrift..."),
        "yes": MessageLookupByLibrary.simpleMessage("Ja"),
        "yesterday": MessageLookupByLibrary.simpleMessage("Gestern"),
        "you": MessageLookupByLibrary.simpleMessage("Du"),
        "youAreAboutToDeleteThisUserFromYourList":
            MessageLookupByLibrary.simpleMessage(
                "Du bist dabei, diesen Benutzer aus deiner Liste zu löschen"),
        "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList":
            MessageLookupByLibrary.simpleMessage(
                "Sie stehen kurz davor, Ihr Konto zu löschen. Ihr Konto wird nicht mehr in der Benutzerliste angezeigt."),
        "youAreAboutToDismissesToMember": MessageLookupByLibrary.simpleMessage(
            "Du stehst kurz davor, ein Mitglied abzuweisen"),
        "youAreAboutToKick": MessageLookupByLibrary.simpleMessage(
            "Du stehst kurz davor, zu entfernen"),
        "youAreAboutToUpgradeToAdmin": MessageLookupByLibrary.simpleMessage(
            "Du stehst kurz davor, zum Administrator befördert zu werden"),
        "youDontHaveAccess":
            MessageLookupByLibrary.simpleMessage("Du hast keinen Zugriff"),
        "youInPublicSearch": MessageLookupByLibrary.simpleMessage(
            "Sie in der öffentlichen Suche"),
        "youNotParticipantInThisGroup": MessageLookupByLibrary.simpleMessage(
            "Sie sind kein Teilnehmer dieser Gruppe"),
        "yourAccountBlocked":
            MessageLookupByLibrary.simpleMessage("Ihr Konto wurde gesperrt"),
        "yourAccountDeleted":
            MessageLookupByLibrary.simpleMessage("Ihr Konto wurde gelöscht"),
        "yourAccountIsUnderReview":
            MessageLookupByLibrary.simpleMessage("Ihr Konto wird überprüft"),
        "yourAreAboutToLogoutFromThisAccount":
            MessageLookupByLibrary.simpleMessage(
                "Sie stehen kurz davor, sich von diesem Konto abzumelden"),
        "yourLastSeen": MessageLookupByLibrary.simpleMessage("Zuletzt gesehen"),
        "yourLastSeenInChats":
            MessageLookupByLibrary.simpleMessage("Zuletzt gesehen in Chats"),
        "yourProfileAppearsInPublicSearchAndAddingForGroups":
            MessageLookupByLibrary.simpleMessage(
                "Ihr Profil wird in der öffentlichen Suche angezeigt und für Gruppen hinzugefügt"),
        "yourSessionIsEndedPleaseLoginAgain":
            MessageLookupByLibrary.simpleMessage(
                "Ihre Sitzung wurde beendet. Bitte melden Sie sich erneut an!"),
        "yourStory": MessageLookupByLibrary.simpleMessage("Deine Geschichte")
      };
}
