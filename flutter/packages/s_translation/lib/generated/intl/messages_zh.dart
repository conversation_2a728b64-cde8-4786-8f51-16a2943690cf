// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Dashboard": MessageLookupByLibrary.simpleMessage("仪表板"),
        "about": MessageLookupByLibrary.simpleMessage("关于"),
        "aboutToBlockUserWithConsequences":
            MessageLookupByLibrary.simpleMessage(
                "您即将屏蔽此用户。您将无法向他发送消息，也无法将他添加到群组或广播中！"),
        "accepted": MessageLookupByLibrary.simpleMessage("已接受"),
        "account": MessageLookupByLibrary.simpleMessage("帐号"),
        "actions": MessageLookupByLibrary.simpleMessage("操作"),
        "addMembers": MessageLookupByLibrary.simpleMessage("添加成员"),
        "addNewStory": MessageLookupByLibrary.simpleMessage("添加新故事"),
        "addParticipants": MessageLookupByLibrary.simpleMessage("添加参与者"),
        "addedYouToNewBroadcast":
            MessageLookupByLibrary.simpleMessage("将您添加到新广播"),
        "admin": MessageLookupByLibrary.simpleMessage("管理员"),
        "adminNotification": MessageLookupByLibrary.simpleMessage("管理员通知"),
        "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself":
            MessageLookupByLibrary.simpleMessage(
                "所有数据已备份，您无需自行管理数据保存！如果您注销然后重新登录，您将看到与Web版本相同的所有聊天记录"),
        "allDeletedMessages": MessageLookupByLibrary.simpleMessage("所有已删除消息"),
        "allowAds": MessageLookupByLibrary.simpleMessage("允许广告"),
        "allowCalls": MessageLookupByLibrary.simpleMessage("允许通话"),
        "allowCreateBroadcast": MessageLookupByLibrary.simpleMessage("允许创建广播"),
        "allowCreateGroups": MessageLookupByLibrary.simpleMessage("允许创建群组"),
        "allowDesktopLogin": MessageLookupByLibrary.simpleMessage("允许桌面登录"),
        "allowMobileLogin": MessageLookupByLibrary.simpleMessage("允许移动登录"),
        "allowSendMedia": MessageLookupByLibrary.simpleMessage("允许发送媒体文件"),
        "allowWebLogin": MessageLookupByLibrary.simpleMessage("允许Web登录"),
        "alreadyHaveAnAccount": MessageLookupByLibrary.simpleMessage("已经有账号？"),
        "android": MessageLookupByLibrary.simpleMessage("Android"),
        "appMembers": MessageLookupByLibrary.simpleMessage("应用程序成员"),
        "appleStoreAppUrl":
            MessageLookupByLibrary.simpleMessage("Apple App Store 链接"),
        "areYouSure": MessageLookupByLibrary.simpleMessage("您确定吗"),
        "areYouSureToBlock": MessageLookupByLibrary.simpleMessage("您确定要屏蔽"),
        "areYouSureToLeaveThisGroupThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage("您确定要离开此群组吗？此操作无法撤销"),
        "areYouSureToPermitYourCopyThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage("您确定要允许复制吗？此操作无法撤销"),
        "areYouSureToReportUserToAdmin":
            MessageLookupByLibrary.simpleMessage("您确定要向管理员举报此用户吗？"),
        "areYouSureToUnBlock":
            MessageLookupByLibrary.simpleMessage("您确定要取消屏蔽吗"),
        "areYouWantToMakeVideoCall":
            MessageLookupByLibrary.simpleMessage("您想进行视频通话吗？"),
        "areYouWantToMakeVoiceCall":
            MessageLookupByLibrary.simpleMessage("您想进行语音通话吗？"),
        "audio": MessageLookupByLibrary.simpleMessage("音频"),
        "audioCall": MessageLookupByLibrary.simpleMessage("音频通话"),
        "back": MessageLookupByLibrary.simpleMessage("返回"),
        "banAt": MessageLookupByLibrary.simpleMessage("封禁于"),
        "banTo": MessageLookupByLibrary.simpleMessage("封禁至"),
        "bio": MessageLookupByLibrary.simpleMessage("简介"),
        "block": MessageLookupByLibrary.simpleMessage("屏蔽"),
        "blockUser": MessageLookupByLibrary.simpleMessage("屏蔽用户"),
        "blocked": MessageLookupByLibrary.simpleMessage("已阻止"),
        "blockedUsers": MessageLookupByLibrary.simpleMessage("已屏蔽用户"),
        "broadcast": MessageLookupByLibrary.simpleMessage("广播"),
        "broadcastInfo": MessageLookupByLibrary.simpleMessage("广播信息"),
        "broadcastMembers": MessageLookupByLibrary.simpleMessage("广播成员"),
        "broadcastName": MessageLookupByLibrary.simpleMessage("广播名称"),
        "broadcastParticipants": MessageLookupByLibrary.simpleMessage("广播参与者"),
        "broadcastSettings": MessageLookupByLibrary.simpleMessage("广播设置"),
        "callNotAllowed": MessageLookupByLibrary.simpleMessage("不允许通话"),
        "callTimeoutInSeconds": MessageLookupByLibrary.simpleMessage("呼叫超时（秒）"),
        "calls": MessageLookupByLibrary.simpleMessage("通话"),
        "camera": MessageLookupByLibrary.simpleMessage("相机"),
        "cancel": MessageLookupByLibrary.simpleMessage("取消"),
        "canceled": MessageLookupByLibrary.simpleMessage("已取消"),
        "changeSubject": MessageLookupByLibrary.simpleMessage("更改主题"),
        "chat": MessageLookupByLibrary.simpleMessage("聊天"),
        "chats": MessageLookupByLibrary.simpleMessage("聊天"),
        "checkForUpdates": MessageLookupByLibrary.simpleMessage("检查更新"),
        "chooseAtLestOneMember":
            MessageLookupByLibrary.simpleMessage("至少选择一个成员"),
        "chooseHowAutomaticDownloadWorks":
            MessageLookupByLibrary.simpleMessage("选择自动下载的工作方式"),
        "chooseRoom": MessageLookupByLibrary.simpleMessage("选择房间"),
        "clear": MessageLookupByLibrary.simpleMessage("清除"),
        "clearCallsConfirm": MessageLookupByLibrary.simpleMessage("确认清除通话记录？"),
        "clearChat": MessageLookupByLibrary.simpleMessage("清除聊天"),
        "clickToAddGroupDescription":
            MessageLookupByLibrary.simpleMessage("点击添加群组描述"),
        "clickToSee": MessageLookupByLibrary.simpleMessage("点击查看"),
        "clickToSeeAllUserCountries":
            MessageLookupByLibrary.simpleMessage("点击查看所有用户国家"),
        "clickToSeeAllUserDevicesDetails":
            MessageLookupByLibrary.simpleMessage("点击查看所有用户设备详细信息"),
        "clickToSeeAllUserInformations":
            MessageLookupByLibrary.simpleMessage("点击查看所有用户信息"),
        "clickToSeeAllUserMessagesDetails":
            MessageLookupByLibrary.simpleMessage("点击查看所有用户消息详细信息"),
        "clickToSeeAllUserReports":
            MessageLookupByLibrary.simpleMessage("点击查看所有用户报告"),
        "clickToSeeAllUserRoomsDetails":
            MessageLookupByLibrary.simpleMessage("点击查看所有用户房间详细信息"),
        "close": MessageLookupByLibrary.simpleMessage("关闭"),
        "codeHasBeenExpired": MessageLookupByLibrary.simpleMessage("验证码已过期"),
        "codeMustEqualToSixNumbers":
            MessageLookupByLibrary.simpleMessage("验证码必须是六位数字"),
        "configureYourAccountPrivacy":
            MessageLookupByLibrary.simpleMessage("配置您的帐户隐私"),
        "confirmPassword": MessageLookupByLibrary.simpleMessage("确认密码"),
        "confirmPasswordMustHaveValue":
            MessageLookupByLibrary.simpleMessage("确认密码必须填写"),
        "congregationsYourAccountHasBeenAccepted":
            MessageLookupByLibrary.simpleMessage("恭喜，您的帐号已被接受"),
        "connecting": MessageLookupByLibrary.simpleMessage("连接中..."),
        "contactInfo": MessageLookupByLibrary.simpleMessage("联系信息"),
        "contactUs": MessageLookupByLibrary.simpleMessage("联系我们"),
        "copy": MessageLookupByLibrary.simpleMessage("复制"),
        "countries": MessageLookupByLibrary.simpleMessage("国家"),
        "country": MessageLookupByLibrary.simpleMessage("国家"),
        "create": MessageLookupByLibrary.simpleMessage("创建"),
        "createBroadcast": MessageLookupByLibrary.simpleMessage("创建广播"),
        "createGroup": MessageLookupByLibrary.simpleMessage("创建群组"),
        "createMediaStory": MessageLookupByLibrary.simpleMessage("创建媒体故事"),
        "createStory": MessageLookupByLibrary.simpleMessage("创作故事"),
        "createTextStory": MessageLookupByLibrary.simpleMessage("创建文本故事"),
        "createYourStory": MessageLookupByLibrary.simpleMessage("创建您的故事"),
        "createdAt": MessageLookupByLibrary.simpleMessage("创建于"),
        "creator": MessageLookupByLibrary.simpleMessage("创建者"),
        "currentDevice": MessageLookupByLibrary.simpleMessage("当前设备"),
        "dashboard": MessageLookupByLibrary.simpleMessage("仪表板"),
        "dataPrivacy": MessageLookupByLibrary.simpleMessage("数据隐私"),
        "delete": MessageLookupByLibrary.simpleMessage("删除"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("删除聊天"),
        "deleteFromAll": MessageLookupByLibrary.simpleMessage("从所有人中删除"),
        "deleteFromMe": MessageLookupByLibrary.simpleMessage("从我这里删除"),
        "deleteMember": MessageLookupByLibrary.simpleMessage("删除成员"),
        "deleteMyAccount": MessageLookupByLibrary.simpleMessage("删除我的帐号"),
        "deleteThisDeviceDesc":
            MessageLookupByLibrary.simpleMessage("删除此设备意味着立即注销此设备"),
        "deleteUser": MessageLookupByLibrary.simpleMessage("删除用户"),
        "deleteYouCopy": MessageLookupByLibrary.simpleMessage("删除你的副本"),
        "deleted": MessageLookupByLibrary.simpleMessage("已删除"),
        "deletedAt": MessageLookupByLibrary.simpleMessage("已删除于"),
        "delivered": MessageLookupByLibrary.simpleMessage("已送达"),
        "description": MessageLookupByLibrary.simpleMessage("描述"),
        "descriptionIsRequired": MessageLookupByLibrary.simpleMessage("描述必填"),
        "desktopAndOtherDevices":
            MessageLookupByLibrary.simpleMessage("桌面和其他设备"),
        "deviceHasBeenLogoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage("设备已从所有设备注销"),
        "deviceStatus": MessageLookupByLibrary.simpleMessage("设备状态"),
        "devices": MessageLookupByLibrary.simpleMessage("设备"),
        "directChat": MessageLookupByLibrary.simpleMessage("直接聊天"),
        "directRooms": MessageLookupByLibrary.simpleMessage("直接房间"),
        "dismissedToMemberBy":
            MessageLookupByLibrary.simpleMessage("由以下成员撤销管理员权限："),
        "dismissesToMember": MessageLookupByLibrary.simpleMessage("撤销为成员"),
        "docs": MessageLookupByLibrary.simpleMessage("文档"),
        "done": MessageLookupByLibrary.simpleMessage("完成"),
        "download": MessageLookupByLibrary.simpleMessage("下载"),
        "downloading": MessageLookupByLibrary.simpleMessage("下载中..."),
        "edit": MessageLookupByLibrary.simpleMessage("编辑"),
        "email": MessageLookupByLibrary.simpleMessage("电子邮件"),
        "emailMustBeValid": MessageLookupByLibrary.simpleMessage("电子邮件必须有效"),
        "emailNotValid": MessageLookupByLibrary.simpleMessage("电子邮件无效"),
        "enterNameAndAddOptionalProfilePicture":
            MessageLookupByLibrary.simpleMessage("输入您的名称并添加可选的个人资料图片"),
        "error": MessageLookupByLibrary.simpleMessage("错误"),
        "exitGroup": MessageLookupByLibrary.simpleMessage("退出群组"),
        "explainWhatHappens": MessageLookupByLibrary.simpleMessage("在此解释发生了什么"),
        "feedBackEmail": MessageLookupByLibrary.simpleMessage("反馈电子邮件"),
        "fileHasBeenSavedTo": MessageLookupByLibrary.simpleMessage("文件已保存至"),
        "fileMessages": MessageLookupByLibrary.simpleMessage("文件消息"),
        "files": MessageLookupByLibrary.simpleMessage("文件"),
        "finished": MessageLookupByLibrary.simpleMessage("已结束"),
        "forRequest": MessageLookupByLibrary.simpleMessage("用于请求"),
        "forgetPassword": MessageLookupByLibrary.simpleMessage("忘记密码"),
        "forgetPasswordExpireTime":
            MessageLookupByLibrary.simpleMessage("忘记密码过期时间"),
        "forward": MessageLookupByLibrary.simpleMessage("转发"),
        "fullName": MessageLookupByLibrary.simpleMessage("全名"),
        "gallery": MessageLookupByLibrary.simpleMessage("相册"),
        "globalSearch": MessageLookupByLibrary.simpleMessage("全局搜索"),
        "googlePlayAppUrl":
            MessageLookupByLibrary.simpleMessage("Google Play 应用链接"),
        "group": MessageLookupByLibrary.simpleMessage("群组"),
        "groupCreatedBy": MessageLookupByLibrary.simpleMessage("群组由以下成员创建："),
        "groupDescription": MessageLookupByLibrary.simpleMessage("群组描述"),
        "groupIcon": MessageLookupByLibrary.simpleMessage("群组图标"),
        "groupInfo": MessageLookupByLibrary.simpleMessage("群组信息"),
        "groupMembers": MessageLookupByLibrary.simpleMessage("群组成员"),
        "groupName": MessageLookupByLibrary.simpleMessage("群组名称"),
        "groupParticipants": MessageLookupByLibrary.simpleMessage("群组参与者"),
        "groupSettings": MessageLookupByLibrary.simpleMessage("群组设置"),
        "groupWith": MessageLookupByLibrary.simpleMessage("与以下成员组成群组："),
        "harassmentOrBullyingDescription": MessageLookupByLibrary.simpleMessage(
            "骚扰或欺凌：此选项允许用户举报针对他们或其他人的骚扰消息、威胁或其他形式的欺凌。"),
        "help": MessageLookupByLibrary.simpleMessage("帮助"),
        "hiIamUse": MessageLookupByLibrary.simpleMessage("您好，我在使用"),
        "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked":
            MessageLookupByLibrary.simpleMessage("如果禁用此选项，将阻止创建聊天广播"),
        "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked":
            MessageLookupByLibrary.simpleMessage("如果禁用此选项，将阻止创建聊天群组"),
        "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "如果禁用此选项，将阻止桌面登录或注册（Windows 和 macOS）"),
        "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly":
            MessageLookupByLibrary.simpleMessage(
                "如果启用此选项，Google Ads 横幅将出现在聊天中"),
        "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "如果禁用此选项，将阻止发送聊天文件、图片、视频和位置信息"),
        "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked":
            MessageLookupByLibrary.simpleMessage("如果禁用此选项，将阻止Web登录或注册"),
        "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed":
            MessageLookupByLibrary.simpleMessage("如果启用此选项，视频和语音通话将被允许"),
        "image": MessageLookupByLibrary.simpleMessage("图片"),
        "imageMessages": MessageLookupByLibrary.simpleMessage("图片消息"),
        "images": MessageLookupByLibrary.simpleMessage("图片"),
        "inAppAlerts": MessageLookupByLibrary.simpleMessage("应用内警报"),
        "inCall": MessageLookupByLibrary.simpleMessage("通话中"),
        "inappropriateContentDescription": MessageLookupByLibrary.simpleMessage(
            "不适当内容：用户可以选择此选项以举报任何涉及性暴露、仇恨言论或其他违反社区标准的内容。"),
        "info": MessageLookupByLibrary.simpleMessage("信息"),
        "infoMessages": MessageLookupByLibrary.simpleMessage("信息消息"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("无效的验证码"),
        "invalidLoginData": MessageLookupByLibrary.simpleMessage("无效的登录数据"),
        "ios": MessageLookupByLibrary.simpleMessage("iOS"),
        "joinedAt": MessageLookupByLibrary.simpleMessage("加入于"),
        "joinedBy": MessageLookupByLibrary.simpleMessage("由以下成员加入："),
        "kickMember": MessageLookupByLibrary.simpleMessage("踢出成员"),
        "kickedBy": MessageLookupByLibrary.simpleMessage("被以下成员踢出群组："),
        "language": MessageLookupByLibrary.simpleMessage("语言"),
        "lastActiveFrom": MessageLookupByLibrary.simpleMessage("上次活动时间："),
        "leaveGroup": MessageLookupByLibrary.simpleMessage("退出群组"),
        "leaveGroupAndDeleteYourMessageCopy":
            MessageLookupByLibrary.simpleMessage("退出群组并删除您的消息副本"),
        "leftTheGroup": MessageLookupByLibrary.simpleMessage("退出了群组"),
        "linkADeviceSoon": MessageLookupByLibrary.simpleMessage("链接设备（即将推出）"),
        "linkByQrCode": MessageLookupByLibrary.simpleMessage("通过QR码链接"),
        "linkedDevices": MessageLookupByLibrary.simpleMessage("已链接设备"),
        "links": MessageLookupByLibrary.simpleMessage("链接"),
        "loading": MessageLookupByLibrary.simpleMessage("加载中..."),
        "location": MessageLookupByLibrary.simpleMessage("位置"),
        "locationMessages": MessageLookupByLibrary.simpleMessage("位置消息"),
        "logOut": MessageLookupByLibrary.simpleMessage("注销"),
        "login": MessageLookupByLibrary.simpleMessage("登录"),
        "loginAgain": MessageLookupByLibrary.simpleMessage("重新登录！"),
        "loginNowAllowedNowPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage("现在允许登录。请稍后再试。"),
        "logoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage("从所有设备注销？"),
        "macOs": MessageLookupByLibrary.simpleMessage("macOS"),
        "makeCall": MessageLookupByLibrary.simpleMessage("发起通话"),
        "media": MessageLookupByLibrary.simpleMessage("媒体"),
        "mediaLinksAndDocs": MessageLookupByLibrary.simpleMessage("媒体、链接和文档"),
        "member": MessageLookupByLibrary.simpleMessage("成员"),
        "members": MessageLookupByLibrary.simpleMessage("成员"),
        "messageCounter": MessageLookupByLibrary.simpleMessage("消息计数器"),
        "messageHasBeenDeleted": MessageLookupByLibrary.simpleMessage("消息已被删除"),
        "messageHasBeenViewed": MessageLookupByLibrary.simpleMessage("消息已查看"),
        "messageInfo": MessageLookupByLibrary.simpleMessage("消息信息"),
        "messages": MessageLookupByLibrary.simpleMessage("消息"),
        "microphoneAndCameraPermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone and camera permission must be accepted"),
        "microphonePermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone permission must be accepted"),
        "minutes": MessageLookupByLibrary.simpleMessage("分钟"),
        "more": MessageLookupByLibrary.simpleMessage("更多"),
        "mute": MessageLookupByLibrary.simpleMessage("静音"),
        "muteNotifications": MessageLookupByLibrary.simpleMessage("静音通知"),
        "myPrivacy": MessageLookupByLibrary.simpleMessage("我的隐私"),
        "name": MessageLookupByLibrary.simpleMessage("名称"),
        "nameMustHaveValue": MessageLookupByLibrary.simpleMessage("名称必须填写"),
        "needNewAccount": MessageLookupByLibrary.simpleMessage("需要新账号？"),
        "newBroadcast": MessageLookupByLibrary.simpleMessage("新广播"),
        "newGroup": MessageLookupByLibrary.simpleMessage("新群组"),
        "newPassword": MessageLookupByLibrary.simpleMessage("新密码"),
        "newPasswordMustHaveValue":
            MessageLookupByLibrary.simpleMessage("新密码必须填写"),
        "newUpdateIsAvailable": MessageLookupByLibrary.simpleMessage("有新更新可用"),
        "next": MessageLookupByLibrary.simpleMessage("下一步"),
        "nickname": MessageLookupByLibrary.simpleMessage("昵称"),
        "no": MessageLookupByLibrary.simpleMessage("否"),
        "noBio": MessageLookupByLibrary.simpleMessage("无简介"),
        "noCodeHasBeenSendToYouToVerifyYourEmail":
            MessageLookupByLibrary.simpleMessage("尚未发送验证码到您的电子邮件以验证您的电子邮件"),
        "noUpdatesAvailableNow":
            MessageLookupByLibrary.simpleMessage("目前没有可用更新"),
        "none": MessageLookupByLibrary.simpleMessage("无"),
        "notAccepted": MessageLookupByLibrary.simpleMessage("未接受"),
        "notification": MessageLookupByLibrary.simpleMessage("通知"),
        "notificationDescription": MessageLookupByLibrary.simpleMessage("通知描述"),
        "notificationTitle": MessageLookupByLibrary.simpleMessage("通知标题"),
        "notificationsPage": MessageLookupByLibrary.simpleMessage("通知页面"),
        "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion":
            MessageLookupByLibrary.simpleMessage(
                "现在您以只读管理员身份登录。由于这是测试版本，您所做的所有编辑都将不会生效。"),
        "off": MessageLookupByLibrary.simpleMessage("关"),
        "offline": MessageLookupByLibrary.simpleMessage("离线"),
        "ok": MessageLookupByLibrary.simpleMessage("确定"),
        "oldPassword": MessageLookupByLibrary.simpleMessage("旧密码"),
        "on": MessageLookupByLibrary.simpleMessage("开"),
        "oneSeenMessage": MessageLookupByLibrary.simpleMessage("已看过的消息"),
        "online": MessageLookupByLibrary.simpleMessage("在线"),
        "orLoginWith": MessageLookupByLibrary.simpleMessage("或使用以下方式登录"),
        "other": MessageLookupByLibrary.simpleMessage("其他"),
        "otherCategoryDescription": MessageLookupByLibrary.simpleMessage(
            "其他：这是一个通用类别，适用于不容易归类到上述类别的违规行为。可以提供一个文本框供用户提供额外的细节。"),
        "otpCode": MessageLookupByLibrary.simpleMessage("OTP验证码"),
        "password": MessageLookupByLibrary.simpleMessage("密码"),
        "passwordHasBeenChanged": MessageLookupByLibrary.simpleMessage("密码已更改"),
        "passwordIsRequired": MessageLookupByLibrary.simpleMessage("密码必填"),
        "passwordMustHaveValue": MessageLookupByLibrary.simpleMessage("密码必须填写"),
        "passwordNotMatch": MessageLookupByLibrary.simpleMessage("密码不匹配"),
        "peerUserDeviceOffline":
            MessageLookupByLibrary.simpleMessage("对方用户设备离线"),
        "peerUserInCallNow": MessageLookupByLibrary.simpleMessage("对方用户正在通话中"),
        "pending": MessageLookupByLibrary.simpleMessage("待处理"),
        "phone": MessageLookupByLibrary.simpleMessage("电话"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("隐私政策"),
        "privacyUrl": MessageLookupByLibrary.simpleMessage("隐私政策链接"),
        "profile": MessageLookupByLibrary.simpleMessage("个人资料"),
        "promotedToAdminBy":
            MessageLookupByLibrary.simpleMessage("由以下成员提升为管理员："),
        "public": MessageLookupByLibrary.simpleMessage("公共"),
        "read": MessageLookupByLibrary.simpleMessage("已阅读"),
        "recentUpdate": MessageLookupByLibrary.simpleMessage("最近更新"),
        "recentUpdates": MessageLookupByLibrary.simpleMessage("最近更新"),
        "recording": MessageLookupByLibrary.simpleMessage("录音中..."),
        "register": MessageLookupByLibrary.simpleMessage("注册"),
        "registerMethod": MessageLookupByLibrary.simpleMessage("注册方式"),
        "registerStatus": MessageLookupByLibrary.simpleMessage("注册状态"),
        "rejected": MessageLookupByLibrary.simpleMessage("已拒绝"),
        "repliedToYourSelf": MessageLookupByLibrary.simpleMessage("已回复自己"),
        "reply": MessageLookupByLibrary.simpleMessage("回复"),
        "replyToYourSelf": MessageLookupByLibrary.simpleMessage("回复自己"),
        "report": MessageLookupByLibrary.simpleMessage("举报"),
        "reportHasBeenSubmitted":
            MessageLookupByLibrary.simpleMessage("您的报告已提交"),
        "reportUser": MessageLookupByLibrary.simpleMessage("举报用户"),
        "reports": MessageLookupByLibrary.simpleMessage("报告"),
        "resetPassword": MessageLookupByLibrary.simpleMessage("重置密码"),
        "retry": MessageLookupByLibrary.simpleMessage("重试"),
        "ring": MessageLookupByLibrary.simpleMessage("响铃"),
        "roomAlreadyInCall": MessageLookupByLibrary.simpleMessage("房间已在通话中"),
        "roomCounter": MessageLookupByLibrary.simpleMessage("房间计数器"),
        "saveLogin": MessageLookupByLibrary.simpleMessage("保存登录"),
        "search": MessageLookupByLibrary.simpleMessage("搜索"),
        "seconds": MessageLookupByLibrary.simpleMessage("秒"),
        "send": MessageLookupByLibrary.simpleMessage("发送"),
        "sendCodeToMyEmail":
            MessageLookupByLibrary.simpleMessage("发送验证码到我的电子邮件"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("发送消息"),
        "sessionEnd": MessageLookupByLibrary.simpleMessage("会话结束"),
        "setMaxBroadcastMembers":
            MessageLookupByLibrary.simpleMessage("设置最大广播成员数"),
        "setMaxGroupMembers": MessageLookupByLibrary.simpleMessage("设置最大群组成员数"),
        "setMaxMessageForwardAndShare":
            MessageLookupByLibrary.simpleMessage("设置最大消息转发和分享数"),
        "setNewPrivacyPolicyUrl":
            MessageLookupByLibrary.simpleMessage("设置新的隐私政策链接"),
        "setToAdmin": MessageLookupByLibrary.simpleMessage("设为管理员"),
        "settings": MessageLookupByLibrary.simpleMessage("设置"),
        "share": MessageLookupByLibrary.simpleMessage("分享"),
        "shareMediaAndLocation":
            MessageLookupByLibrary.simpleMessage("分享媒体和位置"),
        "shareYourStatus": MessageLookupByLibrary.simpleMessage("分享您的状态"),
        "showHistory": MessageLookupByLibrary.simpleMessage("显示历史记录"),
        "showMedia": MessageLookupByLibrary.simpleMessage("显示媒体"),
        "soon": MessageLookupByLibrary.simpleMessage("即将"),
        "spamOrScamDescription": MessageLookupByLibrary.simpleMessage(
            "垃圾邮件或欺诈：此选项可用于举报发送垃圾邮件消息、未经请求的广告或试图欺诈他人的帐户。"),
        "star": MessageLookupByLibrary.simpleMessage("加星标"),
        "starMessage": MessageLookupByLibrary.simpleMessage("加星标消息"),
        "starredMessage": MessageLookupByLibrary.simpleMessage("已加星标消息"),
        "starredMessages": MessageLookupByLibrary.simpleMessage("加星标的消息"),
        "startChat": MessageLookupByLibrary.simpleMessage("开始聊天"),
        "startNewChatWithYou": MessageLookupByLibrary.simpleMessage("与您开始新的聊天"),
        "status": MessageLookupByLibrary.simpleMessage("状态"),
        "storageAndData": MessageLookupByLibrary.simpleMessage("存储和数据"),
        "stories": MessageLookupByLibrary.simpleMessage("故事"),
        "storyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("故事创建成功"),
        "success": MessageLookupByLibrary.simpleMessage("成功"),
        "successfullyDownloadedIn":
            MessageLookupByLibrary.simpleMessage("成功下载至"),
        "supportChatSoon": MessageLookupByLibrary.simpleMessage("支持聊天（即将推出）"),
        "tapADeviceToEditOrLogOut":
            MessageLookupByLibrary.simpleMessage("点击设备以编辑或注销。"),
        "tapForPhoto": MessageLookupByLibrary.simpleMessage("点击以查看照片"),
        "tapToSelectAnIcon": MessageLookupByLibrary.simpleMessage("点击选择图标"),
        "tellAFriend": MessageLookupByLibrary.simpleMessage("告诉朋友"),
        "textFieldHint": MessageLookupByLibrary.simpleMessage("输入消息..."),
        "textMessages": MessageLookupByLibrary.simpleMessage("文本消息"),
        "thereIsFileHasSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage("存在大小超出允许的文件"),
        "thereIsVideoSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage("存在大小超出允许的视频"),
        "timeout": MessageLookupByLibrary.simpleMessage("超时"),
        "titleIsRequired": MessageLookupByLibrary.simpleMessage("需要标题"),
        "today": MessageLookupByLibrary.simpleMessage("今天"),
        "total": MessageLookupByLibrary.simpleMessage("总计"),
        "totalMessages": MessageLookupByLibrary.simpleMessage("总消息数"),
        "totalRooms": MessageLookupByLibrary.simpleMessage("总房间数"),
        "totalVisits": MessageLookupByLibrary.simpleMessage("总访问量"),
        "typing": MessageLookupByLibrary.simpleMessage("正在输入..."),
        "unBlock": MessageLookupByLibrary.simpleMessage("取消屏蔽"),
        "unBlockUser": MessageLookupByLibrary.simpleMessage("取消屏蔽用户"),
        "unMute": MessageLookupByLibrary.simpleMessage("取消静音"),
        "unStar": MessageLookupByLibrary.simpleMessage("取消加星标"),
        "update": MessageLookupByLibrary.simpleMessage("更新"),
        "updateBroadcastTitle": MessageLookupByLibrary.simpleMessage("更新广播标题"),
        "updateFeedBackEmail": MessageLookupByLibrary.simpleMessage("更新反馈电子邮件"),
        "updateGroupDescription":
            MessageLookupByLibrary.simpleMessage("更新群组描述"),
        "updateGroupDescriptionWillUpdateAllGroupMembers":
            MessageLookupByLibrary.simpleMessage("更新群组描述将更新所有群组成员"),
        "updateGroupTitle": MessageLookupByLibrary.simpleMessage("更新群组标题"),
        "updateImage": MessageLookupByLibrary.simpleMessage("更新图片"),
        "updateNickname": MessageLookupByLibrary.simpleMessage("更新昵称"),
        "updateTitle": MessageLookupByLibrary.simpleMessage("更新标题"),
        "updateTitleTo": MessageLookupByLibrary.simpleMessage("更新标题为"),
        "updateYourBio": MessageLookupByLibrary.simpleMessage("更新您的个人简介"),
        "updateYourName": MessageLookupByLibrary.simpleMessage("更新您的名称"),
        "updateYourPassword": MessageLookupByLibrary.simpleMessage("更新您的密码"),
        "updateYourProfile": MessageLookupByLibrary.simpleMessage("更新您的个人资料"),
        "updatedAt": MessageLookupByLibrary.simpleMessage("更新于"),
        "upgradeToAdmin": MessageLookupByLibrary.simpleMessage("升级为管理员"),
        "userAction": MessageLookupByLibrary.simpleMessage("用户操作"),
        "userAlreadyRegister": MessageLookupByLibrary.simpleMessage("用户已注册"),
        "userDeviceSessionEndDeviceDeleted":
            MessageLookupByLibrary.simpleMessage("用户设备会话结束设备已删除"),
        "userEmailNotFound": MessageLookupByLibrary.simpleMessage("未找到用户电子邮件"),
        "userInfo": MessageLookupByLibrary.simpleMessage("用户信息"),
        "userPage": MessageLookupByLibrary.simpleMessage("用户页面"),
        "userProfile": MessageLookupByLibrary.simpleMessage("用户个人资料"),
        "userRegisterStatusNotAcceptedYet":
            MessageLookupByLibrary.simpleMessage("用户注册状态尚未被接受"),
        "users": MessageLookupByLibrary.simpleMessage("用户"),
        "usersAddedSuccessfully":
            MessageLookupByLibrary.simpleMessage("成功添加用户"),
        "vMessageInfoTrans": MessageLookupByLibrary.simpleMessage("消息信息"),
        "vMessagesInfoTrans": MessageLookupByLibrary.simpleMessage("消息信息"),
        "verified": MessageLookupByLibrary.simpleMessage("已验证"),
        "verifiedAt": MessageLookupByLibrary.simpleMessage("已验证"),
        "video": MessageLookupByLibrary.simpleMessage("视频"),
        "videoCallMessages": MessageLookupByLibrary.simpleMessage("视频通话消息"),
        "videoMessages": MessageLookupByLibrary.simpleMessage("视频消息"),
        "visits": MessageLookupByLibrary.simpleMessage("访问次数"),
        "voiceCallMessage": MessageLookupByLibrary.simpleMessage("语音通话消息"),
        "voiceCallMessages": MessageLookupByLibrary.simpleMessage("语音通话消息"),
        "voiceMessages": MessageLookupByLibrary.simpleMessage("语音消息"),
        "wait2MinutesToSendMail":
            MessageLookupByLibrary.simpleMessage("等待2分钟发送邮件"),
        "waitingList": MessageLookupByLibrary.simpleMessage("等待名单"),
        "weHighRecommendToDownloadThisUpdate":
            MessageLookupByLibrary.simpleMessage("我们强烈建议您下载此更新"),
        "web": MessageLookupByLibrary.simpleMessage("Web"),
        "welcome": MessageLookupByLibrary.simpleMessage("欢迎"),
        "whenUsingMobileData": MessageLookupByLibrary.simpleMessage("使用移动数据时"),
        "whenUsingWifi": MessageLookupByLibrary.simpleMessage("使用 Wi-Fi 时"),
        "whileAuthCanFindYou":
            MessageLookupByLibrary.simpleMessage("在认证期间找不到您"),
        "windows": MessageLookupByLibrary.simpleMessage("Windows"),
        "writeACaption": MessageLookupByLibrary.simpleMessage("写标题..."),
        "yes": MessageLookupByLibrary.simpleMessage("是"),
        "yesterday": MessageLookupByLibrary.simpleMessage("昨天"),
        "you": MessageLookupByLibrary.simpleMessage("您"),
        "youAreAboutToDeleteThisUserFromYourList":
            MessageLookupByLibrary.simpleMessage("您即将从列表中删除此用户"),
        "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList":
            MessageLookupByLibrary.simpleMessage("您即将删除您的帐号，您的帐号将不再出现在用户列表中"),
        "youAreAboutToDismissesToMember":
            MessageLookupByLibrary.simpleMessage("您即将撤销为成员"),
        "youAreAboutToKick": MessageLookupByLibrary.simpleMessage("您即将踢出"),
        "youAreAboutToUpgradeToAdmin":
            MessageLookupByLibrary.simpleMessage("您即将升级为管理员"),
        "youDontHaveAccess": MessageLookupByLibrary.simpleMessage("您无权访问"),
        "youInPublicSearch": MessageLookupByLibrary.simpleMessage("您在公共搜索中"),
        "youNotParticipantInThisGroup":
            MessageLookupByLibrary.simpleMessage("您不是该群组的成员"),
        "yourAccountBlocked": MessageLookupByLibrary.simpleMessage("您的帐号已被封禁"),
        "yourAccountDeleted": MessageLookupByLibrary.simpleMessage("您的帐号已被删除"),
        "yourAccountIsUnderReview":
            MessageLookupByLibrary.simpleMessage("您的帐号正在审核中"),
        "yourAreAboutToLogoutFromThisAccount":
            MessageLookupByLibrary.simpleMessage("您即将从此帐号注销"),
        "yourLastSeen": MessageLookupByLibrary.simpleMessage("最后一次看到"),
        "yourLastSeenInChats":
            MessageLookupByLibrary.simpleMessage("在聊天中最后一次看到"),
        "yourProfileAppearsInPublicSearchAndAddingForGroups":
            MessageLookupByLibrary.simpleMessage("您的个人资料将显示在公共搜索和添加到群组中"),
        "yourSessionIsEndedPleaseLoginAgain":
            MessageLookupByLibrary.simpleMessage("您的会话已结束，请重新登录！"),
        "yourStory": MessageLookupByLibrary.simpleMessage("您的故事")
      };
}
