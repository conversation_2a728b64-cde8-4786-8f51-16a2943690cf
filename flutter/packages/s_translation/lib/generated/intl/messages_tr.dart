// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a tr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'tr';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Dashboard": MessageLookupByLibrary.simpleMessage("Kontrol Paneli"),
        "about": MessageLookupByLibrary.simpleMessage("Hakkında"),
        "aboutToBlockUserWithConsequences": MessageLookupByLibrary.simpleMessage(
            "Bu kullanıcıyı engellemek üzeresiniz. Bu kullanıcıya mesaj gönderemez ve gruplara veya yayınlara ekleyemezsiniz!"),
        "accepted": MessageLookupByLibrary.simpleMessage("Kabul edildi"),
        "account": MessageLookupByLibrary.simpleMessage("Hesap"),
        "actions": MessageLookupByLibrary.simpleMessage("Acciones"),
        "addMembers": MessageLookupByLibrary.simpleMessage("Üye Ekle"),
        "addNewStory": MessageLookupByLibrary.simpleMessage("Yeni hikaye ekle"),
        "addParticipants":
            MessageLookupByLibrary.simpleMessage("Agregar participantes"),
        "addedYouToNewBroadcast": MessageLookupByLibrary.simpleMessage(
            "Te agregó a una nueva difusión"),
        "admin": MessageLookupByLibrary.simpleMessage("Yönetici"),
        "adminNotification":
            MessageLookupByLibrary.simpleMessage("Yönetici Bildirimi"),
        "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself":
            MessageLookupByLibrary.simpleMessage(
                "Tüm veriler yedeklendi, verileri kendiniz yönetmeye veya kaydetmeye gerek yok! Çıkış yaparsanız ve tekrar giriş yaparsanız, tüm sohbetleri göreceksiniz, web sürümüyle aynı"),
        "allDeletedMessages":
            MessageLookupByLibrary.simpleMessage("Tüm Silinmiş Mesajlar"),
        "allowAds": MessageLookupByLibrary.simpleMessage("Reklamlara İzin Ver"),
        "allowCalls":
            MessageLookupByLibrary.simpleMessage("Aramalara İzin Ver"),
        "allowCreateBroadcast":
            MessageLookupByLibrary.simpleMessage("Yayın Oluşturmaya İzin Ver"),
        "allowCreateGroups":
            MessageLookupByLibrary.simpleMessage("Grup Oluşturmaya İzin Ver"),
        "allowDesktopLogin":
            MessageLookupByLibrary.simpleMessage("Masaüstü Girişine İzin Ver"),
        "allowMobileLogin":
            MessageLookupByLibrary.simpleMessage("Mobil Girişi İzin Ver"),
        "allowSendMedia":
            MessageLookupByLibrary.simpleMessage("Medya Gönderimine İzin Ver"),
        "allowWebLogin":
            MessageLookupByLibrary.simpleMessage("Web Girişi İzin Ver"),
        "alreadyHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("¿Ya tienes una cuenta?"),
        "android": MessageLookupByLibrary.simpleMessage("Android"),
        "appMembers": MessageLookupByLibrary.simpleMessage("Uygulama Üyeleri"),
        "appleStoreAppUrl":
            MessageLookupByLibrary.simpleMessage("Apple App Store URL\'si"),
        "areYouSure": MessageLookupByLibrary.simpleMessage("Emin misiniz?"),
        "areYouSureToBlock":
            MessageLookupByLibrary.simpleMessage("¿Estás seguro de bloquear a"),
        "areYouSureToLeaveThisGroupThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "¿Estás seguro de abandonar este grupo? Esta acción no se puede deshacer"),
        "areYouSureToPermitYourCopyThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "¿Estás seguro de permitir tu copia? Esta acción no se puede deshacer"),
        "areYouSureToReportUserToAdmin": MessageLookupByLibrary.simpleMessage(
            "Bu kullanıcı hakkında yöneticiye rapor göndermek istediğinizden emin misiniz?"),
        "areYouSureToUnBlock": MessageLookupByLibrary.simpleMessage(
            "Engeli kaldırmak istediğinizden emin misiniz?"),
        "areYouWantToMakeVideoCall": MessageLookupByLibrary.simpleMessage(
            "¿Deseas hacer una videollamada?"),
        "areYouWantToMakeVoiceCall": MessageLookupByLibrary.simpleMessage(
            "¿Deseas hacer una llamada de voz?"),
        "audio": MessageLookupByLibrary.simpleMessage("Ses"),
        "audioCall": MessageLookupByLibrary.simpleMessage("Llamada de audio"),
        "back": MessageLookupByLibrary.simpleMessage("Atrás"),
        "banAt": MessageLookupByLibrary.simpleMessage("Şu tarihte engellendi"),
        "banTo":
            MessageLookupByLibrary.simpleMessage("Şu tarihe kadar engelle"),
        "bio": MessageLookupByLibrary.simpleMessage("Hakkımda"),
        "block": MessageLookupByLibrary.simpleMessage("Engelle"),
        "blockUser": MessageLookupByLibrary.simpleMessage("Bloquear usuario"),
        "blocked": MessageLookupByLibrary.simpleMessage("Engellendi"),
        "blockedUsers":
            MessageLookupByLibrary.simpleMessage("Engellenen Kullanıcılar"),
        "broadcast": MessageLookupByLibrary.simpleMessage("Yayın"),
        "broadcastInfo": MessageLookupByLibrary.simpleMessage("Yayın Bilgisi"),
        "broadcastMembers":
            MessageLookupByLibrary.simpleMessage("Yayın Üyeleri"),
        "broadcastName":
            MessageLookupByLibrary.simpleMessage("Nombre de la difusión"),
        "broadcastParticipants": MessageLookupByLibrary.simpleMessage(
            "Participantes de la difusión"),
        "broadcastSettings": MessageLookupByLibrary.simpleMessage(
            "Configuración de la difusión"),
        "callNotAllowed":
            MessageLookupByLibrary.simpleMessage("Llamada no permitida"),
        "callTimeoutInSeconds": MessageLookupByLibrary.simpleMessage(
            "Arama Süresi Sınırlaması (saniye)"),
        "calls": MessageLookupByLibrary.simpleMessage("Aramalar"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancelar"),
        "canceled": MessageLookupByLibrary.simpleMessage("Cancelado"),
        "changeSubject": MessageLookupByLibrary.simpleMessage("Cambiar asunto"),
        "chat": MessageLookupByLibrary.simpleMessage("Sohbet"),
        "chats": MessageLookupByLibrary.simpleMessage("CHATS"),
        "checkForUpdates":
            MessageLookupByLibrary.simpleMessage("Güncellemeleri Kontrol Et"),
        "chooseAtLestOneMember":
            MessageLookupByLibrary.simpleMessage("En az bir üye seçin"),
        "chooseHowAutomaticDownloadWorks": MessageLookupByLibrary.simpleMessage(
            "Otomatik indirmenin nasıl çalışacağını seçin"),
        "chooseRoom": MessageLookupByLibrary.simpleMessage("Oda Seç"),
        "clear": MessageLookupByLibrary.simpleMessage("Temizle"),
        "clearCallsConfirm": MessageLookupByLibrary.simpleMessage(
            "Aramaları temizlemeyi onayla"),
        "clearChat": MessageLookupByLibrary.simpleMessage("Sohbeti Temizle"),
        "clickToAddGroupDescription": MessageLookupByLibrary.simpleMessage(
            "Grup açıklaması eklemek için tıklayın"),
        "clickToSee":
            MessageLookupByLibrary.simpleMessage("Görmek için tıklayın"),
        "clickToSeeAllUserCountries": MessageLookupByLibrary.simpleMessage(
            "Tüm Kullanıcı Ülkelerini Görmek İçin Tıklayın"),
        "clickToSeeAllUserDevicesDetails": MessageLookupByLibrary.simpleMessage(
            "Tüm Kullanıcı Cihaz Detaylarını Görmek İçin Tıklayın"),
        "clickToSeeAllUserInformations": MessageLookupByLibrary.simpleMessage(
            "Tüm Kullanıcı Bilgilerini Görmek İçin Tıklayın"),
        "clickToSeeAllUserMessagesDetails":
            MessageLookupByLibrary.simpleMessage(
                "Tüm Kullanıcı Mesaj Detaylarını Görmek İçin Tıklayın"),
        "clickToSeeAllUserReports": MessageLookupByLibrary.simpleMessage(
            "Tüm Kullanıcı Raporlarını Görmek İçin Tıklayın"),
        "clickToSeeAllUserRoomsDetails": MessageLookupByLibrary.simpleMessage(
            "Tüm Kullanıcı Oda Detaylarını Görmek İçin Tıklayın"),
        "close": MessageLookupByLibrary.simpleMessage("Kapat"),
        "codeHasBeenExpired":
            MessageLookupByLibrary.simpleMessage("El código ha caducado"),
        "configureYourAccountPrivacy": MessageLookupByLibrary.simpleMessage(
            "Hesap gizliliğinizi yapılandırın"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirmar contraseña"),
        "confirmPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "La confirmación de la contraseña debe tener un valor"),
        "congregationsYourAccountHasBeenAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Tebrikler, hesabınız kabul edildi"),
        "connecting": MessageLookupByLibrary.simpleMessage("Conectando..."),
        "contactInfo":
            MessageLookupByLibrary.simpleMessage("İletişim Bilgileri"),
        "contactUs": MessageLookupByLibrary.simpleMessage("Bize Ulaşın"),
        "copy": MessageLookupByLibrary.simpleMessage("Copiar"),
        "countries": MessageLookupByLibrary.simpleMessage("Ülkeler"),
        "country": MessageLookupByLibrary.simpleMessage("Ülke"),
        "create": MessageLookupByLibrary.simpleMessage("Oluştur"),
        "createBroadcast":
            MessageLookupByLibrary.simpleMessage("Crear difusión"),
        "createGroup": MessageLookupByLibrary.simpleMessage("Crear grupo"),
        "createMediaStory":
            MessageLookupByLibrary.simpleMessage("Medya hikayesi oluştur"),
        "createStory": MessageLookupByLibrary.simpleMessage("Hikaye oluştur"),
        "createTextStory":
            MessageLookupByLibrary.simpleMessage("Metin hikayesi oluştur"),
        "createYourStory":
            MessageLookupByLibrary.simpleMessage("Hikayenizi oluşturun"),
        "createdAt": MessageLookupByLibrary.simpleMessage("Oluşturulma Tarihi"),
        "creator": MessageLookupByLibrary.simpleMessage("Oluşturan"),
        "currentDevice": MessageLookupByLibrary.simpleMessage("Mevcut Cihaz"),
        "dashboard": MessageLookupByLibrary.simpleMessage("Kontrol Paneli"),
        "dataPrivacy": MessageLookupByLibrary.simpleMessage("Veri Gizliliği"),
        "delete": MessageLookupByLibrary.simpleMessage("Eliminar"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("Sohbeti Sil"),
        "deleteFromAll":
            MessageLookupByLibrary.simpleMessage("Eliminar de todos"),
        "deleteFromMe": MessageLookupByLibrary.simpleMessage("Eliminar de mí"),
        "deleteMember": MessageLookupByLibrary.simpleMessage("Üyeyi Sil"),
        "deleteMyAccount": MessageLookupByLibrary.simpleMessage("Hesabımı Sil"),
        "deleteThisDeviceDesc": MessageLookupByLibrary.simpleMessage(
            "Bu cihazı silmek, bu cihazdan hemen çıkış yapmak anlamına gelir"),
        "deleteUser": MessageLookupByLibrary.simpleMessage("Eliminar usuario"),
        "deleteYouCopy":
            MessageLookupByLibrary.simpleMessage("Eliminar tu copia"),
        "deleted": MessageLookupByLibrary.simpleMessage("Silindi"),
        "deletedAt": MessageLookupByLibrary.simpleMessage("Şu tarihte silindi"),
        "delivered": MessageLookupByLibrary.simpleMessage("Teslim Edildi"),
        "description": MessageLookupByLibrary.simpleMessage("Descripción"),
        "descriptionIsRequired":
            MessageLookupByLibrary.simpleMessage("Açıklama Gereklidir"),
        "desktopAndOtherDevices":
            MessageLookupByLibrary.simpleMessage("Masaüstü ve Diğer Cihazlar"),
        "deviceHasBeenLogoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage(
                "El dispositivo ha cerrado la sesión en todos los dispositivos"),
        "deviceStatus": MessageLookupByLibrary.simpleMessage("Cihaz Durumu"),
        "devices": MessageLookupByLibrary.simpleMessage("Cihazlar"),
        "directChat": MessageLookupByLibrary.simpleMessage("Doğrudan Sohbet"),
        "directRooms": MessageLookupByLibrary.simpleMessage("Doğrudan Odalar"),
        "dismissedToMemberBy":
            MessageLookupByLibrary.simpleMessage("Relegado a miembro por"),
        "dismissesToMember":
            MessageLookupByLibrary.simpleMessage("Relegar a miembro"),
        "docs": MessageLookupByLibrary.simpleMessage("Belgeler"),
        "done": MessageLookupByLibrary.simpleMessage("Hecho"),
        "download": MessageLookupByLibrary.simpleMessage("Descargar"),
        "downloading": MessageLookupByLibrary.simpleMessage("Descargando..."),
        "edit": MessageLookupByLibrary.simpleMessage("Düzenle"),
        "email": MessageLookupByLibrary.simpleMessage("Correo electrónico"),
        "emailMustBeValid": MessageLookupByLibrary.simpleMessage(
            "El correo electrónico debe ser válido"),
        "emailNotValid": MessageLookupByLibrary.simpleMessage(
            "Correo electrónico no válido"),
        "enterNameAndAddOptionalProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Adınızı girin ve isteğe bağlı bir profil resmi ekleyin"),
        "error": MessageLookupByLibrary.simpleMessage("Error"),
        "exitGroup": MessageLookupByLibrary.simpleMessage("Grubu Terk Et"),
        "explainWhatHappens": MessageLookupByLibrary.simpleMessage(
            "Burada ne olduğunu açıklayın"),
        "feedBackEmail":
            MessageLookupByLibrary.simpleMessage("Geribildirim E-postası"),
        "fileHasBeenSavedTo": MessageLookupByLibrary.simpleMessage(
            "El archivo se ha guardado en"),
        "fileMessages": MessageLookupByLibrary.simpleMessage("Dosya Mesajları"),
        "files": MessageLookupByLibrary.simpleMessage("Archivos"),
        "finished": MessageLookupByLibrary.simpleMessage("Finalizada"),
        "forRequest": MessageLookupByLibrary.simpleMessage("İstek için"),
        "forgetPassword":
            MessageLookupByLibrary.simpleMessage("Olvidé la contraseña"),
        "forgetPasswordExpireTime":
            MessageLookupByLibrary.simpleMessage("Parola Sıfırlama Süresi"),
        "forward": MessageLookupByLibrary.simpleMessage("Reenviar"),
        "fullName": MessageLookupByLibrary.simpleMessage("Tam Ad"),
        "gallery": MessageLookupByLibrary.simpleMessage("Galeri"),
        "globalSearch": MessageLookupByLibrary.simpleMessage("Búsqueda global"),
        "googlePlayAppUrl":
            MessageLookupByLibrary.simpleMessage("Google Play Store URL\'si"),
        "group": MessageLookupByLibrary.simpleMessage("Grup"),
        "groupCreatedBy":
            MessageLookupByLibrary.simpleMessage("Grupo creado por"),
        "groupDescription":
            MessageLookupByLibrary.simpleMessage("Grup Açıklaması"),
        "groupIcon": MessageLookupByLibrary.simpleMessage("Grup İkonu"),
        "groupInfo": MessageLookupByLibrary.simpleMessage("Grup Bilgisi"),
        "groupMembers":
            MessageLookupByLibrary.simpleMessage("Miembros del grupo"),
        "groupName": MessageLookupByLibrary.simpleMessage("Nombre del grupo"),
        "groupParticipants":
            MessageLookupByLibrary.simpleMessage("Participantes del grupo"),
        "groupSettings":
            MessageLookupByLibrary.simpleMessage("Configuración del grupo"),
        "groupWith": MessageLookupByLibrary.simpleMessage("Şununla Grup"),
        "harassmentOrBullyingDescription": MessageLookupByLibrary.simpleMessage(
            "Taciz veya Zorbalık: Bu seçenek, kullanıcıların kendilerine veya başkalarına taciz eden mesajlar, tehditler veya diğer zorbalık biçimleri gönderen kişileri bildirmelerine olanak tanır."),
        "help": MessageLookupByLibrary.simpleMessage("Yardım"),
        "hiIamUse":
            MessageLookupByLibrary.simpleMessage("Merhaba, ben kullanıcıyım"),
        "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Bu seçenek devre dışı bırakıldığında sohbet yayını oluşturma engellenecektir"),
        "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Bu seçenek devre dışı bırakıldığında sohbet grupları oluşturma engellenecektir"),
        "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Bu seçenek devre dışı bırakıldığında masaüstü (Windows ve macOS) giriş veya kayıt engellenecektir"),
        "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly":
            MessageLookupByLibrary.simpleMessage(
                "Bu seçenek etkinleştirildiğinde Google Ads bannerları sohbetlerde görüntülenir"),
        "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Bu seçenek devre dışı bırakıldığında sohbet dosyalarının, resimlerin, videoların ve konum bilgilerinin gönderimi engellenecektir"),
        "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Bu seçenek devre dışı bırakıldığında web girişi veya kayıt engellenecektir"),
        "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed":
            MessageLookupByLibrary.simpleMessage(
                "Bu seçenek etkinleştirildiğinde video ve sesli aramalara izin verilir"),
        "image": MessageLookupByLibrary.simpleMessage("Resim"),
        "imageMessages":
            MessageLookupByLibrary.simpleMessage("Resim Mesajları"),
        "images": MessageLookupByLibrary.simpleMessage("Resimler"),
        "inAppAlerts":
            MessageLookupByLibrary.simpleMessage("Uygulama İçi Bildirimler"),
        "inCall": MessageLookupByLibrary.simpleMessage("En llamada"),
        "inappropriateContentDescription": MessageLookupByLibrary.simpleMessage(
            "Uygunsuz İçerik: Kullanıcılar, cinsel içerik, nefret söylemi veya topluluk standartlarını ihlal eden diğer içerikleri bildirmek için bu seçeneği seçebilirler."),
        "info": MessageLookupByLibrary.simpleMessage("Información"),
        "infoMessages": MessageLookupByLibrary.simpleMessage("Bilgi Mesajları"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("Código no válido"),
        "invalidLoginData": MessageLookupByLibrary.simpleMessage(
            "Datos de inicio de sesión no válidos"),
        "ios": MessageLookupByLibrary.simpleMessage("iOS"),
        "joinedAt": MessageLookupByLibrary.simpleMessage("Katılım Tarihi"),
        "joinedBy": MessageLookupByLibrary.simpleMessage("Unido por"),
        "kickMember": MessageLookupByLibrary.simpleMessage("Expulsar miembro"),
        "kickedBy": MessageLookupByLibrary.simpleMessage("Expulsado por"),
        "language": MessageLookupByLibrary.simpleMessage("Dil"),
        "lastActiveFrom":
            MessageLookupByLibrary.simpleMessage("Son Etkinlik Tarihi:"),
        "leaveGroup": MessageLookupByLibrary.simpleMessage("Salir del grupo"),
        "leaveGroupAndDeleteYourMessageCopy":
            MessageLookupByLibrary.simpleMessage(
                "Salir del grupo y eliminar tu copia del mensaje"),
        "leftTheGroup":
            MessageLookupByLibrary.simpleMessage("Abandonó el grupo"),
        "linkADeviceSoon":
            MessageLookupByLibrary.simpleMessage("Cihazı Bağla (Yakında)"),
        "linkByQrCode":
            MessageLookupByLibrary.simpleMessage("QR Kodu ile Bağlantı"),
        "linkedDevices": MessageLookupByLibrary.simpleMessage("Bağlı Cihazlar"),
        "links": MessageLookupByLibrary.simpleMessage("Bağlantılar"),
        "loading": MessageLookupByLibrary.simpleMessage("Cargando..."),
        "location": MessageLookupByLibrary.simpleMessage("Ubicación"),
        "locationMessages":
            MessageLookupByLibrary.simpleMessage("Konum Mesajları"),
        "logOut": MessageLookupByLibrary.simpleMessage("Cerrar sesión"),
        "login": MessageLookupByLibrary.simpleMessage("Iniciar sesión"),
        "loginAgain":
            MessageLookupByLibrary.simpleMessage("Yeniden giriş yap!"),
        "loginNowAllowedNowPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Şu anda girişe izin verilmiyor. Lütfen daha sonra tekrar deneyin."),
        "logoutFromAllDevices": MessageLookupByLibrary.simpleMessage(
            "Tüm cihazlardan çıkış yapılsın mı?"),
        "macOs": MessageLookupByLibrary.simpleMessage("macOS"),
        "makeCall": MessageLookupByLibrary.simpleMessage("Realizar llamada"),
        "media": MessageLookupByLibrary.simpleMessage("Medya"),
        "mediaLinksAndDocs": MessageLookupByLibrary.simpleMessage(
            "Medya, Bağlantılar ve Belgeler"),
        "member": MessageLookupByLibrary.simpleMessage("Üye"),
        "members": MessageLookupByLibrary.simpleMessage("Üyeler"),
        "messageCounter": MessageLookupByLibrary.simpleMessage("Mesaj Sayacı"),
        "messageHasBeenDeleted":
            MessageLookupByLibrary.simpleMessage("Mensaje eliminado"),
        "messageHasBeenViewed":
            MessageLookupByLibrary.simpleMessage("Mesaj görüldü"),
        "messageInfo": MessageLookupByLibrary.simpleMessage("Mesaj Bilgisi"),
        "messages": MessageLookupByLibrary.simpleMessage("Mesajlar"),
        "microphoneAndCameraPermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone and camera permission must be accepted"),
        "microphonePermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone permission must be accepted"),
        "minutes": MessageLookupByLibrary.simpleMessage("Minutos"),
        "more": MessageLookupByLibrary.simpleMessage("Daha Fazla"),
        "mute": MessageLookupByLibrary.simpleMessage("Silenciar"),
        "muteNotifications":
            MessageLookupByLibrary.simpleMessage("Silenciar notificaciones"),
        "myPrivacy": MessageLookupByLibrary.simpleMessage("Gizliliğim"),
        "name": MessageLookupByLibrary.simpleMessage("Nombre"),
        "nameMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "El nombre debe tener un valor"),
        "needNewAccount": MessageLookupByLibrary.simpleMessage(
            "¿Necesitas una cuenta nueva?"),
        "newBroadcast": MessageLookupByLibrary.simpleMessage("Nueva difusión"),
        "newGroup": MessageLookupByLibrary.simpleMessage("Nuevo grupo"),
        "newPassword": MessageLookupByLibrary.simpleMessage("Yeni Şifre"),
        "newPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "La nueva contraseña debe tener un valor"),
        "newUpdateIsAvailable": MessageLookupByLibrary.simpleMessage(
            "Nueva actualización disponible"),
        "next": MessageLookupByLibrary.simpleMessage("İleri"),
        "nickname": MessageLookupByLibrary.simpleMessage("Takma Ad"),
        "no": MessageLookupByLibrary.simpleMessage("Hayır"),
        "noBio": MessageLookupByLibrary.simpleMessage("Hakkımda Yok"),
        "noUpdatesAvailableNow":
            MessageLookupByLibrary.simpleMessage("Şu anda güncelleme yok"),
        "none": MessageLookupByLibrary.simpleMessage("Hiçbiri"),
        "notAccepted": MessageLookupByLibrary.simpleMessage("Kabul edilmedi"),
        "notification": MessageLookupByLibrary.simpleMessage("Bildirim"),
        "notificationDescription":
            MessageLookupByLibrary.simpleMessage("Bildirim Açıklaması"),
        "notificationTitle":
            MessageLookupByLibrary.simpleMessage("Bildirim Başlığı"),
        "notificationsPage":
            MessageLookupByLibrary.simpleMessage("Bildirimler Sayfası"),
        "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion":
            MessageLookupByLibrary.simpleMessage(
                "Şu anda yalnızca okuma yetkili bir yönetici olarak giriş yaptınız. Bu bir test sürümü olduğundan yapılan tüm düzenlemeler uygulanmayacaktır."),
        "off": MessageLookupByLibrary.simpleMessage("Kapalı"),
        "offline": MessageLookupByLibrary.simpleMessage("Çevrimdışı"),
        "ok": MessageLookupByLibrary.simpleMessage("Aceptar"),
        "oldPassword": MessageLookupByLibrary.simpleMessage("Eski Şifre"),
        "on": MessageLookupByLibrary.simpleMessage("Açık"),
        "oneSeenMessage":
            MessageLookupByLibrary.simpleMessage("Bir kez görülen mesaj"),
        "online": MessageLookupByLibrary.simpleMessage("En línea"),
        "orLoginWith":
            MessageLookupByLibrary.simpleMessage("veya ile giriş yap"),
        "other": MessageLookupByLibrary.simpleMessage("Diğer"),
        "otherCategoryDescription": MessageLookupByLibrary.simpleMessage(
            "Diğer: Bu genel kategori, yukarıdaki kategorilere kolayca uymayan ihlaller için kullanılabilir. Kullanıcıların ek bilgiler sağlamak için metin kutusu eklemeleri faydalı olabilir."),
        "otpCode": MessageLookupByLibrary.simpleMessage("OTP Kodu"),
        "password": MessageLookupByLibrary.simpleMessage("Contraseña"),
        "passwordHasBeenChanged":
            MessageLookupByLibrary.simpleMessage("Şifre değiştirildi"),
        "passwordIsRequired":
            MessageLookupByLibrary.simpleMessage("Şifre Gereklidir"),
        "passwordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "La contraseña debe tener un valor"),
        "passwordNotMatch": MessageLookupByLibrary.simpleMessage(
            "Las contraseñas no coinciden"),
        "peerUserDeviceOffline": MessageLookupByLibrary.simpleMessage(
            "Dispositivo del usuario de destino fuera de línea"),
        "peerUserInCallNow":
            MessageLookupByLibrary.simpleMessage("Usuario en llamada ahora"),
        "pending": MessageLookupByLibrary.simpleMessage("Bekliyor"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefon"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Gizlilik Politikası"),
        "privacyUrl":
            MessageLookupByLibrary.simpleMessage("Gizlilik Politikası URL\'si"),
        "profile": MessageLookupByLibrary.simpleMessage("Profil"),
        "promotedToAdminBy": MessageLookupByLibrary.simpleMessage(
            "Promovido a administrador por"),
        "public": MessageLookupByLibrary.simpleMessage("Genel"),
        "read": MessageLookupByLibrary.simpleMessage("Okundu"),
        "recentUpdate": MessageLookupByLibrary.simpleMessage("Son güncelleme"),
        "recentUpdates":
            MessageLookupByLibrary.simpleMessage("Actualizaciones recientes"),
        "recording": MessageLookupByLibrary.simpleMessage("Grabando..."),
        "register": MessageLookupByLibrary.simpleMessage("Registrarse"),
        "registerMethod": MessageLookupByLibrary.simpleMessage("Kayıt Yöntemi"),
        "registerStatus": MessageLookupByLibrary.simpleMessage("Kayıt Durumu"),
        "rejected": MessageLookupByLibrary.simpleMessage("Rechazada"),
        "repliedToYourSelf":
            MessageLookupByLibrary.simpleMessage("Respondiste a ti mismo"),
        "reply": MessageLookupByLibrary.simpleMessage("Responder"),
        "replyToYourSelf":
            MessageLookupByLibrary.simpleMessage("Responder a ti mismo"),
        "report": MessageLookupByLibrary.simpleMessage("Reportar"),
        "reportHasBeenSubmitted":
            MessageLookupByLibrary.simpleMessage("Raporunuz gönderildi"),
        "reportUser": MessageLookupByLibrary.simpleMessage("Reportar usuario"),
        "reports": MessageLookupByLibrary.simpleMessage("Raporlar"),
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("Şifre Sıfırlama"),
        "retry": MessageLookupByLibrary.simpleMessage("Tekrar Dene"),
        "ring": MessageLookupByLibrary.simpleMessage("Timbre"),
        "roomAlreadyInCall":
            MessageLookupByLibrary.simpleMessage("Sala en llamada"),
        "roomCounter": MessageLookupByLibrary.simpleMessage("Oda Sayacı"),
        "saveLogin": MessageLookupByLibrary.simpleMessage("Girişi Kaydet"),
        "search": MessageLookupByLibrary.simpleMessage("Ara"),
        "seconds": MessageLookupByLibrary.simpleMessage("saniye"),
        "send": MessageLookupByLibrary.simpleMessage("Gönder"),
        "sendCodeToMyEmail": MessageLookupByLibrary.simpleMessage(
            "Enviar código a mi correo electrónico"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Enviar mensaje"),
        "sessionEnd": MessageLookupByLibrary.simpleMessage("Fin de la sesión"),
        "setMaxBroadcastMembers": MessageLookupByLibrary.simpleMessage(
            "Maksimum Yayın Üye Sayısını Belirle"),
        "setMaxGroupMembers": MessageLookupByLibrary.simpleMessage(
            "Maksimum Grup Üye Sayısını Belirle"),
        "setMaxMessageForwardAndShare": MessageLookupByLibrary.simpleMessage(
            "Maksimum Mesaj İleriye ve Paylaşıma Ayarla"),
        "setNewPrivacyPolicyUrl": MessageLookupByLibrary.simpleMessage(
            "Yeni Gizlilik Politikası URL\'sini Belirle"),
        "setToAdmin": MessageLookupByLibrary.simpleMessage(
            "Establecer como administrador"),
        "settings": MessageLookupByLibrary.simpleMessage("Ajustes"),
        "share": MessageLookupByLibrary.simpleMessage("Compartir"),
        "shareMediaAndLocation": MessageLookupByLibrary.simpleMessage(
            "Compartir multimedia y ubicación"),
        "shareYourStatus":
            MessageLookupByLibrary.simpleMessage("Durumunuzu paylaşın"),
        "showHistory": MessageLookupByLibrary.simpleMessage("Geçmişi Göster"),
        "showMedia": MessageLookupByLibrary.simpleMessage("Mostrar medios"),
        "soon": MessageLookupByLibrary.simpleMessage("Yakında"),
        "spamOrScamDescription": MessageLookupByLibrary.simpleMessage(
            "Spam veya Dolandırıcılık: Bu seçenek, kullanıcıların spam mesajları, istenmeyen reklamları gönderen hesapları veya diğerleri tarafından dolandırılmaya çalışılan hesapları bildirmeleri için kullanılabilir."),
        "star": MessageLookupByLibrary.simpleMessage("Destacar"),
        "starMessage": MessageLookupByLibrary.simpleMessage("Destacar mensaje"),
        "starredMessage":
            MessageLookupByLibrary.simpleMessage("Mensaje destacado"),
        "starredMessages":
            MessageLookupByLibrary.simpleMessage("Yıldızlı Mesajlar"),
        "startChat": MessageLookupByLibrary.simpleMessage("Iniciar chat"),
        "startNewChatWithYou": MessageLookupByLibrary.simpleMessage(
            "Sizle yeni bir sohbet başlatın"),
        "status": MessageLookupByLibrary.simpleMessage("Durum"),
        "storageAndData":
            MessageLookupByLibrary.simpleMessage("Depolama ve Veri"),
        "stories": MessageLookupByLibrary.simpleMessage("Hikayeler"),
        "storyCreatedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Hikaye başarıyla oluşturuldu"),
        "success": MessageLookupByLibrary.simpleMessage("Başarılı"),
        "successfullyDownloadedIn":
            MessageLookupByLibrary.simpleMessage("Başarıyla indirildi:"),
        "supportChatSoon":
            MessageLookupByLibrary.simpleMessage("Destek Sohbeti (Yakında)"),
        "tapADeviceToEditOrLogOut": MessageLookupByLibrary.simpleMessage(
            "Düzenlemek veya oturumu kapatmak için bir cihaza dokunun"),
        "tapForPhoto":
            MessageLookupByLibrary.simpleMessage("Toca para la foto"),
        "tapToSelectAnIcon":
            MessageLookupByLibrary.simpleMessage("İkon seçmek için dokunun"),
        "tellAFriend":
            MessageLookupByLibrary.simpleMessage("Bir Arkadaşa Söyle"),
        "textFieldHint":
            MessageLookupByLibrary.simpleMessage("Escribe un mensaje..."),
        "textMessages": MessageLookupByLibrary.simpleMessage("Metin Mesajları"),
        "thereIsFileHasSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "Hay un archivo cuyo tamaño es mayor al permitido"),
        "thereIsVideoSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "Hay un video cuyo tamaño es mayor al permitido"),
        "timeout": MessageLookupByLibrary.simpleMessage("Tiempo agotado"),
        "titleIsRequired":
            MessageLookupByLibrary.simpleMessage("El título es obligatorio"),
        "today": MessageLookupByLibrary.simpleMessage("Hoy"),
        "total": MessageLookupByLibrary.simpleMessage("Toplam"),
        "totalMessages":
            MessageLookupByLibrary.simpleMessage("Toplam Mesajlar"),
        "totalRooms": MessageLookupByLibrary.simpleMessage("Toplam Odalar"),
        "totalVisits":
            MessageLookupByLibrary.simpleMessage("Toplam Ziyaretler"),
        "typing": MessageLookupByLibrary.simpleMessage("Escribiendo..."),
        "unBlock": MessageLookupByLibrary.simpleMessage("Engeli Kaldır"),
        "unBlockUser": MessageLookupByLibrary.simpleMessage(
            "Kullanıcının Engelini Kaldır"),
        "unMute": MessageLookupByLibrary.simpleMessage("Activar sonido"),
        "unStar": MessageLookupByLibrary.simpleMessage("Yıldızı Kaldır"),
        "update": MessageLookupByLibrary.simpleMessage("Güncelle"),
        "updateBroadcastTitle": MessageLookupByLibrary.simpleMessage(
            "Actualizar título de la difusión"),
        "updateFeedBackEmail": MessageLookupByLibrary.simpleMessage(
            "Geribildirim E-postasını Güncelle"),
        "updateGroupDescription": MessageLookupByLibrary.simpleMessage(
            "Actualizar descripción del grupo"),
        "updateGroupDescriptionWillUpdateAllGroupMembers":
            MessageLookupByLibrary.simpleMessage(
                "Grup açıklamasını güncellemek tüm grup üyelerini güncelleyecektir"),
        "updateGroupTitle":
            MessageLookupByLibrary.simpleMessage("Actualizar título del grupo"),
        "updateImage":
            MessageLookupByLibrary.simpleMessage("Actualizar imagen"),
        "updateNickname":
            MessageLookupByLibrary.simpleMessage("Takma Adı Güncelle"),
        "updateTitle": MessageLookupByLibrary.simpleMessage("Başlığı Güncelle"),
        "updateTitleTo":
            MessageLookupByLibrary.simpleMessage("Actualizar título a"),
        "updateYourBio":
            MessageLookupByLibrary.simpleMessage("Biyografinizi Güncelleyin"),
        "updateYourName":
            MessageLookupByLibrary.simpleMessage("Adınızı Güncelleyin"),
        "updateYourPassword":
            MessageLookupByLibrary.simpleMessage("Şifrenizi Güncelleyin"),
        "updateYourProfile":
            MessageLookupByLibrary.simpleMessage("Profilinizi güncelleyin"),
        "updatedAt": MessageLookupByLibrary.simpleMessage("Güncelleme Tarihi"),
        "upgradeToAdmin":
            MessageLookupByLibrary.simpleMessage("Yöneticiye Yükselt"),
        "userAction": MessageLookupByLibrary.simpleMessage("Kullanıcı Eylemi"),
        "userAlreadyRegister":
            MessageLookupByLibrary.simpleMessage("Usuario ya registrado"),
        "userDeviceSessionEndDeviceDeleted": MessageLookupByLibrary.simpleMessage(
            "La sesión del dispositivo del usuario ha finalizado y el dispositivo se ha eliminado"),
        "userEmailNotFound": MessageLookupByLibrary.simpleMessage(
            "Correo electrónico de usuario no encontrado"),
        "userInfo": MessageLookupByLibrary.simpleMessage("Kullanıcı Bilgisi"),
        "userPage": MessageLookupByLibrary.simpleMessage("Página del usuario"),
        "userProfile":
            MessageLookupByLibrary.simpleMessage("Kullanıcı Profili"),
        "userRegisterStatusNotAcceptedYet":
            MessageLookupByLibrary.simpleMessage(
                "El estado de registro del usuario aún no ha sido aceptado"),
        "users": MessageLookupByLibrary.simpleMessage("Kullanıcılar"),
        "usersAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Usuarios agregados exitosamente"),
        "vMessageInfoTrans":
            MessageLookupByLibrary.simpleMessage("Información del mensaje"),
        "vMessagesInfoTrans":
            MessageLookupByLibrary.simpleMessage("Información de mensajes"),
        "verified": MessageLookupByLibrary.simpleMessage("Doğrulandı"),
        "verifiedAt": MessageLookupByLibrary.simpleMessage("Doğrulandı"),
        "video": MessageLookupByLibrary.simpleMessage("Video"),
        "videoCallMessages":
            MessageLookupByLibrary.simpleMessage("Video Çağrı Mesajları"),
        "videoMessages":
            MessageLookupByLibrary.simpleMessage("Video Mesajları"),
        "visits": MessageLookupByLibrary.simpleMessage("Ziyaretler"),
        "voiceCallMessage":
            MessageLookupByLibrary.simpleMessage("Sesli Arama Mesajı"),
        "voiceCallMessages":
            MessageLookupByLibrary.simpleMessage("Sesli Çağrı Mesajları"),
        "voiceMessages": MessageLookupByLibrary.simpleMessage("Sesli Mesajlar"),
        "wait2MinutesToSendMail": MessageLookupByLibrary.simpleMessage(
            "Espera 2 minutos para enviar el correo"),
        "waitingList": MessageLookupByLibrary.simpleMessage("Bekleme Listesi"),
        "web": MessageLookupByLibrary.simpleMessage("Web"),
        "welcome": MessageLookupByLibrary.simpleMessage("Hoş geldiniz"),
        "whenUsingMobileData":
            MessageLookupByLibrary.simpleMessage("Mobil veri kullanırken"),
        "whenUsingWifi":
            MessageLookupByLibrary.simpleMessage("Wi-Fi kullanırken"),
        "windows": MessageLookupByLibrary.simpleMessage("Windows"),
        "writeACaption":
            MessageLookupByLibrary.simpleMessage("Altyazı yazın..."),
        "yes": MessageLookupByLibrary.simpleMessage("Evet"),
        "yesterday": MessageLookupByLibrary.simpleMessage("Ayer"),
        "you": MessageLookupByLibrary.simpleMessage("Tú"),
        "youAreAboutToDeleteThisUserFromYourList":
            MessageLookupByLibrary.simpleMessage(
                "Estás a punto de eliminar a este usuario de tu lista"),
        "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList":
            MessageLookupByLibrary.simpleMessage(
                "Hesabınızı silmek üzeresiniz. Hesabınız kullanıcı listesinde artık görünmeyecektir"),
        "youAreAboutToDismissesToMember": MessageLookupByLibrary.simpleMessage(
            "Estás a punto de relegar a miembro"),
        "youAreAboutToKick":
            MessageLookupByLibrary.simpleMessage("Estás a punto de expulsar"),
        "youAreAboutToUpgradeToAdmin": MessageLookupByLibrary.simpleMessage(
            "Estás a punto de ascender a administrador"),
        "youDontHaveAccess":
            MessageLookupByLibrary.simpleMessage("No tienes acceso"),
        "youInPublicSearch":
            MessageLookupByLibrary.simpleMessage("Genel aramada siz"),
        "youNotParticipantInThisGroup":
            MessageLookupByLibrary.simpleMessage("Bu grupta üye değilsiniz"),
        "yourAccountBlocked":
            MessageLookupByLibrary.simpleMessage("Tu cuenta ha sido bloqueada"),
        "yourAccountDeleted":
            MessageLookupByLibrary.simpleMessage("Tu cuenta ha sido eliminada"),
        "yourAccountIsUnderReview":
            MessageLookupByLibrary.simpleMessage("Hesabınız inceleniyor"),
        "yourAreAboutToLogoutFromThisAccount":
            MessageLookupByLibrary.simpleMessage(
                "Bu hesaptan çıkıyorsunuz, emin misiniz?"),
        "yourLastSeen": MessageLookupByLibrary.simpleMessage("Son görülme"),
        "yourLastSeenInChats":
            MessageLookupByLibrary.simpleMessage("Sohbetlerde son görülme"),
        "yourProfileAppearsInPublicSearchAndAddingForGroups":
            MessageLookupByLibrary.simpleMessage(
                "Profiliniz genel aramada ve gruplara ekleme için görünür"),
        "yourSessionIsEndedPleaseLoginAgain":
            MessageLookupByLibrary.simpleMessage(
                "Oturumunuz sona erdi, lütfen yeniden giriş yapın"),
        "yourStory": MessageLookupByLibrary.simpleMessage("Hikayeniz")
      };
}
