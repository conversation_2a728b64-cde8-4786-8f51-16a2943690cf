// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Dashboard": MessageLookupByLibrary.simpleMessage("لوحة القيادة"),
        "about": MessageLookupByLibrary.simpleMessage("حول"),
        "aboutToBlockUserWithConsequences": MessageLookupByLibrary.simpleMessage(
            "أنت على وشك حظر هذا المستخدم. لن تتمكن من إرسال رسائل له ولا إضافته إلى المجموعات أو البث!"),
        "accepted": MessageLookupByLibrary.simpleMessage("تم قبوله"),
        "account": MessageLookupByLibrary.simpleMessage("الحساب"),
        "actions": MessageLookupByLibrary.simpleMessage("إجراءات"),
        "addMembers": MessageLookupByLibrary.simpleMessage("إضافة أعضاء"),
        "addNewStory": MessageLookupByLibrary.simpleMessage("إضافة قصة جديدة"),
        "addParticipants":
            MessageLookupByLibrary.simpleMessage("إضافة مشاركين"),
        "addedYouToNewBroadcast":
            MessageLookupByLibrary.simpleMessage("تمت إضافتك إلى بث جديد"),
        "admin": MessageLookupByLibrary.simpleMessage("مسوال"),
        "adminNotification":
            MessageLookupByLibrary.simpleMessage("إشعار المسؤول"),
        "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself":
            MessageLookupByLibrary.simpleMessage(
                "تمت محفظة جميع البيانات، لا تحتاج إلى إدارة حفظ البيانات بنفسك! إذا قمت بتسجيل الخروج وتسجيل الدخول مرة أخرى، ستظهر جميع الدردشات كما هي في نسخة الويب"),
        "allDeletedMessages":
            MessageLookupByLibrary.simpleMessage("جميع الرسائل المحذوفة"),
        "allowAds": MessageLookupByLibrary.simpleMessage("السماح بالإعلانات"),
        "allowCalls": MessageLookupByLibrary.simpleMessage("السماح بالمكالمات"),
        "allowCreateBroadcast":
            MessageLookupByLibrary.simpleMessage("السماح بإنشاء البث"),
        "allowCreateGroups":
            MessageLookupByLibrary.simpleMessage("السماح بإنشاء المجموعات"),
        "allowDesktopLogin": MessageLookupByLibrary.simpleMessage(
            "السماح بتسجيل الدخول عبر سطح المكتب"),
        "allowMobileLogin": MessageLookupByLibrary.simpleMessage(
            "السماح بتسجيل الدخول عبر الهاتف المحمول"),
        "allowSendMedia":
            MessageLookupByLibrary.simpleMessage("السماح بإرسال الوسائط"),
        "allowWebLogin": MessageLookupByLibrary.simpleMessage(
            "السماح بتسجيل الدخول عبر الويب"),
        "alreadyHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("لديك بالفعل حساب؟"),
        "android": MessageLookupByLibrary.simpleMessage("أندرويد"),
        "appMembers": MessageLookupByLibrary.simpleMessage("أعضاء التطبيق"),
        "appleStoreAppUrl":
            MessageLookupByLibrary.simpleMessage("رابط تطبيق متجر آبل"),
        "areYouSure": MessageLookupByLibrary.simpleMessage("هل أنت متأكد؟"),
        "areYouSureToBlock":
            MessageLookupByLibrary.simpleMessage("هل أنت متأكد من حظر"),
        "areYouSureToLeaveThisGroupThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "هل أنت متأكد من مغادرة هذه المجموعة؟ لا يمكن التراجع عن هذا الإجراء"),
        "areYouSureToPermitYourCopyThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "هل أنت متأكد من السماح بنسختك؟ لا يمكن التراجع عن هذا الإجراء"),
        "areYouSureToReportUserToAdmin": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من رغبتك في تقديم تقرير عن هذا المستخدم إلى المسؤول؟"),
        "areYouSureToUnBlock": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من رغبتك في إلغاء حظر المستخدم؟"),
        "areYouWantToMakeVideoCall": MessageLookupByLibrary.simpleMessage(
            "هل ترغب في إجراء مكالمة فيديو؟"),
        "areYouWantToMakeVoiceCall": MessageLookupByLibrary.simpleMessage(
            "هل ترغب في إجراء مكالمة صوتية؟"),
        "audio": MessageLookupByLibrary.simpleMessage("صوت"),
        "audioCall": MessageLookupByLibrary.simpleMessage("مكالمة صوتية"),
        "back": MessageLookupByLibrary.simpleMessage("العودة"),
        "banAt": MessageLookupByLibrary.simpleMessage("تم الحظر في"),
        "banTo": MessageLookupByLibrary.simpleMessage("الحظر حتى"),
        "bio": MessageLookupByLibrary.simpleMessage("السيرة الذاتية"),
        "block": MessageLookupByLibrary.simpleMessage("حظر"),
        "blockUser": MessageLookupByLibrary.simpleMessage("حظر المستخدم"),
        "blocked": MessageLookupByLibrary.simpleMessage("تم حظره"),
        "blockedUsers":
            MessageLookupByLibrary.simpleMessage("المستخدمين المحظورين"),
        "broadcast": MessageLookupByLibrary.simpleMessage("بث"),
        "broadcastInfo": MessageLookupByLibrary.simpleMessage("معلومات البث"),
        "broadcastMembers": MessageLookupByLibrary.simpleMessage("أعضاء البث"),
        "broadcastName": MessageLookupByLibrary.simpleMessage("اسم البث"),
        "broadcastParticipants":
            MessageLookupByLibrary.simpleMessage("مشاركو البث"),
        "broadcastSettings":
            MessageLookupByLibrary.simpleMessage("إعدادات البث"),
        "callNotAllowed":
            MessageLookupByLibrary.simpleMessage("غير مسموح بالمكالمة"),
        "callTimeoutInSeconds":
            MessageLookupByLibrary.simpleMessage("مهلة المكالمة في ثوانٍ"),
        "calls": MessageLookupByLibrary.simpleMessage("المكالمات"),
        "camera": MessageLookupByLibrary.simpleMessage("كاميرا"),
        "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
        "canceled": MessageLookupByLibrary.simpleMessage("تم الإلغاء"),
        "changeSubject": MessageLookupByLibrary.simpleMessage("تغيير الموضوع"),
        "chat": MessageLookupByLibrary.simpleMessage("الدردشة"),
        "chats": MessageLookupByLibrary.simpleMessage("الدردشات"),
        "checkForUpdates":
            MessageLookupByLibrary.simpleMessage("التحقق من التحديثات"),
        "chooseHowAutomaticDownloadWorks": MessageLookupByLibrary.simpleMessage(
            "اختر كيفية عمل التنزيل التلقائي"),
        "chooseRoom": MessageLookupByLibrary.simpleMessage("اختار المحادثات"),
        "clear": MessageLookupByLibrary.simpleMessage("مسح"),
        "clearCallsConfirm":
            MessageLookupByLibrary.simpleMessage("تأكيد مسح المكالمات"),
        "clearChat": MessageLookupByLibrary.simpleMessage("مسح الدردشة"),
        "clickToAddGroupDescription":
            MessageLookupByLibrary.simpleMessage("انقر لإضافة وصف المجموعة"),
        "clickToSee": MessageLookupByLibrary.simpleMessage("انقر لرؤية"),
        "clickToSeeAllUserCountries":
            MessageLookupByLibrary.simpleMessage("انقر لعرض جميع دول المستخدم"),
        "clickToSeeAllUserDevicesDetails": MessageLookupByLibrary.simpleMessage(
            "انقر لعرض تفاصيل جميع أجهزة المستخدم"),
        "clickToSeeAllUserInformations": MessageLookupByLibrary.simpleMessage(
            "انقر لعرض جميع معلومات المستخدم"),
        "clickToSeeAllUserMessagesDetails":
            MessageLookupByLibrary.simpleMessage(
                "انقر لعرض تفاصيل جميع رسائل المستخدم"),
        "clickToSeeAllUserReports": MessageLookupByLibrary.simpleMessage(
            "انقر لعرض جميع التقارير المستخدم"),
        "clickToSeeAllUserRoomsDetails": MessageLookupByLibrary.simpleMessage(
            "انقر لعرض تفاصيل جميع غرف المستخدم"),
        "close": MessageLookupByLibrary.simpleMessage("إغلاق"),
        "codeHasBeenExpired":
            MessageLookupByLibrary.simpleMessage("انتهى صلاحية الرمز"),
        "codeMustEqualToSixNumbers": MessageLookupByLibrary.simpleMessage(
            "يجب أن يكون الرمز مكونًا من ستة أرقام"),
        "configureYourAccountPrivacy":
            MessageLookupByLibrary.simpleMessage("تكوين خصوصية حسابك"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("تأكيد كلمة المرور"),
        "confirmPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "يجب أن تحتوي تأكيد كلمة المرور على قيمة"),
        "connecting": MessageLookupByLibrary.simpleMessage("جاري الاتصال..."),
        "contactInfo": MessageLookupByLibrary.simpleMessage("معلومات الاتصال"),
        "contactUs": MessageLookupByLibrary.simpleMessage("اتصل بنا"),
        "copy": MessageLookupByLibrary.simpleMessage("نسخ"),
        "countries": MessageLookupByLibrary.simpleMessage("الدول"),
        "country": MessageLookupByLibrary.simpleMessage("البلد"),
        "create": MessageLookupByLibrary.simpleMessage("إنشاء"),
        "createBroadcast": MessageLookupByLibrary.simpleMessage("إنشاء بث"),
        "createGroup": MessageLookupByLibrary.simpleMessage("إنشاء مجموعة"),
        "createMediaStory":
            MessageLookupByLibrary.simpleMessage("إنشاء قصة وسائط"),
        "createStory": MessageLookupByLibrary.simpleMessage("إنشاء قصة"),
        "createTextStory":
            MessageLookupByLibrary.simpleMessage("إنشاء قصة نصية"),
        "createYourStory": MessageLookupByLibrary.simpleMessage("إنشاء قصتك"),
        "createdAt": MessageLookupByLibrary.simpleMessage("تم الإنشاء في"),
        "creator": MessageLookupByLibrary.simpleMessage("المنشاء"),
        "currentDevice": MessageLookupByLibrary.simpleMessage("الجهاز الحالي"),
        "dashboard": MessageLookupByLibrary.simpleMessage("لوحة القيادة"),
        "dataPrivacy": MessageLookupByLibrary.simpleMessage("خصوصية البيانات"),
        "delete": MessageLookupByLibrary.simpleMessage("حذف"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("حذف الدردشة"),
        "deleteFromAll": MessageLookupByLibrary.simpleMessage("حذف من الجميع"),
        "deleteFromMe": MessageLookupByLibrary.simpleMessage("حذف مني"),
        "deleteMember": MessageLookupByLibrary.simpleMessage("حذف العضو"),
        "deleteMyAccount": MessageLookupByLibrary.simpleMessage("حذف حسابي"),
        "deleteThisDeviceDesc": MessageLookupByLibrary.simpleMessage(
            "مسح الجهاز سوف يخرج اليوزر من التطبيق حالا"),
        "deleteUser": MessageLookupByLibrary.simpleMessage("حذف المستخدم"),
        "deleteYouCopy": MessageLookupByLibrary.simpleMessage("حذف نسختك"),
        "deleted": MessageLookupByLibrary.simpleMessage("تم حذفه"),
        "deletedAt": MessageLookupByLibrary.simpleMessage("تم الحذف في"),
        "delivered": MessageLookupByLibrary.simpleMessage("تم التسليم"),
        "description": MessageLookupByLibrary.simpleMessage("الوصف"),
        "descriptionIsRequired":
            MessageLookupByLibrary.simpleMessage("الوصف مطلوب"),
        "desktopAndOtherDevices":
            MessageLookupByLibrary.simpleMessage("سطح المكتب والأجهزة الأخرى"),
        "deviceHasBeenLogoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage(
                "تم تسجيل الخروج من جهازك من جميع الأجهزة"),
        "deviceStatus": MessageLookupByLibrary.simpleMessage("حالة الجهاز"),
        "devices": MessageLookupByLibrary.simpleMessage("الأجهزة"),
        "directChat": MessageLookupByLibrary.simpleMessage("دردشة مباشرة"),
        "directRooms": MessageLookupByLibrary.simpleMessage("غرف مباشرة"),
        "dismissedToMemberBy":
            MessageLookupByLibrary.simpleMessage("تم تنزيله إلى عضو بواسطة"),
        "dismissesToMember":
            MessageLookupByLibrary.simpleMessage("تنزيل إلى عضو"),
        "docs": MessageLookupByLibrary.simpleMessage("وثائق"),
        "done": MessageLookupByLibrary.simpleMessage("تم"),
        "download": MessageLookupByLibrary.simpleMessage("تحميل"),
        "downloading": MessageLookupByLibrary.simpleMessage("جاري التحميل..."),
        "edit": MessageLookupByLibrary.simpleMessage("تعديل"),
        "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
        "emailMustBeValid": MessageLookupByLibrary.simpleMessage(
            "يجب أن يكون البريد الإلكتروني صالح"),
        "emailNotValid":
            MessageLookupByLibrary.simpleMessage("البريد الإلكتروني غير صالح"),
        "enterNameAndAddOptionalProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "أدخل اسمك وأضف صورة شخصية اختيارية"),
        "error": MessageLookupByLibrary.simpleMessage("خطأ"),
        "exitGroup": MessageLookupByLibrary.simpleMessage("الخروج من المجموعة"),
        "explainWhatHappens":
            MessageLookupByLibrary.simpleMessage("شرح ما يحدث هنا"),
        "feedBackEmail": MessageLookupByLibrary.simpleMessage("بريد الملاحظات"),
        "fileHasBeenSavedTo":
            MessageLookupByLibrary.simpleMessage("تم حفظ الملف في"),
        "fileMessages": MessageLookupByLibrary.simpleMessage("رسائل ملفات"),
        "files": MessageLookupByLibrary.simpleMessage("ملفات"),
        "finished": MessageLookupByLibrary.simpleMessage("انتهى"),
        "forRequest": MessageLookupByLibrary.simpleMessage("للطلب"),
        "forgetPassword":
            MessageLookupByLibrary.simpleMessage("نسيت كلمة المرور"),
        "forgetPasswordExpireTime": MessageLookupByLibrary.simpleMessage(
            "وقت انتهاء صلاحية نسيان كلمة المرور"),
        "forward": MessageLookupByLibrary.simpleMessage("إعادة توجيه"),
        "fullName": MessageLookupByLibrary.simpleMessage("الاسم الكامل"),
        "gallery": MessageLookupByLibrary.simpleMessage("معرض"),
        "globalSearch": MessageLookupByLibrary.simpleMessage("البحث العالمي"),
        "googlePlayAppUrl":
            MessageLookupByLibrary.simpleMessage("رابط تطبيق متجر جوجل بلاي"),
        "group": MessageLookupByLibrary.simpleMessage("مجموعة"),
        "groupCreatedBy":
            MessageLookupByLibrary.simpleMessage("تم إنشاء المجموعة بواسطة"),
        "groupDescription":
            MessageLookupByLibrary.simpleMessage("وصف المجموعة"),
        "groupIcon": MessageLookupByLibrary.simpleMessage("أيقونة المجموعة"),
        "groupInfo": MessageLookupByLibrary.simpleMessage("معلومات المجموعة"),
        "groupMembers": MessageLookupByLibrary.simpleMessage("أعضاء المجموعة"),
        "groupName": MessageLookupByLibrary.simpleMessage("اسم المجموعة"),
        "groupParticipants":
            MessageLookupByLibrary.simpleMessage("مشاركو المجموعة"),
        "groupSettings":
            MessageLookupByLibrary.simpleMessage("إعدادات المجموعة"),
        "groupWith": MessageLookupByLibrary.simpleMessage("المجموعة مع"),
        "harassmentOrBullyingDescription": MessageLookupByLibrary.simpleMessage(
            "التحرش أو الابتزاز: تتيح هذه الخيارات للمستخدمين الإبلاغ عن الأفراد الذين يستهدفونهم أو الآخرين برسائل مضايقة أو تهديدات أو أشكال أخرى من التنمر."),
        "help": MessageLookupByLibrary.simpleMessage("مساعدة"),
        "hiIamUse": MessageLookupByLibrary.simpleMessage("مرحبًا، أنا استخدم"),
        "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "إذا تم تعطيل هذا الخيار، سيتم حظر إنشاء البث"),
        "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "إذا تم تعطيل هذا الخيار، سيتم حظر إنشاء المجموعات"),
        "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "إذا تم تعطيل هذا الخيار، سيتم حظر تسجيل الدخول أو التسجيل عبر سطح المكتب (ويندوز وmacOS)"),
        "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly":
            MessageLookupByLibrary.simpleMessage(
                "إذا تم تمكين هذا الخيار، سيظهر إعلان Google Ads في الدردشات"),
        "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "إذا تم تعطيل هذا الخيار، سيتم حظر إرسال ملفات الدردشة والصور ومقاطع الفيديو والموقع"),
        "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "إذا تم تعطيل هذا الخيار، سيتم حظر تسجيل الدخول أو التسجيل عبر الويب"),
        "ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats":
            MessageLookupByLibrary.simpleMessage(
                "السماح بتسجيل الدخول عبر الجوال أو التسجيل (على Android وiOS فقط)"),
        "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed":
            MessageLookupByLibrary.simpleMessage(
                "إذا تم تمكين هذا الخيار، سيتم السماح بالمكالمات الصوتية والفيديو"),
        "image": MessageLookupByLibrary.simpleMessage("صورة"),
        "imageMessages": MessageLookupByLibrary.simpleMessage("رسائل صور"),
        "images": MessageLookupByLibrary.simpleMessage("صور"),
        "inAppAlerts":
            MessageLookupByLibrary.simpleMessage("تنبيهات في التطبيق"),
        "inCall": MessageLookupByLibrary.simpleMessage("قيد المكالمة"),
        "inappropriateContentDescription": MessageLookupByLibrary.simpleMessage(
            "المحتوى غير المناسب: يمكن للمستخدمين اختيار هذا الخيار للإبلاغ عن أي مواد جنسية صريحة أو خطب الكراهية أو أي محتوى ينتهك معايير المجتمع."),
        "info": MessageLookupByLibrary.simpleMessage("معلومات"),
        "infoMessages": MessageLookupByLibrary.simpleMessage("رسائل معلومات"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("رمز غير صالح"),
        "invalidLoginData": MessageLookupByLibrary.simpleMessage(
            "بيانات تسجيل الدخول غير صالحة"),
        "ios": MessageLookupByLibrary.simpleMessage("iOS"),
        "joinedAt": MessageLookupByLibrary.simpleMessage("انضم في"),
        "joinedBy": MessageLookupByLibrary.simpleMessage("انضم بواسطة"),
        "kickMember": MessageLookupByLibrary.simpleMessage("طرد العضو"),
        "kickedBy": MessageLookupByLibrary.simpleMessage("تم طرده بواسطة"),
        "language": MessageLookupByLibrary.simpleMessage("اللغة"),
        "lastActiveFrom": MessageLookupByLibrary.simpleMessage("آخر نشاط من"),
        "leaveGroup": MessageLookupByLibrary.simpleMessage("مغادرة المجموعة"),
        "leaveGroupAndDeleteYourMessageCopy":
            MessageLookupByLibrary.simpleMessage(
                "مغادرة المجموعة وحذف نسخة رسالتك"),
        "leftTheGroup": MessageLookupByLibrary.simpleMessage("غادر المجموعة"),
        "linkADeviceSoon":
            MessageLookupByLibrary.simpleMessage("ربط جهاز (قريبًا)"),
        "linkByQrCode": MessageLookupByLibrary.simpleMessage(
            "الربط عبر رمز الاستجابة السريعة"),
        "linkedDevices":
            MessageLookupByLibrary.simpleMessage("الأجهزة المرتبطة"),
        "links": MessageLookupByLibrary.simpleMessage("روابط"),
        "loading": MessageLookupByLibrary.simpleMessage("جاري التحميل..."),
        "location": MessageLookupByLibrary.simpleMessage("الموقع"),
        "locationMessages":
            MessageLookupByLibrary.simpleMessage("رسائل الموقع"),
        "logOut": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
        "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
        "loginAgain":
            MessageLookupByLibrary.simpleMessage("تسجيل الدخول مرة أخرى!"),
        "loginNowAllowedNowPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "نأسف، حالياً غير مسموح بتسجيل الدخول. الرجاء المحاولة مرة أخرى لاحقًا."),
        "logoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage("الخروج من جميع الأجهزة؟"),
        "macOs": MessageLookupByLibrary.simpleMessage("macOS"),
        "makeCall": MessageLookupByLibrary.simpleMessage("إجراء مكالمة"),
        "media": MessageLookupByLibrary.simpleMessage("وسائط"),
        "mediaLinksAndDocs":
            MessageLookupByLibrary.simpleMessage("وسائط، روابط ووثائق"),
        "member": MessageLookupByLibrary.simpleMessage("عضو"),
        "members": MessageLookupByLibrary.simpleMessage("الأعضاء"),
        "messageCounter": MessageLookupByLibrary.simpleMessage("عداد الرسائل"),
        "messageHasBeenDeleted":
            MessageLookupByLibrary.simpleMessage("تم حذف الرسالة"),
        "messageHasBeenViewed":
            MessageLookupByLibrary.simpleMessage("تمت مشاهدة الرسالة"),
        "messageInfo": MessageLookupByLibrary.simpleMessage("معلومات الرسالة"),
        "messages": MessageLookupByLibrary.simpleMessage("الرسائل"),
        "microphoneAndCameraPermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "يجب الموافقه علي ازن الميكروفون والكاميرا"),
        "microphonePermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "يجب الموافقه علي ازن الميكروفون"),
        "minutes": MessageLookupByLibrary.simpleMessage("دقائق"),
        "more": MessageLookupByLibrary.simpleMessage("المزيد"),
        "mute": MessageLookupByLibrary.simpleMessage("كتم"),
        "muteNotifications":
            MessageLookupByLibrary.simpleMessage("كتم الإشعارات"),
        "myPrivacy": MessageLookupByLibrary.simpleMessage("خصوصيتي"),
        "name": MessageLookupByLibrary.simpleMessage("الاسم"),
        "nameMustHaveValue":
            MessageLookupByLibrary.simpleMessage("يجب أن يحتوي الاسم على قيمة"),
        "needNewAccount":
            MessageLookupByLibrary.simpleMessage("بحاجة إلى حساب جديد؟"),
        "newBroadcast": MessageLookupByLibrary.simpleMessage("بث جديد"),
        "newGroup": MessageLookupByLibrary.simpleMessage("مجموعة جديدة"),
        "newPassword":
            MessageLookupByLibrary.simpleMessage("كلمة المرور الجديدة"),
        "newPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "يجب أن تحتوي كلمة المرور الجديدة على قيمة"),
        "newUpdateIsAvailable":
            MessageLookupByLibrary.simpleMessage("تحديث جديد متاح الآن"),
        "next": MessageLookupByLibrary.simpleMessage("التالي"),
        "nickname": MessageLookupByLibrary.simpleMessage("الاسم المستعار"),
        "no": MessageLookupByLibrary.simpleMessage("لا"),
        "noBio": MessageLookupByLibrary.simpleMessage("لا توجد سيرة ذاتية"),
        "noCodeHasBeenSendToYouToVerifyYourEmail":
            MessageLookupByLibrary.simpleMessage(
                "لم يتم إرسال أي رمز للتحقق من بريدك الإلكتروني"),
        "noUpdatesAvailableNow":
            MessageLookupByLibrary.simpleMessage("لا تتوفر تحديثات الآن"),
        "none": MessageLookupByLibrary.simpleMessage("بلا"),
        "notAccepted": MessageLookupByLibrary.simpleMessage("غير مقبول"),
        "notification": MessageLookupByLibrary.simpleMessage("الإشعار"),
        "notificationDescription":
            MessageLookupByLibrary.simpleMessage("وصف الإشعار"),
        "notificationTitle":
            MessageLookupByLibrary.simpleMessage("عنوان الإشعار"),
        "notificationsPage":
            MessageLookupByLibrary.simpleMessage("صفحة الإشعارات"),
        "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion":
            MessageLookupByLibrary.simpleMessage(
                "الآن تسجيل الدخول كمسؤول قائم على القراءة فقط. لن يتم تطبيق أي تعديلات تقوم به نظرًا لأن هذا هو إصدار الاختبار."),
        "off": MessageLookupByLibrary.simpleMessage("إيقاف"),
        "offline": MessageLookupByLibrary.simpleMessage("غير متصل"),
        "ok": MessageLookupByLibrary.simpleMessage("موافق"),
        "oldPassword":
            MessageLookupByLibrary.simpleMessage("كلمة المرور القديمة"),
        "on": MessageLookupByLibrary.simpleMessage("تشغيل"),
        "oneSeenMessage":
            MessageLookupByLibrary.simpleMessage("رسالة واحدة تمت رؤيتها"),
        "online": MessageLookupByLibrary.simpleMessage("متصل"),
        "orLoginWith":
            MessageLookupByLibrary.simpleMessage("أو تسجيل الدخول باستخدام"),
        "other": MessageLookupByLibrary.simpleMessage("آخر"),
        "otherCategoryDescription": MessageLookupByLibrary.simpleMessage(
            "أخرى: يمكن استخدام هذا الفئة الشاملة للانتهاكات التي لا تناسب بسهولة الفئات السابقة. قد يكون من المفيد تضمين مربع نصي يمكن للمستخدمين من خلاله تقديم تفاصيل إضافية."),
        "otpCode": MessageLookupByLibrary.simpleMessage("رمز OTP"),
        "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
        "passwordHasBeenChanged":
            MessageLookupByLibrary.simpleMessage("تم تغيير كلمة المرور"),
        "passwordIsRequired":
            MessageLookupByLibrary.simpleMessage("كلمة المرور مطلوبة"),
        "passwordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "يجب أن تحتوي كلمة المرور على قيمة"),
        "passwordNotMatch":
            MessageLookupByLibrary.simpleMessage("كلمة المرور غير متطابقة"),
        "peerUserDeviceOffline": MessageLookupByLibrary.simpleMessage(
            "الجهاز الخاص بالمستخدم غير متصل"),
        "peerUserInCallNow":
            MessageLookupByLibrary.simpleMessage("المستخدم في مكالمة الآن"),
        "pending": MessageLookupByLibrary.simpleMessage("قيد الانتظار"),
        "phone": MessageLookupByLibrary.simpleMessage("الهاتف"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("سياسة الخصوصية"),
        "privacyUrl": MessageLookupByLibrary.simpleMessage("رابط الخصوصية"),
        "profile": MessageLookupByLibrary.simpleMessage("الملف الشخصي"),
        "promotedToAdminBy":
            MessageLookupByLibrary.simpleMessage("تم ترقيته إلى مشرف بواسطة"),
        "public": MessageLookupByLibrary.simpleMessage("عام"),
        "read": MessageLookupByLibrary.simpleMessage("تمت القراءة"),
        "recentUpdate": MessageLookupByLibrary.simpleMessage("تحديث حديث"),
        "recentUpdates":
            MessageLookupByLibrary.simpleMessage("التحديثات الأخيرة"),
        "recording": MessageLookupByLibrary.simpleMessage("جاري التسجيل..."),
        "register": MessageLookupByLibrary.simpleMessage("التسجيل"),
        "registerMethod": MessageLookupByLibrary.simpleMessage("طريقة التسجيل"),
        "registerStatus": MessageLookupByLibrary.simpleMessage("حالة التسجيل"),
        "rejected": MessageLookupByLibrary.simpleMessage("مرفوض"),
        "repliedToYourSelf":
            MessageLookupByLibrary.simpleMessage("تم الرد على نفسك"),
        "reply": MessageLookupByLibrary.simpleMessage("الرد"),
        "replyToYourSelf":
            MessageLookupByLibrary.simpleMessage("الرد على نفسك"),
        "report": MessageLookupByLibrary.simpleMessage("تقرير"),
        "reportHasBeenSubmitted":
            MessageLookupByLibrary.simpleMessage("تم تقديم التقرير الخاص بك"),
        "reportUser":
            MessageLookupByLibrary.simpleMessage("الإبلاغ عن المستخدم"),
        "reports": MessageLookupByLibrary.simpleMessage("التقارير"),
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("إعادة تعيين كلمة المرور"),
        "retry": MessageLookupByLibrary.simpleMessage("إعادة المحاولة"),
        "ring": MessageLookupByLibrary.simpleMessage("رنين"),
        "roomAlreadyInCall":
            MessageLookupByLibrary.simpleMessage("الغرفة بالفعل في مكالمة"),
        "roomCounter": MessageLookupByLibrary.simpleMessage("عداد الغرف"),
        "saveLogin": MessageLookupByLibrary.simpleMessage("حفظ تسجيل الدخول"),
        "search": MessageLookupByLibrary.simpleMessage("بحث"),
        "seconds": MessageLookupByLibrary.simpleMessage("ثواني"),
        "send": MessageLookupByLibrary.simpleMessage("إرسال"),
        "sendCodeToMyEmail": MessageLookupByLibrary.simpleMessage(
            "إرسال الرمز إلى بريدي الإلكتروني"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("إرسال رسالة"),
        "sessionEnd": MessageLookupByLibrary.simpleMessage("انتهاء الجلسة"),
        "setMaxBroadcastMembers":
            MessageLookupByLibrary.simpleMessage("تعيين حد أقصى لأعضاء البث"),
        "setMaxGroupMembers": MessageLookupByLibrary.simpleMessage(
            "تعيين حد أقصى لأعضاء المجموعة"),
        "setMaxMessageForwardAndShare": MessageLookupByLibrary.simpleMessage(
            "تعيين حد أقصى لإعادة توجيه ومشاركة الرسائل"),
        "setNewPrivacyPolicyUrl": MessageLookupByLibrary.simpleMessage(
            "تعيين رابط سياسة الخصوصية الجديد"),
        "setToAdmin": MessageLookupByLibrary.simpleMessage("تعيين كمشرف"),
        "settings": MessageLookupByLibrary.simpleMessage("الإعدادات"),
        "share": MessageLookupByLibrary.simpleMessage("مشاركة"),
        "shareMediaAndLocation":
            MessageLookupByLibrary.simpleMessage("مشاركة الوسائط والموقع"),
        "shareYourStatus": MessageLookupByLibrary.simpleMessage("شارك حالتك"),
        "showHistory": MessageLookupByLibrary.simpleMessage("عرض التاريخ"),
        "showMedia": MessageLookupByLibrary.simpleMessage("عرض الوسائط"),
        "soon": MessageLookupByLibrary.simpleMessage("قريبًا"),
        "spamOrScamDescription": MessageLookupByLibrary.simpleMessage(
            "البريد المزعج أو الاحتيال: يمكن للمستخدمين الإبلاغ عن حسابات ترسل رسائل بريد مزعجة أو إعلانات غير مرغوب فيها أو تحاول خداع الآخرين."),
        "star": MessageLookupByLibrary.simpleMessage("تثبيت"),
        "starMessage": MessageLookupByLibrary.simpleMessage("تثبيت الرسالة"),
        "starredMessage":
            MessageLookupByLibrary.simpleMessage("الرسائل المثبتة"),
        "starredMessages": MessageLookupByLibrary.simpleMessage("رسائل مميزة"),
        "startChat": MessageLookupByLibrary.simpleMessage("بدء الدردشة"),
        "startNewChatWithYou":
            MessageLookupByLibrary.simpleMessage("بدء دردشة جديدة معك"),
        "status": MessageLookupByLibrary.simpleMessage("الحالة"),
        "storageAndData":
            MessageLookupByLibrary.simpleMessage("التخزين والبيانات"),
        "stories": MessageLookupByLibrary.simpleMessage("قصص"),
        "storyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم إنشاء القصة بنجاح"),
        "success": MessageLookupByLibrary.simpleMessage("نجاح"),
        "successfullyDownloadedIn":
            MessageLookupByLibrary.simpleMessage("تم التنزيل بنجاح في"),
        "supportChatSoon":
            MessageLookupByLibrary.simpleMessage("الدعم عبر الدردشة (قريبًا)"),
        "tapADeviceToEditOrLogOut": MessageLookupByLibrary.simpleMessage(
            "اضغط على جهاز للتعديل أو تسجيل الخروج."),
        "tapForPhoto":
            MessageLookupByLibrary.simpleMessage("انقر للحصول على صورة"),
        "tapToSelectAnIcon":
            MessageLookupByLibrary.simpleMessage("اضغط لاختيار أيقونة"),
        "tellAFriend": MessageLookupByLibrary.simpleMessage("أخبر صديقًا"),
        "textFieldHint": MessageLookupByLibrary.simpleMessage("اكتب رسالة..."),
        "textMessages": MessageLookupByLibrary.simpleMessage("رسائل نصية"),
        "thereIsFileHasSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "هناك ملف بحجم أكبر من الحجم المسموح به"),
        "thereIsVideoSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "هناك فيديو بحجم أكبر من الحجم المسموح به"),
        "timeout": MessageLookupByLibrary.simpleMessage("انتهاء الوقت"),
        "titleIsRequired":
            MessageLookupByLibrary.simpleMessage("العنوان مطلوب"),
        "today": MessageLookupByLibrary.simpleMessage("اليوم"),
        "total": MessageLookupByLibrary.simpleMessage("الإجمالي"),
        "totalMessages": MessageLookupByLibrary.simpleMessage("إجمالي الرسائل"),
        "totalRooms": MessageLookupByLibrary.simpleMessage("إجمالي الغرف"),
        "totalVisits": MessageLookupByLibrary.simpleMessage("إجمالي الزيارات"),
        "typing": MessageLookupByLibrary.simpleMessage("يكتب..."),
        "unBlock": MessageLookupByLibrary.simpleMessage("إلغاء الحظر"),
        "unBlockUser":
            MessageLookupByLibrary.simpleMessage("إلغاء حظر المستخدم"),
        "unMute": MessageLookupByLibrary.simpleMessage("إلغاء الكتم"),
        "unStar": MessageLookupByLibrary.simpleMessage("إلغاء التمييز"),
        "update": MessageLookupByLibrary.simpleMessage("تحديث"),
        "updateBroadcastTitle":
            MessageLookupByLibrary.simpleMessage("تحديث عنوان البث"),
        "updateFeedBackEmail": MessageLookupByLibrary.simpleMessage(
            "تحديث عنوان البريد الإلكتروني للملاحظات"),
        "updateGroupDescription":
            MessageLookupByLibrary.simpleMessage("تحديث وصف المجموعة"),
        "updateGroupDescriptionWillUpdateAllGroupMembers":
            MessageLookupByLibrary.simpleMessage(
                "سيتم تحديث وصف المجموعة وجميع أعضاء المجموعة"),
        "updateGroupTitle":
            MessageLookupByLibrary.simpleMessage("تحديث عنوان المجموعة"),
        "updateImage": MessageLookupByLibrary.simpleMessage("تحديث الصورة"),
        "updateNickname":
            MessageLookupByLibrary.simpleMessage("تحديث الاسم المستعار"),
        "updateTitle": MessageLookupByLibrary.simpleMessage("تحديث العنوان"),
        "updateTitleTo":
            MessageLookupByLibrary.simpleMessage("تحديث العنوان إلى"),
        "updateYourBio": MessageLookupByLibrary.simpleMessage("تحديث نبذة عنك"),
        "updateYourName": MessageLookupByLibrary.simpleMessage("تحديث اسمك"),
        "updateYourPassword":
            MessageLookupByLibrary.simpleMessage("تحديث كلمة المرور الخاصة بك"),
        "updateYourProfile":
            MessageLookupByLibrary.simpleMessage("تحديث ملفك الشخصي"),
        "updatedAt": MessageLookupByLibrary.simpleMessage("تم التحديث في"),
        "upgradeToAdmin":
            MessageLookupByLibrary.simpleMessage("الترقية إلى مسؤول"),
        "userAction": MessageLookupByLibrary.simpleMessage("إجراء المستخدم"),
        "userAlreadyRegister":
            MessageLookupByLibrary.simpleMessage("المستخدم مسجل بالفعل"),
        "userDeviceSessionEndDeviceDeleted":
            MessageLookupByLibrary.simpleMessage(
                "جلسة جهاز المستخدم انتهت وتم حذف الجهاز"),
        "userEmailNotFound": MessageLookupByLibrary.simpleMessage(
            "عنوان البريد الإلكتروني للمستخدم غير موجود"),
        "userInfo": MessageLookupByLibrary.simpleMessage("معلومات المستخدم"),
        "userPage": MessageLookupByLibrary.simpleMessage("صفحة المستخدم"),
        "userProfile": MessageLookupByLibrary.simpleMessage("ملف المستخدم"),
        "userRegisterStatusNotAcceptedYet":
            MessageLookupByLibrary.simpleMessage(
                "حالة تسجيل المستخدم لم تتم قبولها بعد"),
        "users": MessageLookupByLibrary.simpleMessage("المستخدمين"),
        "usersAddedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تمت إضافة المستخدمين بنجاح"),
        "vMessageInfoTrans":
            MessageLookupByLibrary.simpleMessage("معلومات الرسالة"),
        "vMessagesInfoTrans":
            MessageLookupByLibrary.simpleMessage("معلومات الرسائل"),
        "verified": MessageLookupByLibrary.simpleMessage("تم التحقق"),
        "verifiedAt": MessageLookupByLibrary.simpleMessage("تم التحقق في"),
        "video": MessageLookupByLibrary.simpleMessage("فيديو"),
        "videoCallMessages":
            MessageLookupByLibrary.simpleMessage("رسائل مكالمات فيديو"),
        "videoMessages": MessageLookupByLibrary.simpleMessage("رسائل فيديو"),
        "visits": MessageLookupByLibrary.simpleMessage("زيارات"),
        "voiceCallMessage":
            MessageLookupByLibrary.simpleMessage("رسالة مكالمة صوتية"),
        "voiceCallMessages":
            MessageLookupByLibrary.simpleMessage("رسائل مكالمات صوتية"),
        "voiceMessages": MessageLookupByLibrary.simpleMessage("رسائل صوتية"),
        "wait2MinutesToSendMail":
            MessageLookupByLibrary.simpleMessage("انتظر دقيقتين لإرسال البريد"),
        "waitingList": MessageLookupByLibrary.simpleMessage("قائمة الانتظار"),
        "weHighRecommendToDownloadThisUpdate":
            MessageLookupByLibrary.simpleMessage(
                "نوصي بشدة بتنزيل هذا التحديث"),
        "web": MessageLookupByLibrary.simpleMessage("ويب"),
        "welcome": MessageLookupByLibrary.simpleMessage("مرحبًا"),
        "whenUsingMobileData":
            MessageLookupByLibrary.simpleMessage("عند استخدام بيانات الجوال"),
        "whenUsingWifi":
            MessageLookupByLibrary.simpleMessage("عند استخدام Wi-Fi"),
        "whileAuthCanFindYou": MessageLookupByLibrary.simpleMessage(
            "أثناء المصادقة لا يمكن العثور عليك"),
        "windows": MessageLookupByLibrary.simpleMessage("ويندوز"),
        "writeACaption":
            MessageLookupByLibrary.simpleMessage("اكتب تعليقا ..."),
        "yes": MessageLookupByLibrary.simpleMessage("نعم"),
        "yesterday": MessageLookupByLibrary.simpleMessage("أمس"),
        "you": MessageLookupByLibrary.simpleMessage("أنت"),
        "youAreAboutToDeleteThisUserFromYourList":
            MessageLookupByLibrary.simpleMessage(
                "أنت على وشك حذف هذا المستخدم من قائمتك"),
        "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList":
            MessageLookupByLibrary.simpleMessage(
                "أنت على وشك حذف حسابك، لن يظهر حسابك مرة أخرى في قائمة المستخدمين"),
        "youAreAboutToDismissesToMember":
            MessageLookupByLibrary.simpleMessage("أنت على وشك تنزيل إلى عضو"),
        "youAreAboutToKick":
            MessageLookupByLibrary.simpleMessage("أنت على وشك الطرد"),
        "youAreAboutToUpgradeToAdmin": MessageLookupByLibrary.simpleMessage(
            "أنت على وشك الترقية إلى مشرف"),
        "youDontHaveAccess":
            MessageLookupByLibrary.simpleMessage("ليس لديك صلاحية الوصول"),
        "youInPublicSearch":
            MessageLookupByLibrary.simpleMessage("أنت في البحث العام"),
        "youNotParticipantInThisGroup": MessageLookupByLibrary.simpleMessage(
            "أنت لست مشاركًا في هذه المجموعة"),
        "yourAccountBlocked":
            MessageLookupByLibrary.simpleMessage("تم حظر حسابك"),
        "yourAccountDeleted":
            MessageLookupByLibrary.simpleMessage("تم حذف حسابك"),
        "yourAccountIsUnderReview":
            MessageLookupByLibrary.simpleMessage("حسابك قيد المراجعة"),
        "yourAreAboutToLogoutFromThisAccount":
            MessageLookupByLibrary.simpleMessage(
                "أنت على وشك تسجيل الخروج من هذا الحساب"),
        "yourLastSeen": MessageLookupByLibrary.simpleMessage("آخر ظهور لك"),
        "yourLastSeenInChats":
            MessageLookupByLibrary.simpleMessage("آخر ظهور لك في الدردشات"),
        "yourProfileAppearsInPublicSearchAndAddingForGroups":
            MessageLookupByLibrary.simpleMessage(
                "يظهر ملفك الشخصي في البحث العام والإضافة للمجموعات"),
        "yourSessionIsEndedPleaseLoginAgain":
            MessageLookupByLibrary.simpleMessage(
                "انتهت جلستك، يرجى تسجيل الدخول مرة أخرى!"),
        "yourStory": MessageLookupByLibrary.simpleMessage("قصتك")
      };
}
