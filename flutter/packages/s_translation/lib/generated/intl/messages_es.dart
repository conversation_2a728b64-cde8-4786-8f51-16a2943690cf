// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a es locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'es';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Dashboard": MessageLookupByLibrary.simpleMessage("Panel"),
        "about": MessageLookupByLibrary.simpleMessage("Acerca de"),
        "aboutToBlockUserWithConsequences": MessageLookupByLibrary.simpleMessage(
            "Estás a punto de bloquear a este usuario.¡Puedes conocerlo gatos y no puedes agregarlo a grupos o transmitir!"),
        "accepted": MessageLookupByLibrary.simpleMessage("Aceptado"),
        "account": MessageLookupByLibrary.simpleMessage("Nacimiento"),
        "actions": MessageLookupByLibrary.simpleMessage("Comportamiento"),
        "addMembers": MessageLookupByLibrary.simpleMessage("Agregar miembros"),
        "addNewStory":
            MessageLookupByLibrary.simpleMessage("Agregar nueva historia"),
        "addParticipants":
            MessageLookupByLibrary.simpleMessage("Agregar participantes"),
        "addedYouToNewBroadcast": MessageLookupByLibrary.simpleMessage(
            "Te agregó a una nueva transmisión"),
        "admin": MessageLookupByLibrary.simpleMessage("Administración"),
        "adminNotification": MessageLookupByLibrary.simpleMessage(
            "Notificación de administración"),
        "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself":
            MessageLookupByLibrary.simpleMessage(
                "¡Todos los datos han sido una copia de seguridad de que no desea administrar guardar los datos por usted mismo!Si inicia sesión e inicia sesión nuevamente, verá todos los gatos lo mismo para la versión web"),
        "allDeletedMessages": MessageLookupByLibrary.simpleMessage(
            "Todos los mensajes eliminados"),
        "allowAds": MessageLookupByLibrary.simpleMessage("Permitir anuncios"),
        "allowCalls": MessageLookupByLibrary.simpleMessage("Permitir llamadas"),
        "allowCreateBroadcast":
            MessageLookupByLibrary.simpleMessage("Permitir crear transmisión"),
        "allowCreateGroups":
            MessageLookupByLibrary.simpleMessage("Permitir crear grupos"),
        "allowDesktopLogin": MessageLookupByLibrary.simpleMessage(
            "Permitir inicio de sesión de escritorio"),
        "allowMobileLogin": MessageLookupByLibrary.simpleMessage(
            "Permitir el inicio de sesión móvil"),
        "allowSendMedia":
            MessageLookupByLibrary.simpleMessage("Permitir enviar medios"),
        "allowWebLogin": MessageLookupByLibrary.simpleMessage(
            "Permitir el inicio de sesión web"),
        "alreadyHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("¿Ya tienes una cuenta?"),
        "android": MessageLookupByLibrary.simpleMessage("Androide"),
        "appMembers":
            MessageLookupByLibrary.simpleMessage("Miembros de la aplicación"),
        "appleStoreAppUrl": MessageLookupByLibrary.simpleMessage(
            "URL de la aplicación Apple Store"),
        "areYouSure": MessageLookupByLibrary.simpleMessage("¿Está seguro?"),
        "areYouSureToBlock":
            MessageLookupByLibrary.simpleMessage("¿Estás seguro de bloquear?"),
        "areYouSureToLeaveThisGroupThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "¿Estás seguro de dejar este grupo?Esta acción no puede deshacer"),
        "areYouSureToPermitYourCopyThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "¿Estás seguro de permitir tu copia?Esta acción no puede deshacer"),
        "areYouSureToReportUserToAdmin": MessageLookupByLibrary.simpleMessage(
            "¿Seguro que enviará un informe sobre este usuario al administrador?"),
        "areYouSureToUnBlock":
            MessageLookupByLibrary.simpleMessage("¿Estás seguro de ablock?"),
        "areYouWantToMakeVideoCall": MessageLookupByLibrary.simpleMessage(
            "¿Quieres hacer videollamadas?"),
        "areYouWantToMakeVoiceCall": MessageLookupByLibrary.simpleMessage(
            "¿Quieres hacer una llamada de voz?"),
        "audio": MessageLookupByLibrary.simpleMessage("Audio"),
        "audioCall": MessageLookupByLibrary.simpleMessage("Audio llamado"),
        "back": MessageLookupByLibrary.simpleMessage("Atrás"),
        "banAt": MessageLookupByLibrary.simpleMessage("Prohibir"),
        "banTo": MessageLookupByLibrary.simpleMessage("Prohibir"),
        "bio": MessageLookupByLibrary.simpleMessage("Orgánico"),
        "block": MessageLookupByLibrary.simpleMessage("Bloquear"),
        "blockUser": MessageLookupByLibrary.simpleMessage("Bloque de usuarios"),
        "blocked": MessageLookupByLibrary.simpleMessage("Obstruido"),
        "blockedUsers":
            MessageLookupByLibrary.simpleMessage("Usuarios de bloque"),
        "broadcast": MessageLookupByLibrary.simpleMessage("Transmisión"),
        "broadcastInfo":
            MessageLookupByLibrary.simpleMessage("Información de transmisión"),
        "broadcastMembers":
            MessageLookupByLibrary.simpleMessage("Miembros de la transmisión"),
        "broadcastName":
            MessageLookupByLibrary.simpleMessage("Nombre de transmisión"),
        "broadcastParticipants": MessageLookupByLibrary.simpleMessage(
            "Participantes de transmisión"),
        "broadcastSettings": MessageLookupByLibrary.simpleMessage(
            "Configuración de transmisión"),
        "callNotAllowed":
            MessageLookupByLibrary.simpleMessage("Llamar no permitido"),
        "callTimeoutInSeconds": MessageLookupByLibrary.simpleMessage(
            "Llame al tiempo de espera en segundos"),
        "calls": MessageLookupByLibrary.simpleMessage("Llamadas"),
        "camera": MessageLookupByLibrary.simpleMessage("Cámara"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancelar"),
        "canceled": MessageLookupByLibrary.simpleMessage("Cancelado"),
        "changeSubject": MessageLookupByLibrary.simpleMessage("Cambiar sujeto"),
        "chat": MessageLookupByLibrary.simpleMessage("Gato"),
        "chats": MessageLookupByLibrary.simpleMessage("Gatos"),
        "checkForUpdates": MessageLookupByLibrary.simpleMessage(
            "Verifique las actualizaciones"),
        "chooseAtLestOneMember":
            MessageLookupByLibrary.simpleMessage("Elija al menos un miembro"),
        "chooseHowAutomaticDownloadWorks": MessageLookupByLibrary.simpleMessage(
            "Elija cómo funciona la descarga automática"),
        "chooseRoom": MessageLookupByLibrary.simpleMessage("Elija habitación"),
        "clear": MessageLookupByLibrary.simpleMessage("Claro"),
        "clearCallsConfirm":
            MessageLookupByLibrary.simpleMessage("Llamadas de claro confirmar"),
        "clearChat": MessageLookupByLibrary.simpleMessage("Gato claro"),
        "clickToAddGroupDescription": MessageLookupByLibrary.simpleMessage(
            "Haga clic para agregar la descripción del grupo"),
        "clickToSee":
            MessageLookupByLibrary.simpleMessage("Haga clic para ver"),
        "clickToSeeAllUserCountries": MessageLookupByLibrary.simpleMessage(
            "Haga clic para ver a todos los países de los usuarios"),
        "clickToSeeAllUserDevicesDetails": MessageLookupByLibrary.simpleMessage(
            "Haga clic para ver todos los detalles de los dispositivos de usuario"),
        "clickToSeeAllUserInformations": MessageLookupByLibrary.simpleMessage(
            "Haga clic para ver toda la información del usuario"),
        "clickToSeeAllUserMessagesDetails": MessageLookupByLibrary.simpleMessage(
            "Haga clic para ver todos los detalles de los mensajes de usuario"),
        "clickToSeeAllUserReports": MessageLookupByLibrary.simpleMessage(
            "Haga clic para ver todos los informes de los usuarios"),
        "clickToSeeAllUserRoomsDetails": MessageLookupByLibrary.simpleMessage(
            "Haga clic para ver todos los detalles de las habitaciones de usuario"),
        "close": MessageLookupByLibrary.simpleMessage("Cerrado"),
        "codeHasBeenExpired":
            MessageLookupByLibrary.simpleMessage("Ha sido código de caducidad"),
        "codeMustEqualToSixNumbers": MessageLookupByLibrary.simpleMessage(
            "El código debe igual a seis números"),
        "configureYourAccountPrivacy": MessageLookupByLibrary.simpleMessage(
            "Configurar la privacidad de su cuenta"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("confirmar Contraseña"),
        "confirmPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Confirmar la contraseña debe tener valor"),
        "congregationsYourAccountHasBeenAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Congregaciones Su cuenta ha sido aceptada"),
        "connecting": MessageLookupByLibrary.simpleMessage("Conectando ..."),
        "contactInfo":
            MessageLookupByLibrary.simpleMessage("Información de contacto"),
        "contactUs":
            MessageLookupByLibrary.simpleMessage("Contacto de EE. UU."),
        "copy": MessageLookupByLibrary.simpleMessage("Copiar"),
        "countries": MessageLookupByLibrary.simpleMessage("Manejo"),
        "country": MessageLookupByLibrary.simpleMessage("País"),
        "create": MessageLookupByLibrary.simpleMessage("Crear"),
        "createBroadcast":
            MessageLookupByLibrary.simpleMessage("Crear transmisión"),
        "createGroup": MessageLookupByLibrary.simpleMessage("Crear grupo"),
        "createMediaStory": MessageLookupByLibrary.simpleMessage(
            "Crear historia de los medios"),
        "createStory":
            MessageLookupByLibrary.simpleMessage("Crear una historia"),
        "createTextStory":
            MessageLookupByLibrary.simpleMessage("Crear historia de texto"),
        "createYourStory":
            MessageLookupByLibrary.simpleMessage("Crea tu historia"),
        "createdAt": MessageLookupByLibrary.simpleMessage("Creado a"),
        "creator": MessageLookupByLibrary.simpleMessage("Creador"),
        "currentDevice":
            MessageLookupByLibrary.simpleMessage("Dispositivo actual"),
        "dashboard": MessageLookupByLibrary.simpleMessage("Panel"),
        "dataPrivacy":
            MessageLookupByLibrary.simpleMessage("Privacidad de datos"),
        "delete": MessageLookupByLibrary.simpleMessage("Borrar"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("Gato eliminar"),
        "deleteFromAll":
            MessageLookupByLibrary.simpleMessage("Eliminar de todos"),
        "deleteFromMe": MessageLookupByLibrary.simpleMessage("Eliminar de mi"),
        "deleteMember":
            MessageLookupByLibrary.simpleMessage("Eliminar miembro"),
        "deleteMyAccount":
            MessageLookupByLibrary.simpleMessage("Eliminar mi cuenta"),
        "deleteThisDeviceDesc": MessageLookupByLibrary.simpleMessage(
            "Eliminar este dispositivo significa que inicia sesión instantáneamente este dispositivo"),
        "deleteUser": MessageLookupByLibrary.simpleMessage("Eliminar usuario"),
        "deleteYouCopy":
            MessageLookupByLibrary.simpleMessage("Elimina tu copia"),
        "deleted": MessageLookupByLibrary.simpleMessage("Eliminado"),
        "deletedAt": MessageLookupByLibrary.simpleMessage("Eliminado en"),
        "delivered": MessageLookupByLibrary.simpleMessage("Entregado"),
        "description": MessageLookupByLibrary.simpleMessage("Descripción"),
        "descriptionIsRequired":
            MessageLookupByLibrary.simpleMessage("Se requiere una descripción"),
        "desktopAndOtherDevices": MessageLookupByLibrary.simpleMessage(
            "Escritorio y otros dispositivos"),
        "deviceHasBeenLogoutFromAllDevices": MessageLookupByLibrary.simpleMessage(
            "El dispositivo ha sido inicio de sesión desde todos los dispositivos"),
        "deviceStatus":
            MessageLookupByLibrary.simpleMessage("Estado del dispositivo"),
        "devices": MessageLookupByLibrary.simpleMessage("Dispositivos"),
        "directChat": MessageLookupByLibrary.simpleMessage("Gato directo"),
        "directRooms":
            MessageLookupByLibrary.simpleMessage("Habitaciones directas"),
        "dismissedToMemberBy":
            MessageLookupByLibrary.simpleMessage("Disminizado al miembro por"),
        "dismissesToMember":
            MessageLookupByLibrary.simpleMessage("Despedida al miembro"),
        "docs": MessageLookupByLibrary.simpleMessage("Documento"),
        "done": MessageLookupByLibrary.simpleMessage("Hecho"),
        "download": MessageLookupByLibrary.simpleMessage("Descargar"),
        "downloading": MessageLookupByLibrary.simpleMessage("Descarga ..."),
        "edit": MessageLookupByLibrary.simpleMessage("Editar"),
        "email": MessageLookupByLibrary.simpleMessage("Correo electrónico"),
        "emailMustBeValid": MessageLookupByLibrary.simpleMessage(
            "El correo electrónico debe ser válido"),
        "emailNotValid": MessageLookupByLibrary.simpleMessage(
            "Correo electrónico no válido"),
        "enterNameAndAddOptionalProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Ingrese su nombre y agregue una foto de perfil opcional"),
        "error": MessageLookupByLibrary.simpleMessage("Error"),
        "exitGroup": MessageLookupByLibrary.simpleMessage("Grupo de salida"),
        "explainWhatHappens":
            MessageLookupByLibrary.simpleMessage("Explica aquí lo que pasa"),
        "feedBackEmail": MessageLookupByLibrary.simpleMessage(
            "Correo electrónico de comentarios"),
        "fileHasBeenSavedTo": MessageLookupByLibrary.simpleMessage(
            "El archivo se ha guardado en"),
        "fileMessages":
            MessageLookupByLibrary.simpleMessage("Mensajes de archivo"),
        "files": MessageLookupByLibrary.simpleMessage("Pauta"),
        "finished": MessageLookupByLibrary.simpleMessage("Finalizado"),
        "forRequest": MessageLookupByLibrary.simpleMessage("Por solicitud"),
        "forgetPassword":
            MessageLookupByLibrary.simpleMessage("¿Olvidar la contraseña?"),
        "forgetPasswordExpireTime": MessageLookupByLibrary.simpleMessage(
            "Olvidar la contraseña expira el tiempo"),
        "forward": MessageLookupByLibrary.simpleMessage("Adelante"),
        "fullName": MessageLookupByLibrary.simpleMessage("Nombre completo"),
        "gallery": MessageLookupByLibrary.simpleMessage("Galería"),
        "globalSearch": MessageLookupByLibrary.simpleMessage("Búsqueda global"),
        "googlePlayAppUrl": MessageLookupByLibrary.simpleMessage(
            "URL de la aplicación Google Play"),
        "group": MessageLookupByLibrary.simpleMessage("Grupo"),
        "groupCreatedBy":
            MessageLookupByLibrary.simpleMessage("Grupo creado por"),
        "groupDescription":
            MessageLookupByLibrary.simpleMessage("Descripción del grupo"),
        "groupIcon": MessageLookupByLibrary.simpleMessage("Icono grupal"),
        "groupInfo": MessageLookupByLibrary.simpleMessage("Información grupal"),
        "groupMembers":
            MessageLookupByLibrary.simpleMessage("Miembros del grupo"),
        "groupName": MessageLookupByLibrary.simpleMessage("Nombre de grupo"),
        "groupParticipants":
            MessageLookupByLibrary.simpleMessage("Grupo de participantes"),
        "groupSettings":
            MessageLookupByLibrary.simpleMessage("Configuración grupal"),
        "groupWith": MessageLookupByLibrary.simpleMessage("Agrupar con"),
        "harassmentOrBullyingDescription": MessageLookupByLibrary.simpleMessage(
            "Acoso o intimidación: esta opción permite a los usuarios informar a las personas que se dirigen a ellos u otros con mensajes de acoso, amenazas u otras formas de intimidación."),
        "help": MessageLookupByLibrary.simpleMessage("Ayuda"),
        "hiIamUse": MessageLookupByLibrary.simpleMessage("Hola iam usando"),
        "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Si esta opción está deshabilitada, la creación de la transmisión de chat se bloqueará."),
        "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Si esta opción está deshabilitada, la creación de grupos de chat se bloqueará."),
        "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Si esta opción está deshabilitada, se bloqueará el inicio de sesión o registro de escritorio (Windows, Mac)."),
        "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly":
            MessageLookupByLibrary.simpleMessage(
                "Si esta opción está deshabilitada, el inicio de sesión o el registro móvil se bloqueará solo en Android e iOS."),
        "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Si esta opción está deshabilitada, se bloqueará el envío de archivos, imágenes, videos y alquiler CAT."),
        "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Si esta opción está deshabilitada, el inicio de sesión o registro web se bloqueará."),
        "ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats":
            MessageLookupByLibrary.simpleMessage(
                "Si esta opción está habilitada, el banner de Google ADS aparecerá en gatos."),
        "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed":
            MessageLookupByLibrary.simpleMessage(
                "Si esta opción está habilitada, se permitirán la llamada de video y voz."),
        "image": MessageLookupByLibrary.simpleMessage("Imagen"),
        "imageMessages":
            MessageLookupByLibrary.simpleMessage("Mensajes de imagen"),
        "images": MessageLookupByLibrary.simpleMessage("Imágenes"),
        "inAppAlerts":
            MessageLookupByLibrary.simpleMessage("En alertas de aplicaciones"),
        "inCall": MessageLookupByLibrary.simpleMessage("En la llamada"),
        "inappropriateContentDescription": MessageLookupByLibrary.simpleMessage(
            "Contenido inapropiado: los usuarios pueden seleccionar esta opción para informar cualquier material explícito sexual, discurso de odio u otro contenido que el estándar de la comunidad violenta."),
        "info": MessageLookupByLibrary.simpleMessage("Información"),
        "infoMessages":
            MessageLookupByLibrary.simpleMessage("Mensajes de información"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("Código no válido"),
        "invalidLoginData": MessageLookupByLibrary.simpleMessage(
            "Datos de inicio de sesión no válidos"),
        "ios": MessageLookupByLibrary.simpleMessage("iOS"),
        "joinedAt": MessageLookupByLibrary.simpleMessage("Unido a"),
        "joinedBy": MessageLookupByLibrary.simpleMessage("Unido por"),
        "kickMember":
            MessageLookupByLibrary.simpleMessage("Miembro de la patada"),
        "kickedBy": MessageLookupByLibrary.simpleMessage("Pateado por"),
        "language": MessageLookupByLibrary.simpleMessage("Idioma"),
        "lastActiveFrom":
            MessageLookupByLibrary.simpleMessage("Último activo desde"),
        "leaveGroup": MessageLookupByLibrary.simpleMessage("Dejar el grupo"),
        "leaveGroupAndDeleteYourMessageCopy":
            MessageLookupByLibrary.simpleMessage(
                "Deja el grupo y elimina la copia de tu mensaje"),
        "leftTheGroup": MessageLookupByLibrary.simpleMessage("Dejó el grupo"),
        "linkADeviceSoon": MessageLookupByLibrary.simpleMessage(
            "Enlace un dispositivo (pronto)"),
        "linkByQrCode":
            MessageLookupByLibrary.simpleMessage("Enlace por código QR"),
        "linkedDevices":
            MessageLookupByLibrary.simpleMessage("Dispositivos vinculados"),
        "links": MessageLookupByLibrary.simpleMessage("Campo de golf"),
        "loading": MessageLookupByLibrary.simpleMessage("Cargando ..."),
        "location": MessageLookupByLibrary.simpleMessage("Alquiler"),
        "locationMessages":
            MessageLookupByLibrary.simpleMessage("Alquiler de mensajes"),
        "logOut": MessageLookupByLibrary.simpleMessage("Finalizar la sesión"),
        "login": MessageLookupByLibrary.simpleMessage("Acceso"),
        "loginAgain":
            MessageLookupByLibrary.simpleMessage("¡Inicie sesión de nuevo!"),
        "loginNowAllowedNowPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Iniciar sesión ahora permitido.Vuelva a intentarlo más tarde."),
        "logoutFromAllDevices": MessageLookupByLibrary.simpleMessage(
            "¿INCARGE de todos los dispositivos?"),
        "macOs": MessageLookupByLibrary.simpleMessage("macosa"),
        "makeCall": MessageLookupByLibrary.simpleMessage("Hacer llamadas"),
        "media": MessageLookupByLibrary.simpleMessage("Medios de comunicación"),
        "mediaLinksAndDocs": MessageLookupByLibrary.simpleMessage(
            "Medios, enlaces y documentos"),
        "member": MessageLookupByLibrary.simpleMessage("Colocar"),
        "members": MessageLookupByLibrary.simpleMessage("Colocar"),
        "messageCounter": MessageLookupByLibrary.simpleMessage("Mostrador"),
        "messageHasBeenDeleted":
            MessageLookupByLibrary.simpleMessage("Se ha eliminado el mensaje"),
        "messageHasBeenViewed":
            MessageLookupByLibrary.simpleMessage("Se ha visto el mensaje"),
        "messageInfo":
            MessageLookupByLibrary.simpleMessage("Mensaje de información"),
        "messages": MessageLookupByLibrary.simpleMessage("Mensajes"),
        "microphoneAndCameraPermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "El micrófono y el permiso de la cámara deben"),
        "microphonePermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "El permiso de micrófono debe ser aceptado"),
        "minutes": MessageLookupByLibrary.simpleMessage("Minutos"),
        "more": MessageLookupByLibrary.simpleMessage("Más"),
        "mute": MessageLookupByLibrary.simpleMessage("Silenciar"),
        "muteNotifications":
            MessageLookupByLibrary.simpleMessage("Notificaciones mudas"),
        "myPrivacy": MessageLookupByLibrary.simpleMessage("Mi privacidad"),
        "name": MessageLookupByLibrary.simpleMessage("Nombre"),
        "nameMustHaveValue":
            MessageLookupByLibrary.simpleMessage("El nombre debe tener valor"),
        "needNewAccount": MessageLookupByLibrary.simpleMessage(
            "¿Necesitas una nueva cuenta?"),
        "newBroadcast":
            MessageLookupByLibrary.simpleMessage("Nueva transmisión"),
        "newGroup": MessageLookupByLibrary.simpleMessage("Nuevo grupo"),
        "newPassword": MessageLookupByLibrary.simpleMessage("Nueva contraseña"),
        "newPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Nueva contraseña debe tener valor"),
        "newUpdateIsAvailable": MessageLookupByLibrary.simpleMessage(
            "Una nueva actualización está disponible"),
        "next": MessageLookupByLibrary.simpleMessage("Próximo"),
        "nickname": MessageLookupByLibrary.simpleMessage("Apodo"),
        "no": MessageLookupByLibrary.simpleMessage("No"),
        "noBio": MessageLookupByLibrary.simpleMessage("No biografía"),
        "noCodeHasBeenSendToYouToVerifyYourEmail":
            MessageLookupByLibrary.simpleMessage(
                "Ningún código ha sido sen a usted para verificar su correo electrónico"),
        "noUpdatesAvailableNow": MessageLookupByLibrary.simpleMessage(
            "No hay actualizaciones disponibles ahora"),
        "none": MessageLookupByLibrary.simpleMessage("Ninguno"),
        "notAccepted": MessageLookupByLibrary.simpleMessage("No aceptar"),
        "notification": MessageLookupByLibrary.simpleMessage("Notificación"),
        "notificationDescription":
            MessageLookupByLibrary.simpleMessage("Notificación de descripción"),
        "notificationTitle":
            MessageLookupByLibrary.simpleMessage("Notificación de título"),
        "notificationsPage":
            MessageLookupByLibrary.simpleMessage("Página de notificaciones"),
        "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion":
            MessageLookupByLibrary.simpleMessage(
                "Ahora inicia sesión como administrador de solo lectura.Todas las ediciones que realice no se aplicarán debido a que esta es una versión de prueba."),
        "off": MessageLookupByLibrary.simpleMessage("Apagado"),
        "offline": MessageLookupByLibrary.simpleMessage("Desconectado"),
        "ok": MessageLookupByLibrary.simpleMessage("DE ACUERDO"),
        "oldPassword":
            MessageLookupByLibrary.simpleMessage("Contraseña anterior"),
        "on": MessageLookupByLibrary.simpleMessage("Nosotros"),
        "oneSeenMessage": MessageLookupByLibrary.simpleMessage("Un mensaje"),
        "online": MessageLookupByLibrary.simpleMessage("En línea"),
        "orLoginWith":
            MessageLookupByLibrary.simpleMessage("Gold Iniciar sesión con"),
        "other": MessageLookupByLibrary.simpleMessage("Otro"),
        "otherCategoryDescription": MessageLookupByLibrary.simpleMessage(
            "Otro: esta categoría de Catch-All se puede usar para violaciones que se ajustan fácilmente a las categorías de Abovers.Puede ser útil incluir un cuadro de texto para que los usuarios proporcionen detalles adicionales."),
        "otpCode": MessageLookupByLibrary.simpleMessage("Código OTP"),
        "password": MessageLookupByLibrary.simpleMessage("Contraseña"),
        "passwordHasBeenChanged": MessageLookupByLibrary.simpleMessage(
            "Se ha cambiado la contraseña"),
        "passwordIsRequired":
            MessageLookupByLibrary.simpleMessage("Se requiere contraseña"),
        "passwordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "La contraseña debe tener valor"),
        "passwordNotMatch":
            MessageLookupByLibrary.simpleMessage("Contraseña no coincide"),
        "peerUserDeviceOffline": MessageLookupByLibrary.simpleMessage(
            "Dispositivo de usuario de par sin conexión"),
        "peerUserInCallNow":
            MessageLookupByLibrary.simpleMessage("Usuario en la llamada ahora"),
        "pending": MessageLookupByLibrary.simpleMessage("Colgante"),
        "phone": MessageLookupByLibrary.simpleMessage("Teléfono"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("política de privacidad"),
        "privacyUrl": MessageLookupByLibrary.simpleMessage("URL de privacidad"),
        "profile": MessageLookupByLibrary.simpleMessage("Perfil"),
        "promotedToAdminBy": MessageLookupByLibrary.simpleMessage(
            "Ascendido a administrador por"),
        "public": MessageLookupByLibrary.simpleMessage("Audiencia"),
        "read": MessageLookupByLibrary.simpleMessage("Leer"),
        "recentUpdate":
            MessageLookupByLibrary.simpleMessage("Actualización reciente"),
        "recentUpdates":
            MessageLookupByLibrary.simpleMessage("Actualizaciones recientes"),
        "recording": MessageLookupByLibrary.simpleMessage("Grabación ..."),
        "register": MessageLookupByLibrary.simpleMessage("Registro"),
        "registerMethod":
            MessageLookupByLibrary.simpleMessage("Método Registrarse"),
        "registerStatus":
            MessageLookupByLibrary.simpleMessage("Estado de registro"),
        "rejected": MessageLookupByLibrary.simpleMessage("Rechazado"),
        "repliedToYourSelf":
            MessageLookupByLibrary.simpleMessage("Replic para ti mismo"),
        "reply": MessageLookupByLibrary.simpleMessage("Responder"),
        "replyToYourSelf":
            MessageLookupByLibrary.simpleMessage("Responder a ti mismo"),
        "report": MessageLookupByLibrary.simpleMessage("Aplazamiento"),
        "reportHasBeenSubmitted": MessageLookupByLibrary.simpleMessage(
            "Su aplazamiento ha sido enviado"),
        "reportUser": MessageLookupByLibrary.simpleMessage("Informe usuario"),
        "reports": MessageLookupByLibrary.simpleMessage("Aplazamiento"),
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("Restablecer contraseña"),
        "retry": MessageLookupByLibrary.simpleMessage("Rever"),
        "ring": MessageLookupByLibrary.simpleMessage("Anillo"),
        "roomAlreadyInCall":
            MessageLookupByLibrary.simpleMessage("Habitación ya en llamada"),
        "roomCounter": MessageLookupByLibrary.simpleMessage("Mostrador"),
        "saveLogin":
            MessageLookupByLibrary.simpleMessage("Guardar inicio de sesión"),
        "search": MessageLookupByLibrary.simpleMessage("Buscar"),
        "seconds":
            MessageLookupByLibrary.simpleMessage("Artículos de segunda clase"),
        "send": MessageLookupByLibrary.simpleMessage("Enviar"),
        "sendCodeToMyEmail": MessageLookupByLibrary.simpleMessage(
            "Enviar código a mi correo electrónico"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Mensaje"),
        "sessionEnd": MessageLookupByLibrary.simpleMessage("Sesión final"),
        "setMaxBroadcastMembers": MessageLookupByLibrary.simpleMessage(
            "Establecer miembros de transmisión Max"),
        "setMaxGroupMembers": MessageLookupByLibrary.simpleMessage(
            "Establecer miembros del grupo Max"),
        "setMaxMessageForwardAndShare": MessageLookupByLibrary.simpleMessage(
            "Establezca el mensaje máximo hacia adelante y comparta"),
        "setNewPrivacyPolicyUrl": MessageLookupByLibrary.simpleMessage(
            "Establecer una nueva URL de política de privacidad"),
        "setToAdmin":
            MessageLookupByLibrary.simpleMessage("Establecer en Admin"),
        "settings": MessageLookupByLibrary.simpleMessage("Configuraciones"),
        "share": MessageLookupByLibrary.simpleMessage("Compartir"),
        "shareMediaAndLocation":
            MessageLookupByLibrary.simpleMessage("Compartir medios y alquiler"),
        "shareYourStatus":
            MessageLookupByLibrary.simpleMessage("Comparte tu estado"),
        "showHistory":
            MessageLookupByLibrary.simpleMessage("Exhibir la historia"),
        "showMedia": MessageLookupByLibrary.simpleMessage("Programa de medios"),
        "soon": MessageLookupByLibrary.simpleMessage("Pronto"),
        "spamOrScamDescription": MessageLookupByLibrary.simpleMessage(
            "Spam o estafa: esta opción sería para utilizar una cuenta de aplazamiento que envía mensajes de spam, anuncios no solicitados o intentan estafar a los Oters."),
        "star": MessageLookupByLibrary.simpleMessage("Estrella"),
        "starMessage":
            MessageLookupByLibrary.simpleMessage("Mensaje de estrella"),
        "starredMessage":
            MessageLookupByLibrary.simpleMessage("Mensaje protagonizado"),
        "starredMessages":
            MessageLookupByLibrary.simpleMessage("Mensajes protagonizados"),
        "startChat": MessageLookupByLibrary.simpleMessage("Inicie el gato"),
        "startNewChatWithYou": MessageLookupByLibrary.simpleMessage(
            "Empiece a chat nuevo contigo"),
        "status": MessageLookupByLibrary.simpleMessage("Estado"),
        "storageAndData":
            MessageLookupByLibrary.simpleMessage("Almacenamiento y datos"),
        "stories": MessageLookupByLibrary.simpleMessage("Historias"),
        "storyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Historia creada con éxito"),
        "success": MessageLookupByLibrary.simpleMessage("Éxito"),
        "successfullyDownloadedIn":
            MessageLookupByLibrary.simpleMessage("Descargado con éxito en"),
        "supportChatSoon":
            MessageLookupByLibrary.simpleMessage("Soporte de gatos (pronto)"),
        "tapADeviceToEditOrLogOut": MessageLookupByLibrary.simpleMessage(
            "Toque un dispositivo para editar o iniciar sesión."),
        "tapForPhoto":
            MessageLookupByLibrary.simpleMessage("Toque para la foto"),
        "tapToSelectAnIcon": MessageLookupByLibrary.simpleMessage(
            "Toque para seleccionar el icono de año"),
        "tellAFriend": MessageLookupByLibrary.simpleMessage("Dile a un amigo"),
        "textFieldHint":
            MessageLookupByLibrary.simpleMessage("Escriba un mensaje ..."),
        "textMessages":
            MessageLookupByLibrary.simpleMessage("Mensajes de texto"),
        "thereIsFileHasSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "Hay que el archivo tiene tamaño más grande que el tamaño permitido"),
        "thereIsVideoSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "Hay tamaño de video más grande que el tamaño permitido"),
        "timeout": MessageLookupByLibrary.simpleMessage("Se acabó el tiempo"),
        "titleIsRequired":
            MessageLookupByLibrary.simpleMessage("Se requiere título"),
        "today": MessageLookupByLibrary.simpleMessage("Hoy"),
        "total": MessageLookupByLibrary.simpleMessage("Total"),
        "totalMessages":
            MessageLookupByLibrary.simpleMessage("Mensajes totales"),
        "totalRooms":
            MessageLookupByLibrary.simpleMessage("Habitaciones totales"),
        "totalVisits": MessageLookupByLibrary.simpleMessage("Visitas totales"),
        "typing": MessageLookupByLibrary.simpleMessage("Escribiendo ..."),
        "unBlock": MessageLookupByLibrary.simpleMessage("Un bloque"),
        "unBlockUser":
            MessageLookupByLibrary.simpleMessage("Usuario de ablock"),
        "unMute": MessageLookupByLibrary.simpleMessage("Un mudo"),
        "unStar": MessageLookupByLibrary.simpleMessage("Una estrella"),
        "update": MessageLookupByLibrary.simpleMessage("Actualizar"),
        "updateBroadcastTitle": MessageLookupByLibrary.simpleMessage(
            "Actualizar título de transmisión"),
        "updateFeedBackEmail": MessageLookupByLibrary.simpleMessage(
            "Actualizar el correo electrónico de comentarios"),
        "updateGroupDescription": MessageLookupByLibrary.simpleMessage(
            "Actualizar la descripción del grupo"),
        "updateGroupDescriptionWillUpdateAllGroupMembers":
            MessageLookupByLibrary.simpleMessage(
                "Actualizar la descripción del grupo actualizará a todos los miembros del grupo"),
        "updateGroupTitle": MessageLookupByLibrary.simpleMessage(
            "Actualizar el título del grupo"),
        "updateImage":
            MessageLookupByLibrary.simpleMessage("Imagen de actualización"),
        "updateNickname": MessageLookupByLibrary.simpleMessage("ACTUALIZACIÓN"),
        "updateTitle":
            MessageLookupByLibrary.simpleMessage("Título de actualización"),
        "updateTitleTo":
            MessageLookupByLibrary.simpleMessage("Actualizar el título de"),
        "updateYourBio":
            MessageLookupByLibrary.simpleMessage("Actualiza tu orgánico"),
        "updateYourName":
            MessageLookupByLibrary.simpleMessage("Actualiza tu nombre"),
        "updateYourPassword":
            MessageLookupByLibrary.simpleMessage("Actualiza tu contraseña"),
        "updateYourProfile":
            MessageLookupByLibrary.simpleMessage("Actualiza tu perfil"),
        "updatedAt": MessageLookupByLibrary.simpleMessage("Actualizado en"),
        "upgradeToAdmin":
            MessageLookupByLibrary.simpleMessage("Actualizar al administrador"),
        "userAction":
            MessageLookupByLibrary.simpleMessage("Acción del usuario"),
        "userAlreadyRegister":
            MessageLookupByLibrary.simpleMessage("El usuario ya se registra"),
        "userDeviceSessionEndDeviceDeleted":
            MessageLookupByLibrary.simpleMessage(
                "Dispositivo del dispositivo del usuario Dispositivo eliminado"),
        "userEmailNotFound": MessageLookupByLibrary.simpleMessage(
            "Correo electrónico del usuario no encontrado"),
        "userInfo":
            MessageLookupByLibrary.simpleMessage("Información de usuario"),
        "userPage": MessageLookupByLibrary.simpleMessage("Página de usuario"),
        "userProfile":
            MessageLookupByLibrary.simpleMessage("Perfil de usuario"),
        "userRegisterStatusNotAcceptedYet":
            MessageLookupByLibrary.simpleMessage(
                "Estado de registro de usuario aún no aceptado"),
        "users": MessageLookupByLibrary.simpleMessage("Usuarios"),
        "usersAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Los usuarios agregaron con éxito"),
        "vMessageInfoTrans":
            MessageLookupByLibrary.simpleMessage("Mensaje de información"),
        "vMessagesInfoTrans":
            MessageLookupByLibrary.simpleMessage("Mensajes de información"),
        "verified": MessageLookupByLibrary.simpleMessage("Verificado"),
        "verifiedAt": MessageLookupByLibrary.simpleMessage("Verificado en"),
        "video": MessageLookupByLibrary.simpleMessage("Video"),
        "videoCallMessages": MessageLookupByLibrary.simpleMessage(
            "Llamar a los mensajes de video"),
        "videoMessages":
            MessageLookupByLibrary.simpleMessage("Mensajes de video"),
        "visits": MessageLookupByLibrary.simpleMessage("Visitas"),
        "voiceCallMessage":
            MessageLookupByLibrary.simpleMessage("Mensaje de llamadas de voz"),
        "voiceCallMessages":
            MessageLookupByLibrary.simpleMessage("Mensajes de llamadas de voz"),
        "voiceMessages":
            MessageLookupByLibrary.simpleMessage("Mensajes de voz"),
        "wait2MinutesToSendMail": MessageLookupByLibrary.simpleMessage(
            "Espere 2 minutos para enviar correo"),
        "waitingList": MessageLookupByLibrary.simpleMessage("Lista de espera"),
        "weHighRecommendToDownloadThisUpdate":
            MessageLookupByLibrary.simpleMessage(
                "Recomendamos altos descargar esta actualización"),
        "web": MessageLookupByLibrary.simpleMessage("Web"),
        "welcome": MessageLookupByLibrary.simpleMessage("Bienvenido"),
        "whenUsingMobileData":
            MessageLookupByLibrary.simpleMessage("Cuando se usa datos móviles"),
        "whenUsingWifi":
            MessageLookupByLibrary.simpleMessage("Cuando se usa Wi-Fi"),
        "whileAuthCanFindYou": MessageLookupByLibrary.simpleMessage(
            "Mientras que la autenticación no puede encontrarte"),
        "windows": MessageLookupByLibrary.simpleMessage("Windows"),
        "writeACaption":
            MessageLookupByLibrary.simpleMessage("Escribe un subtítulo ..."),
        "yes": MessageLookupByLibrary.simpleMessage("Sí"),
        "yesterday": MessageLookupByLibrary.simpleMessage("Ayer"),
        "you": MessageLookupByLibrary.simpleMessage("Tú"),
        "youAreAboutToDeleteThisUserFromYourList":
            MessageLookupByLibrary.simpleMessage(
                "Estás a punto de eliminar a este usuario de su lista"),
        "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList":
            MessageLookupByLibrary.simpleMessage(
                "Estás a punto de eliminar tu nacimiento"),
        "youAreAboutToDismissesToMember": MessageLookupByLibrary.simpleMessage(
            "Estás a punto de disfmo al miembro"),
        "youAreAboutToKick":
            MessageLookupByLibrary.simpleMessage("Estás a punto de patear"),
        "youAreAboutToUpgradeToAdmin": MessageLookupByLibrary.simpleMessage(
            "Estás a punto de actualizar al administrador"),
        "youDontHaveAccess":
            MessageLookupByLibrary.simpleMessage("No tienes acceso"),
        "youInPublicSearch":
            MessageLookupByLibrary.simpleMessage("Tú en la búsqueda pública"),
        "youNotParticipantInThisGroup":
            MessageLookupByLibrary.simpleMessage("No participa en este grupo"),
        "yourAccountBlocked": MessageLookupByLibrary.simpleMessage(
            "Tu cumpleaños ha sido prohibido"),
        "yourAccountDeleted": MessageLookupByLibrary.simpleMessage(
            "Tu cumpleaños ha sido eliminado"),
        "yourAccountIsUnderReview": MessageLookupByLibrary.simpleMessage(
            "Tu nacimiento está bajo revisión"),
        "yourAreAboutToLogoutFromThisAccount":
            MessageLookupByLibrary.simpleMessage(
                "Estás a punto de cerrar sesión desde este acuerdo"),
        "yourLastSeen": MessageLookupByLibrary.simpleMessage("Tu último visto"),
        "yourLastSeenInChats":
            MessageLookupByLibrary.simpleMessage("Tu último visto en gatos"),
        "yourProfileAppearsInPublicSearchAndAddingForGroups":
            MessageLookupByLibrary.simpleMessage(
                "Su perfil aparece en la búsqueda pública y la adición de grupos"),
        "yourSessionIsEndedPleaseLoginAgain":
            MessageLookupByLibrary.simpleMessage(
                "¡Su sesión es y finalizó por favor inicie sesión nuevamente!"),
        "yourStory": MessageLookupByLibrary.simpleMessage("Tu historia")
      };
}
