// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ja locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ja';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Dashboard": MessageLookupByLibrary.simpleMessage("ダッシュボード"),
        "about": MessageLookupByLibrary.simpleMessage("情報"),
        "aboutToBlockUserWithConsequences": MessageLookupByLibrary.simpleMessage(
            "このユーザーをブロックしようとしています。彼にチャットを送信したり、グループまたはブロードキャストに追加したりすることはできません！"),
        "accepted": MessageLookupByLibrary.simpleMessage("承認済み"),
        "account": MessageLookupByLibrary.simpleMessage("アカウント"),
        "actions": MessageLookupByLibrary.simpleMessage("アクション"),
        "addMembers": MessageLookupByLibrary.simpleMessage("メンバーを追加"),
        "addNewStory": MessageLookupByLibrary.simpleMessage("新しいストーリーを追加"),
        "addParticipants": MessageLookupByLibrary.simpleMessage("参加者を追加"),
        "addedYouToNewBroadcast":
            MessageLookupByLibrary.simpleMessage("新しいブロードキャストに追加されました"),
        "admin": MessageLookupByLibrary.simpleMessage("管理者"),
        "adminNotification": MessageLookupByLibrary.simpleMessage("管理者通知"),
        "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself":
            MessageLookupByLibrary.simpleMessage(
                "すべてのデータはバックアップされ、データの管理は必要ありません。ログアウトして再ログインすると、ウェブバージョンと同じチャットが表示されます"),
        "allDeletedMessages":
            MessageLookupByLibrary.simpleMessage("すべての削除済みメッセージ"),
        "allowAds": MessageLookupByLibrary.simpleMessage("広告を許可"),
        "allowCalls": MessageLookupByLibrary.simpleMessage("通話を許可"),
        "allowCreateBroadcast":
            MessageLookupByLibrary.simpleMessage("ブロードキャストの作成を許可"),
        "allowCreateGroups": MessageLookupByLibrary.simpleMessage("グループの作成を許可"),
        "allowDesktopLogin":
            MessageLookupByLibrary.simpleMessage("デスクトップログインを許可"),
        "allowMobileLogin": MessageLookupByLibrary.simpleMessage("モバイルログインを許可"),
        "allowSendMedia": MessageLookupByLibrary.simpleMessage("メディアの送信を許可"),
        "allowWebLogin": MessageLookupByLibrary.simpleMessage("Webログインを許可"),
        "alreadyHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("既にアカウントをお持ちですか？"),
        "android": MessageLookupByLibrary.simpleMessage("Android"),
        "appMembers": MessageLookupByLibrary.simpleMessage("アプリのメンバー"),
        "appleStoreAppUrl":
            MessageLookupByLibrary.simpleMessage("Apple StoreアプリURL"),
        "areYouSure": MessageLookupByLibrary.simpleMessage("本当に確認しますか？"),
        "areYouSureToBlock":
            MessageLookupByLibrary.simpleMessage("ユーザーをブロックしますか："),
        "areYouSureToLeaveThisGroupThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "このグループを退席しますか？このアクションは元に戻せません"),
        "areYouSureToPermitYourCopyThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage("コピーを許可しますか？このアクションは元に戻せません"),
        "areYouSureToReportUserToAdmin":
            MessageLookupByLibrary.simpleMessage("このユーザーについて管理者に報告する確認していますか？"),
        "areYouSureToUnBlock":
            MessageLookupByLibrary.simpleMessage("本当にブロックを解除しますか？"),
        "areYouWantToMakeVideoCall":
            MessageLookupByLibrary.simpleMessage("ビデオ通話を開始しますか？"),
        "areYouWantToMakeVoiceCall":
            MessageLookupByLibrary.simpleMessage("音声通話を開始しますか？"),
        "audio": MessageLookupByLibrary.simpleMessage("オーディオ"),
        "audioCall": MessageLookupByLibrary.simpleMessage("音声通話"),
        "back": MessageLookupByLibrary.simpleMessage("戻る"),
        "banAt": MessageLookupByLibrary.simpleMessage("ブロック日時"),
        "banTo": MessageLookupByLibrary.simpleMessage("ブロック解除日時"),
        "bio": MessageLookupByLibrary.simpleMessage("バイオ"),
        "block": MessageLookupByLibrary.simpleMessage("ブロック"),
        "blockUser": MessageLookupByLibrary.simpleMessage("ユーザーをブロック"),
        "blocked": MessageLookupByLibrary.simpleMessage("ブロック済み"),
        "blockedUsers": MessageLookupByLibrary.simpleMessage("ブロックされたユーザー"),
        "broadcast": MessageLookupByLibrary.simpleMessage("ブロードキャスト"),
        "broadcastInfo": MessageLookupByLibrary.simpleMessage("ブロードキャスト情報"),
        "broadcastMembers":
            MessageLookupByLibrary.simpleMessage("ブロードキャストメンバー"),
        "broadcastName": MessageLookupByLibrary.simpleMessage("ブロードキャスト名"),
        "broadcastParticipants":
            MessageLookupByLibrary.simpleMessage("ブロードキャスト参加者"),
        "broadcastSettings":
            MessageLookupByLibrary.simpleMessage("ブロードキャストの設定"),
        "callNotAllowed": MessageLookupByLibrary.simpleMessage("通話が許可されていません"),
        "callTimeoutInSeconds":
            MessageLookupByLibrary.simpleMessage("通話タイムアウト（秒）"),
        "calls": MessageLookupByLibrary.simpleMessage("通話"),
        "camera": MessageLookupByLibrary.simpleMessage("カメラ"),
        "cancel": MessageLookupByLibrary.simpleMessage("キャンセル"),
        "canceled": MessageLookupByLibrary.simpleMessage("キャンセル"),
        "changeSubject": MessageLookupByLibrary.simpleMessage("サブジェクトを変更"),
        "chat": MessageLookupByLibrary.simpleMessage("チャット"),
        "chats": MessageLookupByLibrary.simpleMessage("チャット"),
        "checkForUpdates": MessageLookupByLibrary.simpleMessage("アップデートの確認"),
        "chooseHowAutomaticDownloadWorks":
            MessageLookupByLibrary.simpleMessage("自動ダウンロードの動作方法を選択"),
        "chooseRoom": MessageLookupByLibrary.simpleMessage("ルームを選択"),
        "clear": MessageLookupByLibrary.simpleMessage("クリア"),
        "clearCallsConfirm":
            MessageLookupByLibrary.simpleMessage("電話をクリアすることを確認しますか？"),
        "clearChat": MessageLookupByLibrary.simpleMessage("チャットをクリア"),
        "clickToAddGroupDescription":
            MessageLookupByLibrary.simpleMessage("クリックしてグループ説明を追加"),
        "clickToSee": MessageLookupByLibrary.simpleMessage("見るにはクリック"),
        "clickToSeeAllUserCountries":
            MessageLookupByLibrary.simpleMessage("すべてのユーザー国を表示するにはクリック"),
        "clickToSeeAllUserDevicesDetails":
            MessageLookupByLibrary.simpleMessage("すべてのユーザーデバイスの詳細を表示するにはクリック"),
        "clickToSeeAllUserInformations":
            MessageLookupByLibrary.simpleMessage("すべてのユーザー情報を表示するにはクリック"),
        "clickToSeeAllUserMessagesDetails":
            MessageLookupByLibrary.simpleMessage("すべてのユーザーメッセージの詳細を表示するにはクリック"),
        "clickToSeeAllUserReports":
            MessageLookupByLibrary.simpleMessage("すべてのユーザーレポートを表示するにはクリック"),
        "clickToSeeAllUserRoomsDetails":
            MessageLookupByLibrary.simpleMessage("すべてのユーザールームの詳細を表示するにはクリック"),
        "close": MessageLookupByLibrary.simpleMessage("閉じる"),
        "codeHasBeenExpired":
            MessageLookupByLibrary.simpleMessage("コードは期限切れです"),
        "configureYourAccountPrivacy":
            MessageLookupByLibrary.simpleMessage("アカウントのプライバシーを設定"),
        "confirmPassword": MessageLookupByLibrary.simpleMessage("パスワードの確認"),
        "confirmPasswordMustHaveValue":
            MessageLookupByLibrary.simpleMessage("パスワードの確認は必須です"),
        "congregationsYourAccountHasBeenAccepted":
            MessageLookupByLibrary.simpleMessage("おめでとうございます、アカウントが承認されました"),
        "connecting": MessageLookupByLibrary.simpleMessage("接続中..."),
        "contactInfo": MessageLookupByLibrary.simpleMessage("連絡先情報"),
        "contactUs": MessageLookupByLibrary.simpleMessage("お問い合わせ"),
        "copy": MessageLookupByLibrary.simpleMessage("コピー"),
        "countries": MessageLookupByLibrary.simpleMessage("国"),
        "country": MessageLookupByLibrary.simpleMessage("国"),
        "create": MessageLookupByLibrary.simpleMessage("作成"),
        "createBroadcast": MessageLookupByLibrary.simpleMessage("ブロードキャストを作成"),
        "createGroup": MessageLookupByLibrary.simpleMessage("グループを作成"),
        "createMediaStory":
            MessageLookupByLibrary.simpleMessage("メディアストーリーを作成"),
        "createStory": MessageLookupByLibrary.simpleMessage("ストーリーを作成"),
        "createTextStory": MessageLookupByLibrary.simpleMessage("テキストストーリーを作成"),
        "createYourStory": MessageLookupByLibrary.simpleMessage("あなたのストーリーを作成"),
        "createdAt": MessageLookupByLibrary.simpleMessage("作成日時"),
        "creator": MessageLookupByLibrary.simpleMessage("作成者"),
        "currentDevice": MessageLookupByLibrary.simpleMessage("現在のデバイス"),
        "dashboard": MessageLookupByLibrary.simpleMessage("ダッシュボード"),
        "dataPrivacy": MessageLookupByLibrary.simpleMessage("データプライバシー"),
        "delete": MessageLookupByLibrary.simpleMessage("削除"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("チャットを削除"),
        "deleteFromAll": MessageLookupByLibrary.simpleMessage("全てから削除"),
        "deleteFromMe": MessageLookupByLibrary.simpleMessage("自分から削除"),
        "deleteMember": MessageLookupByLibrary.simpleMessage("メンバーを削除"),
        "deleteMyAccount": MessageLookupByLibrary.simpleMessage("アカウントを削除"),
        "deleteThisDeviceDesc": MessageLookupByLibrary.simpleMessage(
            "このデバイスを削除すると、このデバイスからログアウトが即時に実行されます"),
        "deleteUser": MessageLookupByLibrary.simpleMessage("ユーザーを削除"),
        "deleteYouCopy": MessageLookupByLibrary.simpleMessage("コピーを削除"),
        "deleted": MessageLookupByLibrary.simpleMessage("削除済み"),
        "deletedAt": MessageLookupByLibrary.simpleMessage("削除日時"),
        "delivered": MessageLookupByLibrary.simpleMessage("配信済み"),
        "description": MessageLookupByLibrary.simpleMessage("説明"),
        "descriptionIsRequired":
            MessageLookupByLibrary.simpleMessage("説明は必須です"),
        "desktopAndOtherDevices":
            MessageLookupByLibrary.simpleMessage("デスクトップおよびその他のデバイス"),
        "deviceHasBeenLogoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage("デバイスはすべてのデバイスからログアウトしました"),
        "deviceStatus": MessageLookupByLibrary.simpleMessage("デバイスの状態"),
        "devices": MessageLookupByLibrary.simpleMessage("デバイス"),
        "directChat": MessageLookupByLibrary.simpleMessage("ダイレクトチャット"),
        "directRooms": MessageLookupByLibrary.simpleMessage("ダイレクトルーム"),
        "dismissedToMemberBy": MessageLookupByLibrary.simpleMessage("メンバーに降格："),
        "dismissesToMember": MessageLookupByLibrary.simpleMessage("メンバーに降格"),
        "docs": MessageLookupByLibrary.simpleMessage("ドキュメント"),
        "done": MessageLookupByLibrary.simpleMessage("完了"),
        "download": MessageLookupByLibrary.simpleMessage("ダウンロード"),
        "downloading": MessageLookupByLibrary.simpleMessage("ダウンロード中..."),
        "edit": MessageLookupByLibrary.simpleMessage("編集"),
        "email": MessageLookupByLibrary.simpleMessage("メールアドレス"),
        "emailMustBeValid":
            MessageLookupByLibrary.simpleMessage("メールアドレスは有効である必要があります"),
        "emailNotValid": MessageLookupByLibrary.simpleMessage("メールアドレスが無効です"),
        "enterNameAndAddOptionalProfilePicture":
            MessageLookupByLibrary.simpleMessage("名前を入力し、オプションのプロフィール画像を追加"),
        "error": MessageLookupByLibrary.simpleMessage("エラー"),
        "exitGroup": MessageLookupByLibrary.simpleMessage("グループを退出"),
        "explainWhatHappens":
            MessageLookupByLibrary.simpleMessage("ここに何が起こるかを説明してください"),
        "feedBackEmail": MessageLookupByLibrary.simpleMessage("フィードバックメール"),
        "fileHasBeenSavedTo":
            MessageLookupByLibrary.simpleMessage("ファイルは保存されました："),
        "fileMessages": MessageLookupByLibrary.simpleMessage("ファイルメッセージ"),
        "files": MessageLookupByLibrary.simpleMessage("ファイル"),
        "finished": MessageLookupByLibrary.simpleMessage("終了"),
        "forRequest": MessageLookupByLibrary.simpleMessage("リクエスト用"),
        "forgetPassword": MessageLookupByLibrary.simpleMessage("パスワードを忘れました"),
        "forgetPasswordExpireTime":
            MessageLookupByLibrary.simpleMessage("パスワードリセットの有効期限"),
        "forward": MessageLookupByLibrary.simpleMessage("転送"),
        "fullName": MessageLookupByLibrary.simpleMessage("フルネーム"),
        "gallery": MessageLookupByLibrary.simpleMessage("ギャラリー"),
        "globalSearch": MessageLookupByLibrary.simpleMessage("グローバル検索"),
        "googlePlayAppUrl":
            MessageLookupByLibrary.simpleMessage("Google PlayアプリURL"),
        "group": MessageLookupByLibrary.simpleMessage("グループ"),
        "groupCreatedBy": MessageLookupByLibrary.simpleMessage("グループ作成者："),
        "groupDescription": MessageLookupByLibrary.simpleMessage("グループ説明"),
        "groupIcon": MessageLookupByLibrary.simpleMessage("グループアイコン"),
        "groupInfo": MessageLookupByLibrary.simpleMessage("グループ情報"),
        "groupMembers": MessageLookupByLibrary.simpleMessage("グループメンバー"),
        "groupName": MessageLookupByLibrary.simpleMessage("グループ名"),
        "groupParticipants": MessageLookupByLibrary.simpleMessage("グループ参加者"),
        "groupSettings": MessageLookupByLibrary.simpleMessage("グループの設定"),
        "groupWith": MessageLookupByLibrary.simpleMessage("グループと"),
        "harassmentOrBullyingDescription": MessageLookupByLibrary.simpleMessage(
            "嫌がらせやいじめ：このオプションを使用して、嫌がらせメッセージ、脅迫、またはその他のいじめ行為を行っている個人を報告できます。"),
        "help": MessageLookupByLibrary.simpleMessage("ヘルプ"),
        "hiIamUse": MessageLookupByLibrary.simpleMessage("こんにちは、私は使います"),
        "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "このオプションが無効になっている場合、チャットブロードキャストの作成がブロックされます"),
        "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "このオプションが無効になっている場合、チャットグループの作成がブロックされます"),
        "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "このオプションが無効になっている場合、デスクトップログインまたは登録（WindowsおよびmacOS）がブロックされます"),
        "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly":
            MessageLookupByLibrary.simpleMessage(
                "このオプションが有効になっている場合、Google広告バナーがチャットに表示されます"),
        "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "このオプションが無効になっている場合、チャットファイル、画像、ビデオ、および位置情報の送信がブロックされます"),
        "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "このオプションが無効になっている場合、Webログインまたは登録がブロックされます"),
        "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed":
            MessageLookupByLibrary.simpleMessage(
                "このオプションが有効になっている場合、ビデオ通話および音声通話が許可されます"),
        "image": MessageLookupByLibrary.simpleMessage("画像"),
        "imageMessages": MessageLookupByLibrary.simpleMessage("画像メッセージ"),
        "images": MessageLookupByLibrary.simpleMessage("画像"),
        "inAppAlerts": MessageLookupByLibrary.simpleMessage("アプリ内アラート"),
        "inCall": MessageLookupByLibrary.simpleMessage("通話中"),
        "inappropriateContentDescription": MessageLookupByLibrary.simpleMessage(
            "不適切なコンテンツ：ユーザーは、性的に露骨なコンテンツ、ヘイトスピーチ、またはコミュニティの基準に違反するその他のコンテンツを報告するためにこのオプションを選択できます。"),
        "info": MessageLookupByLibrary.simpleMessage("情報"),
        "infoMessages": MessageLookupByLibrary.simpleMessage("情報メッセージ"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("無効なコード"),
        "invalidLoginData": MessageLookupByLibrary.simpleMessage("無効なログインデータ"),
        "ios": MessageLookupByLibrary.simpleMessage("iOS"),
        "joinedAt": MessageLookupByLibrary.simpleMessage("参加日時"),
        "joinedBy": MessageLookupByLibrary.simpleMessage("参加者："),
        "kickMember": MessageLookupByLibrary.simpleMessage("メンバーをキック"),
        "kickedBy": MessageLookupByLibrary.simpleMessage("キックされました："),
        "language": MessageLookupByLibrary.simpleMessage("言語"),
        "lastActiveFrom": MessageLookupByLibrary.simpleMessage("最後にアクティブ"),
        "leaveGroup": MessageLookupByLibrary.simpleMessage("グループを退席"),
        "leaveGroupAndDeleteYourMessageCopy":
            MessageLookupByLibrary.simpleMessage("グループを退席してメッセージのコピーを削除"),
        "leftTheGroup": MessageLookupByLibrary.simpleMessage("グループを退席"),
        "linkADeviceSoon":
            MessageLookupByLibrary.simpleMessage("デバイスをリンクする（近日公開予定）"),
        "linkByQrCode": MessageLookupByLibrary.simpleMessage("QRコードでリンク"),
        "linkedDevices": MessageLookupByLibrary.simpleMessage("リンクされたデバイス"),
        "links": MessageLookupByLibrary.simpleMessage("リンク"),
        "loading": MessageLookupByLibrary.simpleMessage("読み込み中..."),
        "location": MessageLookupByLibrary.simpleMessage("位置情報"),
        "locationMessages": MessageLookupByLibrary.simpleMessage("位置情報メッセージ"),
        "logOut": MessageLookupByLibrary.simpleMessage("ログアウト"),
        "login": MessageLookupByLibrary.simpleMessage("ログイン"),
        "loginAgain": MessageLookupByLibrary.simpleMessage("再度ログイン！"),
        "loginNowAllowedNowPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "現在、ログインは許可されていません。後でもう一度お試しください。"),
        "logoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage("すべてのデバイスからログアウトしますか？"),
        "macOs": MessageLookupByLibrary.simpleMessage("macOS"),
        "makeCall": MessageLookupByLibrary.simpleMessage("通話を開始"),
        "media": MessageLookupByLibrary.simpleMessage("メディア"),
        "mediaLinksAndDocs":
            MessageLookupByLibrary.simpleMessage("メディア、リンク、およびドキュメント"),
        "member": MessageLookupByLibrary.simpleMessage("メンバー"),
        "members": MessageLookupByLibrary.simpleMessage("メンバー"),
        "messageCounter": MessageLookupByLibrary.simpleMessage("メッセージカウンター"),
        "messageHasBeenDeleted":
            MessageLookupByLibrary.simpleMessage("メッセージが削除されました"),
        "messageHasBeenViewed":
            MessageLookupByLibrary.simpleMessage("メッセージは閲覧されました"),
        "messageInfo": MessageLookupByLibrary.simpleMessage("メッセージ情報"),
        "messages": MessageLookupByLibrary.simpleMessage("メッセージ"),
        "microphoneAndCameraPermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone and camera permission must be accepted"),
        "microphonePermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Microphone permission must be accepted"),
        "minutes": MessageLookupByLibrary.simpleMessage("分"),
        "more": MessageLookupByLibrary.simpleMessage("詳細"),
        "mute": MessageLookupByLibrary.simpleMessage("ミュート"),
        "muteNotifications": MessageLookupByLibrary.simpleMessage("通知をミュート"),
        "myPrivacy": MessageLookupByLibrary.simpleMessage("プライバシー"),
        "name": MessageLookupByLibrary.simpleMessage("名前"),
        "nameMustHaveValue": MessageLookupByLibrary.simpleMessage("名前は必須です"),
        "needNewAccount":
            MessageLookupByLibrary.simpleMessage("新しいアカウントが必要ですか？"),
        "newBroadcast": MessageLookupByLibrary.simpleMessage("新しいブロードキャスト"),
        "newGroup": MessageLookupByLibrary.simpleMessage("新しいグループ"),
        "newPassword": MessageLookupByLibrary.simpleMessage("新しいパスワード"),
        "newPasswordMustHaveValue":
            MessageLookupByLibrary.simpleMessage("新しいパスワードは必須です"),
        "newUpdateIsAvailable":
            MessageLookupByLibrary.simpleMessage("新しいアップデートが利用可能です"),
        "next": MessageLookupByLibrary.simpleMessage("次へ"),
        "nickname": MessageLookupByLibrary.simpleMessage("ニックネーム"),
        "no": MessageLookupByLibrary.simpleMessage("いいえ"),
        "noBio": MessageLookupByLibrary.simpleMessage("バイオなし"),
        "noUpdatesAvailableNow":
            MessageLookupByLibrary.simpleMessage("現在、アップデートは利用できません"),
        "none": MessageLookupByLibrary.simpleMessage("なし"),
        "notAccepted": MessageLookupByLibrary.simpleMessage("未承認"),
        "notification": MessageLookupByLibrary.simpleMessage("通知"),
        "notificationDescription":
            MessageLookupByLibrary.simpleMessage("通知の説明"),
        "notificationTitle": MessageLookupByLibrary.simpleMessage("通知タイトル"),
        "notificationsPage": MessageLookupByLibrary.simpleMessage("通知ページ"),
        "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion":
            MessageLookupByLibrary.simpleMessage(
                "現在、読み取り専用の管理者としてログインしています。これはテストバージョンのため、行ったすべての編集が適用されません。"),
        "off": MessageLookupByLibrary.simpleMessage("オフ"),
        "offline": MessageLookupByLibrary.simpleMessage("オフライン"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "oldPassword": MessageLookupByLibrary.simpleMessage("古いパスワード"),
        "on": MessageLookupByLibrary.simpleMessage("オン"),
        "oneSeenMessage": MessageLookupByLibrary.simpleMessage("一度見たメッセージ"),
        "online": MessageLookupByLibrary.simpleMessage("オンライン"),
        "orLoginWith": MessageLookupByLibrary.simpleMessage("または次でログイン"),
        "other": MessageLookupByLibrary.simpleMessage("その他"),
        "otherCategoryDescription": MessageLookupByLibrary.simpleMessage(
            "その他：上記のカテゴリに簡単に適合しない違反に使用できるキャッチオールカテゴリです。ユーザーが追加の詳細を提供できるように、テキストボックスを含めるのが役立つかもしれません。"),
        "otpCode": MessageLookupByLibrary.simpleMessage("OTPコード"),
        "password": MessageLookupByLibrary.simpleMessage("パスワード"),
        "passwordHasBeenChanged":
            MessageLookupByLibrary.simpleMessage("パスワードが変更されました"),
        "passwordIsRequired":
            MessageLookupByLibrary.simpleMessage("パスワードは必須です"),
        "passwordMustHaveValue":
            MessageLookupByLibrary.simpleMessage("パスワードは必須です"),
        "passwordNotMatch":
            MessageLookupByLibrary.simpleMessage("パスワードが一致しません"),
        "peerUserDeviceOffline":
            MessageLookupByLibrary.simpleMessage("相手のユーザーデバイスがオフラインです"),
        "peerUserInCallNow":
            MessageLookupByLibrary.simpleMessage("相手のユーザーは現在通話中です"),
        "pending": MessageLookupByLibrary.simpleMessage("保留中"),
        "phone": MessageLookupByLibrary.simpleMessage("電話"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("プライバシーポリシー"),
        "privacyUrl": MessageLookupByLibrary.simpleMessage("プライバシーURL"),
        "profile": MessageLookupByLibrary.simpleMessage("プロフィール"),
        "promotedToAdminBy": MessageLookupByLibrary.simpleMessage("管理者に昇格："),
        "public": MessageLookupByLibrary.simpleMessage("公開"),
        "read": MessageLookupByLibrary.simpleMessage("既読"),
        "recentUpdate": MessageLookupByLibrary.simpleMessage("最近の更新"),
        "recentUpdates": MessageLookupByLibrary.simpleMessage("最近のアップデート"),
        "recording": MessageLookupByLibrary.simpleMessage("録音中..."),
        "register": MessageLookupByLibrary.simpleMessage("登録"),
        "registerMethod": MessageLookupByLibrary.simpleMessage("登録方法"),
        "registerStatus": MessageLookupByLibrary.simpleMessage("登録ステータス"),
        "rejected": MessageLookupByLibrary.simpleMessage("拒否"),
        "repliedToYourSelf": MessageLookupByLibrary.simpleMessage("自分に返信しました"),
        "reply": MessageLookupByLibrary.simpleMessage("返信"),
        "replyToYourSelf": MessageLookupByLibrary.simpleMessage("自分に返信"),
        "report": MessageLookupByLibrary.simpleMessage("通報"),
        "reportHasBeenSubmitted":
            MessageLookupByLibrary.simpleMessage("レポートが送信されました"),
        "reportUser": MessageLookupByLibrary.simpleMessage("ユーザーを通報"),
        "reports": MessageLookupByLibrary.simpleMessage("レポート"),
        "resetPassword": MessageLookupByLibrary.simpleMessage("パスワードをリセット"),
        "retry": MessageLookupByLibrary.simpleMessage("再試行"),
        "ring": MessageLookupByLibrary.simpleMessage("呼び出し中..."),
        "roomAlreadyInCall":
            MessageLookupByLibrary.simpleMessage("ルームは既に通話中です"),
        "roomCounter": MessageLookupByLibrary.simpleMessage("ルームカウンター"),
        "saveLogin": MessageLookupByLibrary.simpleMessage("ログイン情報を保存"),
        "search": MessageLookupByLibrary.simpleMessage("検索"),
        "seconds": MessageLookupByLibrary.simpleMessage("秒"),
        "send": MessageLookupByLibrary.simpleMessage("送信"),
        "sendCodeToMyEmail":
            MessageLookupByLibrary.simpleMessage("コードを私のメールに送信"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("メッセージを送信"),
        "sessionEnd": MessageLookupByLibrary.simpleMessage("セッション終了"),
        "setMaxBroadcastMembers":
            MessageLookupByLibrary.simpleMessage("最大ブロードキャストメンバー数を設定"),
        "setMaxGroupMembers":
            MessageLookupByLibrary.simpleMessage("最大グループメンバー数を設定"),
        "setMaxMessageForwardAndShare":
            MessageLookupByLibrary.simpleMessage("最大メッセージ転送および共有を設定"),
        "setNewPrivacyPolicyUrl":
            MessageLookupByLibrary.simpleMessage("新しいプライバシーポリシーのURLを設定"),
        "setToAdmin": MessageLookupByLibrary.simpleMessage("管理者に設定"),
        "settings": MessageLookupByLibrary.simpleMessage("設定"),
        "share": MessageLookupByLibrary.simpleMessage("共有"),
        "shareMediaAndLocation":
            MessageLookupByLibrary.simpleMessage("メディアと位置情報を共有"),
        "shareYourStatus": MessageLookupByLibrary.simpleMessage("ステータスを共有"),
        "showHistory": MessageLookupByLibrary.simpleMessage("履歴を表示"),
        "showMedia": MessageLookupByLibrary.simpleMessage("メディアを表示"),
        "soon": MessageLookupByLibrary.simpleMessage("近日公開"),
        "spamOrScamDescription": MessageLookupByLibrary.simpleMessage(
            "スパムまたは詐欺：このオプションは、スパムメッセージ、不要な広告を送信しているアカウント、または他のユーザーを騙そうとしているアカウントを報告するためのものです。"),
        "star": MessageLookupByLibrary.simpleMessage("スター"),
        "starMessage": MessageLookupByLibrary.simpleMessage("メッセージをスター付け"),
        "starredMessage": MessageLookupByLibrary.simpleMessage("スター付きメッセージ"),
        "starredMessages": MessageLookupByLibrary.simpleMessage("スター付きメッセージ"),
        "startChat": MessageLookupByLibrary.simpleMessage("チャットを開始"),
        "startNewChatWithYou":
            MessageLookupByLibrary.simpleMessage("あなたと新しいチャットを開始"),
        "status": MessageLookupByLibrary.simpleMessage("ステータス"),
        "storageAndData": MessageLookupByLibrary.simpleMessage("ストレージとデータ"),
        "stories": MessageLookupByLibrary.simpleMessage("ストーリー"),
        "storyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("ストーリーの作成に成功しました"),
        "success": MessageLookupByLibrary.simpleMessage("成功"),
        "successfullyDownloadedIn":
            MessageLookupByLibrary.simpleMessage("ダウンロードが正常に完了しました"),
        "supportChatSoon":
            MessageLookupByLibrary.simpleMessage("サポートチャット（近日公開予定）"),
        "tapADeviceToEditOrLogOut":
            MessageLookupByLibrary.simpleMessage("デバイスをタップして編集またはログアウト"),
        "tapForPhoto": MessageLookupByLibrary.simpleMessage("写真をタップ"),
        "tapToSelectAnIcon":
            MessageLookupByLibrary.simpleMessage("アイコンを選択するにはタップ"),
        "tellAFriend": MessageLookupByLibrary.simpleMessage("友達に教える"),
        "textFieldHint": MessageLookupByLibrary.simpleMessage("メッセージを入力..."),
        "textMessages": MessageLookupByLibrary.simpleMessage("テキストメッセージ"),
        "thereIsFileHasSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage("許容サイズよりも大きいファイルがあります"),
        "thereIsVideoSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage("許容サイズよりも大きいビデオがあります"),
        "timeout": MessageLookupByLibrary.simpleMessage("タイムアウト"),
        "titleIsRequired": MessageLookupByLibrary.simpleMessage("タイトルが必要です"),
        "today": MessageLookupByLibrary.simpleMessage("今日"),
        "total": MessageLookupByLibrary.simpleMessage("合計"),
        "totalMessages": MessageLookupByLibrary.simpleMessage("総メッセージ数"),
        "totalRooms": MessageLookupByLibrary.simpleMessage("合計ルーム数"),
        "totalVisits": MessageLookupByLibrary.simpleMessage("総訪問数"),
        "typing": MessageLookupByLibrary.simpleMessage("入力中..."),
        "unBlock": MessageLookupByLibrary.simpleMessage("ブロック解除"),
        "unBlockUser": MessageLookupByLibrary.simpleMessage("ユーザーのブロックを解除"),
        "unMute": MessageLookupByLibrary.simpleMessage("ミュート解除"),
        "unStar": MessageLookupByLibrary.simpleMessage("スターを解除"),
        "update": MessageLookupByLibrary.simpleMessage("更新"),
        "updateBroadcastTitle":
            MessageLookupByLibrary.simpleMessage("ブロードキャストのタイトルを更新"),
        "updateFeedBackEmail":
            MessageLookupByLibrary.simpleMessage("フィードバックメールを更新"),
        "updateGroupDescription":
            MessageLookupByLibrary.simpleMessage("グループの説明を更新"),
        "updateGroupDescriptionWillUpdateAllGroupMembers":
            MessageLookupByLibrary.simpleMessage(
                "グループの説明を更新すると、すべてのグループメンバーに適用されます"),
        "updateGroupTitle":
            MessageLookupByLibrary.simpleMessage("グループのタイトルを更新"),
        "updateImage": MessageLookupByLibrary.simpleMessage("画像を更新"),
        "updateNickname": MessageLookupByLibrary.simpleMessage("ニックネームを更新"),
        "updateTitle": MessageLookupByLibrary.simpleMessage("タイトルを更新"),
        "updateTitleTo": MessageLookupByLibrary.simpleMessage("タイトルを更新"),
        "updateYourBio": MessageLookupByLibrary.simpleMessage("バイオを更新"),
        "updateYourName": MessageLookupByLibrary.simpleMessage("名前を更新"),
        "updateYourPassword": MessageLookupByLibrary.simpleMessage("パスワードを更新"),
        "updateYourProfile": MessageLookupByLibrary.simpleMessage("プロフィールを更新"),
        "updatedAt": MessageLookupByLibrary.simpleMessage("更新日時"),
        "upgradeToAdmin": MessageLookupByLibrary.simpleMessage("管理者に昇格"),
        "userAction": MessageLookupByLibrary.simpleMessage("ユーザーアクション"),
        "userAlreadyRegister":
            MessageLookupByLibrary.simpleMessage("ユーザーは既に登録済みです"),
        "userDeviceSessionEndDeviceDeleted":
            MessageLookupByLibrary.simpleMessage("ユーザーデバイスセッション終了、デバイス削除"),
        "userEmailNotFound":
            MessageLookupByLibrary.simpleMessage("ユーザーメールアドレスが見つかりません"),
        "userInfo": MessageLookupByLibrary.simpleMessage("ユーザー情報"),
        "userPage": MessageLookupByLibrary.simpleMessage("ユーザーページ"),
        "userProfile": MessageLookupByLibrary.simpleMessage("ユーザープロファイル"),
        "userRegisterStatusNotAcceptedYet":
            MessageLookupByLibrary.simpleMessage("ユーザー登録ステータスはまだ受け入れられていません"),
        "users": MessageLookupByLibrary.simpleMessage("ユーザー"),
        "usersAddedSuccessfully":
            MessageLookupByLibrary.simpleMessage("ユーザーが正常に追加されました"),
        "vMessageInfoTrans": MessageLookupByLibrary.simpleMessage("メッセージ情報"),
        "vMessagesInfoTrans": MessageLookupByLibrary.simpleMessage("メッセージ情報"),
        "verified": MessageLookupByLibrary.simpleMessage("確認済み"),
        "verifiedAt": MessageLookupByLibrary.simpleMessage("確認済み"),
        "video": MessageLookupByLibrary.simpleMessage("ビデオ"),
        "videoCallMessages": MessageLookupByLibrary.simpleMessage("ビデオ通話メッセージ"),
        "videoMessages": MessageLookupByLibrary.simpleMessage("ビデオメッセージ"),
        "visits": MessageLookupByLibrary.simpleMessage("訪問"),
        "voiceCallMessage": MessageLookupByLibrary.simpleMessage("音声通話メッセージ"),
        "voiceCallMessages": MessageLookupByLibrary.simpleMessage("音声通話メッセージ"),
        "voiceMessages": MessageLookupByLibrary.simpleMessage("音声メッセージ"),
        "wait2MinutesToSendMail":
            MessageLookupByLibrary.simpleMessage("メールを送信するには2分待ってください"),
        "waitingList": MessageLookupByLibrary.simpleMessage("待機リスト"),
        "web": MessageLookupByLibrary.simpleMessage("ウェブ"),
        "welcome": MessageLookupByLibrary.simpleMessage("ようこそ"),
        "whenUsingMobileData":
            MessageLookupByLibrary.simpleMessage("モバイルデータを使用している場合"),
        "whenUsingWifi": MessageLookupByLibrary.simpleMessage("Wi-Fiを使用している場合"),
        "windows": MessageLookupByLibrary.simpleMessage("Windows"),
        "writeACaption": MessageLookupByLibrary.simpleMessage("キャプションを入力..."),
        "yes": MessageLookupByLibrary.simpleMessage("はい"),
        "yesterday": MessageLookupByLibrary.simpleMessage("昨日"),
        "you": MessageLookupByLibrary.simpleMessage("あなた"),
        "youAreAboutToDeleteThisUserFromYourList":
            MessageLookupByLibrary.simpleMessage("このユーザーをリストから削除しようとしています"),
        "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList":
            MessageLookupByLibrary.simpleMessage(
                "アカウントを削除しようとしています。アカウントはユーザーリストに再表示されません"),
        "youAreAboutToDismissesToMember":
            MessageLookupByLibrary.simpleMessage("メンバーを降格しようとしています"),
        "youAreAboutToKick":
            MessageLookupByLibrary.simpleMessage("キックしようとしています"),
        "youAreAboutToUpgradeToAdmin":
            MessageLookupByLibrary.simpleMessage("管理者に昇格しようとしています"),
        "youDontHaveAccess":
            MessageLookupByLibrary.simpleMessage("アクセス権がありません"),
        "youInPublicSearch":
            MessageLookupByLibrary.simpleMessage("パブリック検索での表示"),
        "youNotParticipantInThisGroup":
            MessageLookupByLibrary.simpleMessage("このグループの参加者ではありません"),
        "yourAccountBlocked":
            MessageLookupByLibrary.simpleMessage("アカウントがブロックされました"),
        "yourAccountDeleted":
            MessageLookupByLibrary.simpleMessage("アカウントが削除されました"),
        "yourAccountIsUnderReview":
            MessageLookupByLibrary.simpleMessage("アカウントは審査中です"),
        "yourAreAboutToLogoutFromThisAccount":
            MessageLookupByLibrary.simpleMessage("このアカウントからログアウトしようとしています"),
        "yourLastSeen": MessageLookupByLibrary.simpleMessage("最後に見た"),
        "yourLastSeenInChats":
            MessageLookupByLibrary.simpleMessage("チャットで最後に見た"),
        "yourProfileAppearsInPublicSearchAndAddingForGroups":
            MessageLookupByLibrary.simpleMessage(
                "あなたのプロフィールは、パブリック検索とグループへの追加に表示されます"),
        "yourSessionIsEndedPleaseLoginAgain":
            MessageLookupByLibrary.simpleMessage(
                "セッションが終了しました。もう一度ログインしてください。"),
        "yourStory": MessageLookupByLibrary.simpleMessage("あなたのストーリー")
      };
}
