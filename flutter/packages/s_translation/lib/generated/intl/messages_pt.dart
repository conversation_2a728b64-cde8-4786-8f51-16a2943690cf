// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a pt locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'pt';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Dashboard": MessageLookupByLibrary.simpleMessage("Painel"),
        "about": MessageLookupByLibrary.simpleMessage("Sobre"),
        "aboutToBlockUserWithConsequences": MessageLookupByLibrary.simpleMessage(
            "Você está prestes a bloquear esse usuário.Você não pode enviar -lhe bate -papos e não pode adicioná -lo a grupos ou transmissão!"),
        "accepted": MessageLookupByLibrary.simpleMessage("Aceito"),
        "account": MessageLookupByLibrary.simpleMessage("Conta"),
        "actions": MessageLookupByLibrary.simpleMessage("Ações"),
        "addMembers": MessageLookupByLibrary.simpleMessage("Adicionar membros"),
        "addNewStory":
            MessageLookupByLibrary.simpleMessage("Adicione nova história"),
        "addParticipants":
            MessageLookupByLibrary.simpleMessage("Adicionar participantes"),
        "addedYouToNewBroadcast": MessageLookupByLibrary.simpleMessage(
            "Adicionou você a uma nova transmissão"),
        "admin": MessageLookupByLibrary.simpleMessage("Administrador"),
        "adminNotification": MessageLookupByLibrary.simpleMessage(
            "Notificação de administrador"),
        "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself":
            MessageLookupByLibrary.simpleMessage(
                "Todos os dados foram backup que você não deseja gerenciar o Salvar os dados por si mesmo!Se você fazer logout e fazer login novamente, verá todos os bate -papos iguais para a versão da web"),
        "allDeletedMessages": MessageLookupByLibrary.simpleMessage(
            "Todas as mensagens excluídas"),
        "allowAds": MessageLookupByLibrary.simpleMessage("Permitir anúncios"),
        "allowCalls": MessageLookupByLibrary.simpleMessage("Permitir chamadas"),
        "allowCreateBroadcast":
            MessageLookupByLibrary.simpleMessage("Permitir criar transmissão"),
        "allowCreateGroups":
            MessageLookupByLibrary.simpleMessage("Permitir grupos de criação"),
        "allowDesktopLogin":
            MessageLookupByLibrary.simpleMessage("Permitir login de mesa"),
        "allowMobileLogin":
            MessageLookupByLibrary.simpleMessage("Permitir login móvel"),
        "allowSendMedia":
            MessageLookupByLibrary.simpleMessage("Permitir a mídia de envio"),
        "allowWebLogin":
            MessageLookupByLibrary.simpleMessage("Permitir login na web"),
        "alreadyHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Já tem uma conta?"),
        "android": MessageLookupByLibrary.simpleMessage("Android"),
        "appMembers":
            MessageLookupByLibrary.simpleMessage("Membros do aplicativo"),
        "appleStoreAppUrl": MessageLookupByLibrary.simpleMessage(
            "URL do aplicativo da Apple Store"),
        "areYouSure": MessageLookupByLibrary.simpleMessage("Tem certeza?"),
        "areYouSureToBlock": MessageLookupByLibrary.simpleMessage(
            "Você tem certeza de bloquear"),
        "areYouSureToLeaveThisGroupThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "Você tem certeza de deixar este grupo?Esta ação não pode desfazer"),
        "areYouSureToPermitYourCopyThisActionCantUndo":
            MessageLookupByLibrary.simpleMessage(
                "Você tem certeza de permitir sua cópia?Esta ação não pode desfazer"),
        "areYouSureToReportUserToAdmin": MessageLookupByLibrary.simpleMessage(
            "Você tem certeza de enviar um relatório sobre esse usuário para o administrador?"),
        "areYouSureToUnBlock": MessageLookupByLibrary.simpleMessage(
            "Você tem certeza de desbloquear"),
        "areYouWantToMakeVideoCall": MessageLookupByLibrary.simpleMessage(
            "Você quer fazer videochamada?"),
        "areYouWantToMakeVoiceCall": MessageLookupByLibrary.simpleMessage(
            "Você quer fazer uma chamada de voz?"),
        "audio": MessageLookupByLibrary.simpleMessage("Áudio"),
        "audioCall": MessageLookupByLibrary.simpleMessage("Chamada de áudio"),
        "back": MessageLookupByLibrary.simpleMessage("Voltar"),
        "banAt": MessageLookupByLibrary.simpleMessage("Proibição em"),
        "banTo": MessageLookupByLibrary.simpleMessage("Proibir para"),
        "bio": MessageLookupByLibrary.simpleMessage("Bio"),
        "block": MessageLookupByLibrary.simpleMessage("Bloquear"),
        "blockUser": MessageLookupByLibrary.simpleMessage("Bloquear o usuário"),
        "blocked": MessageLookupByLibrary.simpleMessage("Bloqueado"),
        "blockedUsers":
            MessageLookupByLibrary.simpleMessage("Usuários bloqueados"),
        "broadcast": MessageLookupByLibrary.simpleMessage("Transmissão"),
        "broadcastInfo":
            MessageLookupByLibrary.simpleMessage("Informações de transmissão"),
        "broadcastMembers":
            MessageLookupByLibrary.simpleMessage("Membros da transmissão"),
        "broadcastName":
            MessageLookupByLibrary.simpleMessage("Nome da transmissão"),
        "broadcastParticipants": MessageLookupByLibrary.simpleMessage(
            "Participantes da transmissão"),
        "broadcastSettings": MessageLookupByLibrary.simpleMessage(
            "Configurações de transmissão"),
        "callNotAllowed":
            MessageLookupByLibrary.simpleMessage("Chamada não permitida"),
        "callTimeoutInSeconds": MessageLookupByLibrary.simpleMessage(
            "Timeout de chamada em segundos"),
        "calls": MessageLookupByLibrary.simpleMessage("Chamados"),
        "camera": MessageLookupByLibrary.simpleMessage("Câmera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancelar"),
        "canceled": MessageLookupByLibrary.simpleMessage("Cancelado"),
        "changeSubject":
            MessageLookupByLibrary.simpleMessage("Mudança de assunto"),
        "chat": MessageLookupByLibrary.simpleMessage("Bater papo"),
        "chats": MessageLookupByLibrary.simpleMessage("Bate -papos"),
        "checkForUpdates": MessageLookupByLibrary.simpleMessage(
            "Verifique se há atualizações"),
        "chooseAtLestOneMember": MessageLookupByLibrary.simpleMessage(
            "Escolha pelo menos um membro"),
        "chooseHowAutomaticDownloadWorks": MessageLookupByLibrary.simpleMessage(
            "Escolha como funciona o download automático"),
        "chooseRoom": MessageLookupByLibrary.simpleMessage("Escolha o quarto"),
        "clear": MessageLookupByLibrary.simpleMessage("Clear"),
        "clearCallsConfirm":
            MessageLookupByLibrary.simpleMessage("Chamadas claras confirmam"),
        "clearChat": MessageLookupByLibrary.simpleMessage("Bate -papo claro"),
        "clickToAddGroupDescription": MessageLookupByLibrary.simpleMessage(
            "Clique para adicionar descrição do grupo"),
        "clickToSee": MessageLookupByLibrary.simpleMessage("Clique para ver"),
        "clickToSeeAllUserCountries": MessageLookupByLibrary.simpleMessage(
            "Clique para ver todos os países de usuários"),
        "clickToSeeAllUserDevicesDetails": MessageLookupByLibrary.simpleMessage(
            "Clique para ver todos os detalhes dos dispositivos de usuário"),
        "clickToSeeAllUserInformations": MessageLookupByLibrary.simpleMessage(
            "Clique para ver todas as informações do usuário"),
        "clickToSeeAllUserMessagesDetails":
            MessageLookupByLibrary.simpleMessage(
                "Clique para ver todos os detalhes das mensagens do usuário"),
        "clickToSeeAllUserReports": MessageLookupByLibrary.simpleMessage(
            "Clique para ver todos os relatórios do usuário"),
        "clickToSeeAllUserRoomsDetails": MessageLookupByLibrary.simpleMessage(
            "Clique para ver todos os detalhes dos quartos do usuário"),
        "close": MessageLookupByLibrary.simpleMessage("Fechar"),
        "codeHasBeenExpired":
            MessageLookupByLibrary.simpleMessage("Código expirou"),
        "codeMustEqualToSixNumbers": MessageLookupByLibrary.simpleMessage(
            "O código deve ser igual a seis números"),
        "configureYourAccountPrivacy": MessageLookupByLibrary.simpleMessage(
            "Configure a privacidade da sua conta"),
        "confirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirme sua senha"),
        "confirmPasswordMustHaveValue": MessageLookupByLibrary.simpleMessage(
            "Confirmar a senha deve ter valor"),
        "congregationsYourAccountHasBeenAccepted":
            MessageLookupByLibrary.simpleMessage(
                "Congregações que sua conta foi aceita"),
        "connecting": MessageLookupByLibrary.simpleMessage("Conectando ..."),
        "contactInfo":
            MessageLookupByLibrary.simpleMessage("Informações de contato"),
        "contactUs": MessageLookupByLibrary.simpleMessage("Contate-nos"),
        "copy": MessageLookupByLibrary.simpleMessage("Cópia"),
        "countries": MessageLookupByLibrary.simpleMessage("Países"),
        "country": MessageLookupByLibrary.simpleMessage("País"),
        "create": MessageLookupByLibrary.simpleMessage("Criar"),
        "createBroadcast":
            MessageLookupByLibrary.simpleMessage("Crie transmissão"),
        "createGroup": MessageLookupByLibrary.simpleMessage("Criar grupo"),
        "createMediaStory":
            MessageLookupByLibrary.simpleMessage("Crie uma história de mídia"),
        "createStory": MessageLookupByLibrary.simpleMessage("Criar história"),
        "createTextStory":
            MessageLookupByLibrary.simpleMessage("Crie história de texto"),
        "createYourStory":
            MessageLookupByLibrary.simpleMessage("Crie sua história"),
        "createdAt": MessageLookupByLibrary.simpleMessage("Criado em"),
        "creator": MessageLookupByLibrary.simpleMessage("Criador"),
        "currentDevice":
            MessageLookupByLibrary.simpleMessage("Dispositivo atual"),
        "dashboard": MessageLookupByLibrary.simpleMessage("Painel"),
        "dataPrivacy":
            MessageLookupByLibrary.simpleMessage("Privacidade de dados"),
        "delete": MessageLookupByLibrary.simpleMessage("Excluir"),
        "deleteChat": MessageLookupByLibrary.simpleMessage("Exclua bate -papo"),
        "deleteFromAll":
            MessageLookupByLibrary.simpleMessage("Exclua de todos"),
        "deleteFromMe": MessageLookupByLibrary.simpleMessage("Exclua de mim"),
        "deleteMember": MessageLookupByLibrary.simpleMessage("Excluir membro"),
        "deleteMyAccount":
            MessageLookupByLibrary.simpleMessage("Exclua minha conta"),
        "deleteThisDeviceDesc": MessageLookupByLibrary.simpleMessage(
            "Excluindo este dispositivo significa logout instantaneamente este dispositivo"),
        "deleteUser": MessageLookupByLibrary.simpleMessage("Excluir usuário"),
        "deleteYouCopy":
            MessageLookupByLibrary.simpleMessage("Exclua sua cópia"),
        "deleted": MessageLookupByLibrary.simpleMessage("Excluído"),
        "deletedAt": MessageLookupByLibrary.simpleMessage("Excluído em"),
        "delivered": MessageLookupByLibrary.simpleMessage("Entregue"),
        "description": MessageLookupByLibrary.simpleMessage("Descrição"),
        "descriptionIsRequired":
            MessageLookupByLibrary.simpleMessage("Descrição é necessária"),
        "desktopAndOtherDevices": MessageLookupByLibrary.simpleMessage(
            "Desktop e outros dispositivos"),
        "deviceHasBeenLogoutFromAllDevices":
            MessageLookupByLibrary.simpleMessage(
                "O dispositivo foi logout de todos os dispositivos"),
        "deviceStatus":
            MessageLookupByLibrary.simpleMessage("Status do dispositivo"),
        "devices": MessageLookupByLibrary.simpleMessage("Devices"),
        "directChat": MessageLookupByLibrary.simpleMessage("Chat direto"),
        "directRooms": MessageLookupByLibrary.simpleMessage("Salas diretas"),
        "dismissedToMemberBy":
            MessageLookupByLibrary.simpleMessage("Demitido ao membro por"),
        "dismissesToMember":
            MessageLookupByLibrary.simpleMessage("Dispensa o membro"),
        "docs": MessageLookupByLibrary.simpleMessage("Documentos"),
        "done": MessageLookupByLibrary.simpleMessage("Feito"),
        "download": MessageLookupByLibrary.simpleMessage("Download"),
        "downloading": MessageLookupByLibrary.simpleMessage("Download ..."),
        "edit": MessageLookupByLibrary.simpleMessage("Editar"),
        "email": MessageLookupByLibrary.simpleMessage("E-mail"),
        "emailMustBeValid":
            MessageLookupByLibrary.simpleMessage("O email deve ser válido"),
        "emailNotValid":
            MessageLookupByLibrary.simpleMessage("Email não é válido"),
        "enterNameAndAddOptionalProfilePicture":
            MessageLookupByLibrary.simpleMessage(
                "Digite seu nome e adicione uma imagem de perfil opcional"),
        "error": MessageLookupByLibrary.simpleMessage("Erro"),
        "exitGroup": MessageLookupByLibrary.simpleMessage("Grupo de saída"),
        "explainWhatHappens": MessageLookupByLibrary.simpleMessage(
            "Explique aqui o que acontece"),
        "feedBackEmail":
            MessageLookupByLibrary.simpleMessage("E -mail de feedback"),
        "fileHasBeenSavedTo":
            MessageLookupByLibrary.simpleMessage("O arquivo foi salvo para"),
        "fileMessages":
            MessageLookupByLibrary.simpleMessage("Mensagens de arquivo"),
        "files": MessageLookupByLibrary.simpleMessage("Arquivos"),
        "finished": MessageLookupByLibrary.simpleMessage("Finalizado"),
        "forRequest": MessageLookupByLibrary.simpleMessage("Para solicitação"),
        "forgetPassword":
            MessageLookupByLibrary.simpleMessage("Esquecer a senha?"),
        "forgetPasswordExpireTime": MessageLookupByLibrary.simpleMessage(
            "Esqueça o tempo de expiração da senha"),
        "forward": MessageLookupByLibrary.simpleMessage("Avançar"),
        "fullName": MessageLookupByLibrary.simpleMessage("Nome completo"),
        "gallery": MessageLookupByLibrary.simpleMessage("Galeria"),
        "globalSearch": MessageLookupByLibrary.simpleMessage("Pesquisa global"),
        "googlePlayAppUrl": MessageLookupByLibrary.simpleMessage(
            "URL do aplicativo do Google Play"),
        "group": MessageLookupByLibrary.simpleMessage("Grupo"),
        "groupCreatedBy":
            MessageLookupByLibrary.simpleMessage("Grupo criado por"),
        "groupDescription":
            MessageLookupByLibrary.simpleMessage("Descrição do grupo"),
        "groupIcon": MessageLookupByLibrary.simpleMessage("Ícone do grupo"),
        "groupInfo":
            MessageLookupByLibrary.simpleMessage("Informações do grupo"),
        "groupMembers":
            MessageLookupByLibrary.simpleMessage("Membros do grupo"),
        "groupName": MessageLookupByLibrary.simpleMessage("Nome do grupo"),
        "groupParticipants":
            MessageLookupByLibrary.simpleMessage("Participantes do grupo"),
        "groupSettings":
            MessageLookupByLibrary.simpleMessage("Configurações do grupo"),
        "groupWith": MessageLookupByLibrary.simpleMessage("Grupo com"),
        "harassmentOrBullyingDescription": MessageLookupByLibrary.simpleMessage(
            "Assédio ou bullying: Esta opção permite que os usuários relatem indivíduos que estão segmentando eles ou outras pessoas com mensagens, ameaças ou outras formas de bullying."),
        "help": MessageLookupByLibrary.simpleMessage("Ajuda"),
        "hiIamUse": MessageLookupByLibrary.simpleMessage("Oi estou usando"),
        "ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Se esta opção estiver desativada, a criação de transmissão de bate -papo será bloqueada."),
        "ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Se esta opção estiver desativada, a criação de grupos de bate -papo será bloqueada."),
        "ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Se esta opção estiver desativada, o login ou registro da área de trabalho (Windows, Mac) será bloqueado."),
        "ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly":
            MessageLookupByLibrary.simpleMessage(
                "Se esta opção estiver desativada, o login ou registro móvel será bloqueado apenas no Android e iOS."),
        "ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Se esta opção estiver desativada, o envio de arquivos de bate -papo, imagens, vídeos e localização será bloqueado."),
        "ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked":
            MessageLookupByLibrary.simpleMessage(
                "Se esta opção estiver desativada, o login ou registro da Web será bloqueado."),
        "ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats":
            MessageLookupByLibrary.simpleMessage(
                "Se esta opção estiver ativada, o banner do Google Ads aparecerá nos bate -papos."),
        "ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed":
            MessageLookupByLibrary.simpleMessage(
                "Se esta opção estiver ativada, a chamada de vídeo e voz será permitida."),
        "image": MessageLookupByLibrary.simpleMessage("Imagem"),
        "imageMessages":
            MessageLookupByLibrary.simpleMessage("Mensagens de imagem"),
        "images": MessageLookupByLibrary.simpleMessage("Imagens"),
        "inAppAlerts":
            MessageLookupByLibrary.simpleMessage("Em alertas de aplicativos"),
        "inCall": MessageLookupByLibrary.simpleMessage("Na chamada"),
        "inappropriateContentDescription": MessageLookupByLibrary.simpleMessage(
            "Conteúdo inadequado: os usuários podem selecionar esta opção para relatar qualquer material sexualmente explícito, discurso de ódio ou outro conteúdo que viole os padrões da comunidade."),
        "info": MessageLookupByLibrary.simpleMessage("Informações"),
        "infoMessages":
            MessageLookupByLibrary.simpleMessage("Mensagens de informação"),
        "invalidCode": MessageLookupByLibrary.simpleMessage("Código inválido"),
        "invalidLoginData":
            MessageLookupByLibrary.simpleMessage("Dados de login inválidos"),
        "ios": MessageLookupByLibrary.simpleMessage("iOS"),
        "joinedAt": MessageLookupByLibrary.simpleMessage("Juntou -se em"),
        "joinedBy": MessageLookupByLibrary.simpleMessage("Juntado por"),
        "kickMember": MessageLookupByLibrary.simpleMessage("Membro do chute"),
        "kickedBy": MessageLookupByLibrary.simpleMessage("Chutado por"),
        "language": MessageLookupByLibrary.simpleMessage("Linguagem"),
        "lastActiveFrom":
            MessageLookupByLibrary.simpleMessage("Último ativo de"),
        "leaveGroup": MessageLookupByLibrary.simpleMessage("Grupo de licença"),
        "leaveGroupAndDeleteYourMessageCopy":
            MessageLookupByLibrary.simpleMessage(
                "Deixe o grupo e exclua sua cópia de mensagem"),
        "leftTheGroup": MessageLookupByLibrary.simpleMessage("Deixou o grupo"),
        "linkADeviceSoon": MessageLookupByLibrary.simpleMessage(
            "Vincular um dispositivo (em breve)"),
        "linkByQrCode":
            MessageLookupByLibrary.simpleMessage("Link por código QR"),
        "linkedDevices":
            MessageLookupByLibrary.simpleMessage("Dispositivos vinculados"),
        "links": MessageLookupByLibrary.simpleMessage("Links"),
        "loading": MessageLookupByLibrary.simpleMessage("Carregando ..."),
        "location": MessageLookupByLibrary.simpleMessage("Localização"),
        "locationMessages":
            MessageLookupByLibrary.simpleMessage("Mensagens de localização"),
        "logOut": MessageLookupByLibrary.simpleMessage("Log Out"),
        "login": MessageLookupByLibrary.simpleMessage("Conecte-se"),
        "loginAgain":
            MessageLookupByLibrary.simpleMessage("Faça login novamente!"),
        "loginNowAllowedNowPleaseTryAgainLater":
            MessageLookupByLibrary.simpleMessage(
                "Login agora permitido.Por favor, tente novamente mais tarde."),
        "logoutFromAllDevices": MessageLookupByLibrary.simpleMessage(
            "Logout de todos os dispositivos?"),
        "macOs": MessageLookupByLibrary.simpleMessage("macos"),
        "makeCall": MessageLookupByLibrary.simpleMessage("Faça uma chamada"),
        "media": MessageLookupByLibrary.simpleMessage("Mídia"),
        "mediaLinksAndDocs":
            MessageLookupByLibrary.simpleMessage("Mídia, links e documentos"),
        "member": MessageLookupByLibrary.simpleMessage("Membro"),
        "members": MessageLookupByLibrary.simpleMessage("Membros"),
        "messageCounter":
            MessageLookupByLibrary.simpleMessage("Contador de mensagens"),
        "messageHasBeenDeleted":
            MessageLookupByLibrary.simpleMessage("A mensagem foi excluída"),
        "messageHasBeenViewed":
            MessageLookupByLibrary.simpleMessage("A mensagem foi vista"),
        "messageInfo":
            MessageLookupByLibrary.simpleMessage("Informações da mensagem"),
        "messages": MessageLookupByLibrary.simpleMessage("Mensagens"),
        "microphoneAndCameraPermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "A permissão de microfone e câmera deve ser aceita"),
        "microphonePermissionMustBeAccepted":
            MessageLookupByLibrary.simpleMessage(
                "A permissão do microfone deve ser aceita"),
        "minutes": MessageLookupByLibrary.simpleMessage("Minutos"),
        "more": MessageLookupByLibrary.simpleMessage("Mais"),
        "mute": MessageLookupByLibrary.simpleMessage("Mudo"),
        "muteNotifications":
            MessageLookupByLibrary.simpleMessage("Notificações de mudo"),
        "myPrivacy": MessageLookupByLibrary.simpleMessage("Minha privacidade"),
        "name": MessageLookupByLibrary.simpleMessage("Nome"),
        "nameMustHaveValue":
            MessageLookupByLibrary.simpleMessage("Nome deve ter valor"),
        "needNewAccount":
            MessageLookupByLibrary.simpleMessage("Precisa de uma nova conta?"),
        "newBroadcast":
            MessageLookupByLibrary.simpleMessage("Nova transmissão"),
        "newGroup": MessageLookupByLibrary.simpleMessage("Novo grupo"),
        "newPassword": MessageLookupByLibrary.simpleMessage("Nova Senha"),
        "newPasswordMustHaveValue":
            MessageLookupByLibrary.simpleMessage("Nova senha deve ter valor"),
        "newUpdateIsAvailable": MessageLookupByLibrary.simpleMessage(
            "Nova atualização está disponível"),
        "next": MessageLookupByLibrary.simpleMessage("Next"),
        "nickname": MessageLookupByLibrary.simpleMessage("Apelido"),
        "no": MessageLookupByLibrary.simpleMessage("Não"),
        "noBio": MessageLookupByLibrary.simpleMessage("Sem biografia"),
        "noCodeHasBeenSendToYouToVerifyYourEmail":
            MessageLookupByLibrary.simpleMessage(
                "Nenhum código foi enviado para você para verificar seu e -mail"),
        "noUpdatesAvailableNow": MessageLookupByLibrary.simpleMessage(
            "Sem atualizações disponíveis agora"),
        "none": MessageLookupByLibrary.simpleMessage("Nenhum"),
        "notAccepted": MessageLookupByLibrary.simpleMessage("Não aceito"),
        "notification": MessageLookupByLibrary.simpleMessage("Notificação"),
        "notificationDescription":
            MessageLookupByLibrary.simpleMessage("Descrição da notificação"),
        "notificationTitle":
            MessageLookupByLibrary.simpleMessage("Título de notificação"),
        "notificationsPage":
            MessageLookupByLibrary.simpleMessage("Página de notificações"),
        "nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion":
            MessageLookupByLibrary.simpleMessage(
                "Agora você é login como administrador somente leitura.Todas as edições que você fizer não serão aplicadas devido a esta ser uma versão de teste."),
        "off": MessageLookupByLibrary.simpleMessage("Off"),
        "offline": MessageLookupByLibrary.simpleMessage("Offline"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "oldPassword": MessageLookupByLibrary.simpleMessage("Senha Antiga"),
        "on": MessageLookupByLibrary.simpleMessage("Sobre"),
        "oneSeenMessage":
            MessageLookupByLibrary.simpleMessage("Uma mensagem vista"),
        "online": MessageLookupByLibrary.simpleMessage("On-line"),
        "orLoginWith": MessageLookupByLibrary.simpleMessage("ou login com"),
        "other": MessageLookupByLibrary.simpleMessage("Other"),
        "otherCategoryDescription": MessageLookupByLibrary.simpleMessage(
            "Outro: essa categoria de captura pode ser usada para violações que não se encaixam facilmente nas categorias acima.Pode ser útil incluir uma caixa de texto para os usuários forneceram detalhes adicionais."),
        "otpCode": MessageLookupByLibrary.simpleMessage("Código OTP"),
        "password": MessageLookupByLibrary.simpleMessage("Senha"),
        "passwordHasBeenChanged":
            MessageLookupByLibrary.simpleMessage("A senha foi alterada"),
        "passwordIsRequired":
            MessageLookupByLibrary.simpleMessage("A senha é necessária"),
        "passwordMustHaveValue":
            MessageLookupByLibrary.simpleMessage("A senha deve ter valor"),
        "passwordNotMatch":
            MessageLookupByLibrary.simpleMessage("Senha não corresponde"),
        "peerUserDeviceOffline": MessageLookupByLibrary.simpleMessage(
            "Dispositivo de usuário de pares offline offline"),
        "peerUserInCallNow":
            MessageLookupByLibrary.simpleMessage("Usuário Ligue agora"),
        "pending": MessageLookupByLibrary.simpleMessage("Pendente"),
        "phone": MessageLookupByLibrary.simpleMessage("Telefone"),
        "privacyPolicy":
            MessageLookupByLibrary.simpleMessage("Política de Privacidade"),
        "privacyUrl":
            MessageLookupByLibrary.simpleMessage("URL de privacidade"),
        "profile": MessageLookupByLibrary.simpleMessage("Perfil"),
        "promotedToAdminBy":
            MessageLookupByLibrary.simpleMessage("Promovido a admin por"),
        "public": MessageLookupByLibrary.simpleMessage("Public"),
        "read": MessageLookupByLibrary.simpleMessage("Ler"),
        "recentUpdate":
            MessageLookupByLibrary.simpleMessage("Atualização recente"),
        "recentUpdates":
            MessageLookupByLibrary.simpleMessage("Atualizações recentes"),
        "recording": MessageLookupByLibrary.simpleMessage("Gravação..."),
        "register": MessageLookupByLibrary.simpleMessage("Registrar"),
        "registerMethod":
            MessageLookupByLibrary.simpleMessage("Método de registro"),
        "registerStatus":
            MessageLookupByLibrary.simpleMessage("Status de registro"),
        "rejected": MessageLookupByLibrary.simpleMessage("Rejeitado"),
        "repliedToYourSelf":
            MessageLookupByLibrary.simpleMessage("Respondeu para si mesmo"),
        "reply": MessageLookupByLibrary.simpleMessage("Responder"),
        "replyToYourSelf":
            MessageLookupByLibrary.simpleMessage("Responder a si mesmo"),
        "report": MessageLookupByLibrary.simpleMessage("Relatório"),
        "reportHasBeenSubmitted":
            MessageLookupByLibrary.simpleMessage("Seu relatório foi enviado"),
        "reportUser": MessageLookupByLibrary.simpleMessage("Relatar usuário"),
        "reports": MessageLookupByLibrary.simpleMessage("Relatórios"),
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("Redefinir senha"),
        "retry": MessageLookupByLibrary.simpleMessage("Tente novamente"),
        "ring": MessageLookupByLibrary.simpleMessage("Anel"),
        "roomAlreadyInCall":
            MessageLookupByLibrary.simpleMessage("Quarto já de chamada"),
        "roomCounter": MessageLookupByLibrary.simpleMessage("Balcão da sala"),
        "saveLogin": MessageLookupByLibrary.simpleMessage("Salvar login"),
        "search": MessageLookupByLibrary.simpleMessage("Procurar"),
        "seconds": MessageLookupByLibrary.simpleMessage("Seconds"),
        "send": MessageLookupByLibrary.simpleMessage("Enviar"),
        "sendCodeToMyEmail": MessageLookupByLibrary.simpleMessage(
            "Enviar código para meu e -mail"),
        "sendMessage": MessageLookupByLibrary.simpleMessage("Enviar mensagem"),
        "sessionEnd": MessageLookupByLibrary.simpleMessage("Final da sessão"),
        "setMaxBroadcastMembers": MessageLookupByLibrary.simpleMessage(
            "Defina os membros da transmissão máxima"),
        "setMaxGroupMembers": MessageLookupByLibrary.simpleMessage(
            "Defina os membros do Grupo Max"),
        "setMaxMessageForwardAndShare": MessageLookupByLibrary.simpleMessage(
            "Defina a mensagem Max Avan e compartilhe"),
        "setNewPrivacyPolicyUrl": MessageLookupByLibrary.simpleMessage(
            "Defina um novo URL da Política de Privacidade"),
        "setToAdmin":
            MessageLookupByLibrary.simpleMessage("Definido como admin"),
        "settings": MessageLookupByLibrary.simpleMessage("Configurações"),
        "share": MessageLookupByLibrary.simpleMessage("Compartilhar"),
        "shareMediaAndLocation": MessageLookupByLibrary.simpleMessage(
            "Compartilhar mídia e localização"),
        "shareYourStatus":
            MessageLookupByLibrary.simpleMessage("Compartilhe seu status"),
        "showHistory": MessageLookupByLibrary.simpleMessage("Mostrar história"),
        "showMedia": MessageLookupByLibrary.simpleMessage("Mostrar mídia"),
        "soon": MessageLookupByLibrary.simpleMessage("Breve"),
        "spamOrScamDescription": MessageLookupByLibrary.simpleMessage(
            "Spam ou golpe: esta opção seria para os usuários relatarem contas que estão enviando mensagens de spam, anúncios não solicitados ou estão tentando enganar outras pessoas."),
        "star": MessageLookupByLibrary.simpleMessage("Estrela"),
        "starMessage":
            MessageLookupByLibrary.simpleMessage("Mensagem de estrela"),
        "starredMessage":
            MessageLookupByLibrary.simpleMessage("Mensagem estrelada"),
        "starredMessages":
            MessageLookupByLibrary.simpleMessage("Mensagens estreladas"),
        "startChat": MessageLookupByLibrary.simpleMessage("Inicie o chat"),
        "startNewChatWithYou": MessageLookupByLibrary.simpleMessage(
            "Comece o novo bate -papo com você"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "storageAndData":
            MessageLookupByLibrary.simpleMessage("Armazenamento e dados"),
        "stories": MessageLookupByLibrary.simpleMessage("Histórias"),
        "storyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("História criada com sucesso"),
        "success": MessageLookupByLibrary.simpleMessage("Sucesso"),
        "successfullyDownloadedIn":
            MessageLookupByLibrary.simpleMessage("Baixado com sucesso"),
        "supportChatSoon":
            MessageLookupByLibrary.simpleMessage("Apoie o chat (em breve)"),
        "tapADeviceToEditOrLogOut": MessageLookupByLibrary.simpleMessage(
            "Toque em um dispositivo para editar ou fazer logon."),
        "tapForPhoto": MessageLookupByLibrary.simpleMessage("Toque para foto"),
        "tapToSelectAnIcon": MessageLookupByLibrary.simpleMessage(
            "Toque para selecionar um ícone"),
        "tellAFriend": MessageLookupByLibrary.simpleMessage("Conte a um amigo"),
        "textFieldHint":
            MessageLookupByLibrary.simpleMessage("Digite uma mensagem ..."),
        "textMessages":
            MessageLookupByLibrary.simpleMessage("Mensagens de texto"),
        "thereIsFileHasSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "Há arquivo tem tamanho maior do que o tamanho permitido"),
        "thereIsVideoSizeBiggerThanAllowedSize":
            MessageLookupByLibrary.simpleMessage(
                "Há tamanho de vídeo maior do que o tamanho permitido"),
        "timeout": MessageLookupByLibrary.simpleMessage("Tempo esgotado"),
        "titleIsRequired":
            MessageLookupByLibrary.simpleMessage("O título é necessário"),
        "today": MessageLookupByLibrary.simpleMessage("Hoje"),
        "total": MessageLookupByLibrary.simpleMessage("Total"),
        "totalMessages":
            MessageLookupByLibrary.simpleMessage("Total de mensagens"),
        "totalRooms": MessageLookupByLibrary.simpleMessage("Total de quartos"),
        "totalVisits": MessageLookupByLibrary.simpleMessage("Visitas totais"),
        "typing": MessageLookupByLibrary.simpleMessage("Digitando..."),
        "unBlock": MessageLookupByLibrary.simpleMessage("Bloco da ONU"),
        "unBlockUser":
            MessageLookupByLibrary.simpleMessage("Usuário desbloqueie"),
        "unMute":
            MessageLookupByLibrary.simpleMessage("Ativação de atividades"),
        "unStar": MessageLookupByLibrary.simpleMessage("Star da ONU"),
        "update": MessageLookupByLibrary.simpleMessage("Atualizar"),
        "updateBroadcastTitle": MessageLookupByLibrary.simpleMessage(
            "Atualizar título de transmissão"),
        "updateFeedBackEmail": MessageLookupByLibrary.simpleMessage(
            "Atualize o email de feedback"),
        "updateGroupDescription": MessageLookupByLibrary.simpleMessage(
            "Descrição do grupo de atualização"),
        "updateGroupDescriptionWillUpdateAllGroupMembers":
            MessageLookupByLibrary.simpleMessage(
                "Descrição do grupo de atualização atualizará todos os membros do grupo"),
        "updateGroupTitle": MessageLookupByLibrary.simpleMessage(
            "Título do grupo de atualização"),
        "updateImage": MessageLookupByLibrary.simpleMessage("Atualizar imagem"),
        "updateNickname":
            MessageLookupByLibrary.simpleMessage("Atualize o apelido"),
        "updateTitle":
            MessageLookupByLibrary.simpleMessage("Título de atualização"),
        "updateTitleTo":
            MessageLookupByLibrary.simpleMessage("Atualizar título para"),
        "updateYourBio":
            MessageLookupByLibrary.simpleMessage("Atualize sua biografia"),
        "updateYourName":
            MessageLookupByLibrary.simpleMessage("Atualize seu nome"),
        "updateYourPassword":
            MessageLookupByLibrary.simpleMessage("Atualize sua senha"),
        "updateYourProfile":
            MessageLookupByLibrary.simpleMessage("Atualize seu perfil"),
        "updatedAt": MessageLookupByLibrary.simpleMessage("Atualizado em"),
        "upgradeToAdmin":
            MessageLookupByLibrary.simpleMessage("Atualizar para admin"),
        "userAction": MessageLookupByLibrary.simpleMessage("Ação do usuário"),
        "userAlreadyRegister":
            MessageLookupByLibrary.simpleMessage("Usuário já se registra"),
        "userDeviceSessionEndDeviceDeleted":
            MessageLookupByLibrary.simpleMessage(
                "Dispositivo de sessão de dispositivo de usuário excluído"),
        "userEmailNotFound": MessageLookupByLibrary.simpleMessage(
            "Email de usuário não encontrado"),
        "userInfo":
            MessageLookupByLibrary.simpleMessage("Informações do usuário"),
        "userPage": MessageLookupByLibrary.simpleMessage("Página do usuário"),
        "userProfile":
            MessageLookupByLibrary.simpleMessage("Perfil de usuário"),
        "userRegisterStatusNotAcceptedYet":
            MessageLookupByLibrary.simpleMessage(
                "Status do registro do usuário ainda não aceito"),
        "users": MessageLookupByLibrary.simpleMessage("Usuários"),
        "usersAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Os usuários adicionaram com sucesso"),
        "vMessageInfoTrans":
            MessageLookupByLibrary.simpleMessage("Informações da mensagem"),
        "vMessagesInfoTrans":
            MessageLookupByLibrary.simpleMessage("Informações das mensagens"),
        "verified": MessageLookupByLibrary.simpleMessage("Verificado"),
        "verifiedAt": MessageLookupByLibrary.simpleMessage("Verificado em"),
        "video": MessageLookupByLibrary.simpleMessage("Vídeo"),
        "videoCallMessages": MessageLookupByLibrary.simpleMessage(
            "Mensagens de chamada de vídeo"),
        "videoMessages":
            MessageLookupByLibrary.simpleMessage("Mensagens de vídeo"),
        "visits": MessageLookupByLibrary.simpleMessage("Visitas"),
        "voiceCallMessage":
            MessageLookupByLibrary.simpleMessage("Mensagem de chamada de voz"),
        "voiceCallMessages":
            MessageLookupByLibrary.simpleMessage("Mensagens de chamada de voz"),
        "voiceMessages":
            MessageLookupByLibrary.simpleMessage("Mensagens de voz"),
        "wait2MinutesToSendMail": MessageLookupByLibrary.simpleMessage(
            "Aguarde 2 minutos para enviar e -mail"),
        "waitingList": MessageLookupByLibrary.simpleMessage("Lista de espera"),
        "weHighRecommendToDownloadThisUpdate":
            MessageLookupByLibrary.simpleMessage(
                "Recomendamos o download desta atualização"),
        "web": MessageLookupByLibrary.simpleMessage("Web"),
        "welcome": MessageLookupByLibrary.simpleMessage("Bem-vindo"),
        "whenUsingMobileData":
            MessageLookupByLibrary.simpleMessage("Ao usar dados móveis"),
        "whenUsingWifi": MessageLookupByLibrary.simpleMessage("Ao usar Wi-Fi"),
        "whileAuthCanFindYou": MessageLookupByLibrary.simpleMessage(
            "Enquanto a autenticação não pode encontrar você"),
        "windows": MessageLookupByLibrary.simpleMessage("Windows"),
        "writeACaption":
            MessageLookupByLibrary.simpleMessage("Escreva uma legenda ..."),
        "yes": MessageLookupByLibrary.simpleMessage("Sim"),
        "yesterday": MessageLookupByLibrary.simpleMessage("Ontem"),
        "you": MessageLookupByLibrary.simpleMessage("Você"),
        "youAreAboutToDeleteThisUserFromYourList":
            MessageLookupByLibrary.simpleMessage(
                "Você está prestes a excluir este usuário da sua lista"),
        "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList":
            MessageLookupByLibrary.simpleMessage(
                "Você está prestes a excluir sua conta, sua conta não aparecerá novamente na lista de usuários"),
        "youAreAboutToDismissesToMember": MessageLookupByLibrary.simpleMessage(
            "Você está prestes a demitir para o membro"),
        "youAreAboutToKick":
            MessageLookupByLibrary.simpleMessage("Você está prestes a chutar"),
        "youAreAboutToUpgradeToAdmin": MessageLookupByLibrary.simpleMessage(
            "Você está prestes a atualizar para o administrador"),
        "youDontHaveAccess":
            MessageLookupByLibrary.simpleMessage("Você não tem acesso"),
        "youInPublicSearch":
            MessageLookupByLibrary.simpleMessage("Você em pesquisa pública"),
        "youNotParticipantInThisGroup": MessageLookupByLibrary.simpleMessage(
            "Você não participou deste grupo"),
        "yourAccountBlocked":
            MessageLookupByLibrary.simpleMessage("Sua conta foi banida"),
        "yourAccountDeleted":
            MessageLookupByLibrary.simpleMessage("Sua conta foi excluída"),
        "yourAccountIsUnderReview":
            MessageLookupByLibrary.simpleMessage("Sua conta está em revisão"),
        "yourAreAboutToLogoutFromThisAccount":
            MessageLookupByLibrary.simpleMessage(
                "Você está prestes a fazer logout desta conta"),
        "yourLastSeen":
            MessageLookupByLibrary.simpleMessage("Seu último visto"),
        "yourLastSeenInChats": MessageLookupByLibrary.simpleMessage(
            "Seu último visto em bate -papo"),
        "yourProfileAppearsInPublicSearchAndAddingForGroups":
            MessageLookupByLibrary.simpleMessage(
                "Seu perfil aparece na pesquisa pública e adicionando grupos"),
        "yourSessionIsEndedPleaseLoginAgain":
            MessageLookupByLibrary.simpleMessage(
                "Sua sessão terminou, faça o login novamente!"),
        "yourStory": MessageLookupByLibrary.simpleMessage("Sua história")
      };
}
