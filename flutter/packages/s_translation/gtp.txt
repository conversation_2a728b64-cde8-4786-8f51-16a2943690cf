i want to translate this parts in to a language
this is the first part
{
  "@@locale": "en",
  "done": "Done",
  "loading": "Loading ...",
  "messageHasBeenDeleted": "Message has been deleted",
  "mute": "Mute",
  "cancel": "Cancel",
  "typing": "Typing...",
  "ok": "OK",
  "recording": "Recording...",
  "connecting": "Connecting...",
  "deleteYouCopy": "Delete your copy",
  "unMute": "Un mute",
  "delete": "Delete",
  "report": "Report",
  "leaveGroup": "Leave group",
  "areYouSureToPermitYourCopyThisActionCantUndo": "Are you sure to permit your copy? This action can't undo",
  "areYouSureToLeaveThisGroupThisActionCantUndo": "Are you sure to leave this group? This action can't undo",
  "leaveGroupAndDeleteYourMessageCopy": "Leave group and delete your message copy",
  "vMessageInfoTrans": "Message info",
  "updateTitleTo": "Update title to",
  "updateImage": "Update image",
  "joinedBy": "Joined by",
  "promotedToAdminBy": "Promoted to admin by",
  "dismissedToMemberBy": "Dismissed to member by",
  "leftTheGroup": "Left the group",
  "you": "You",
  "kickedBy": "Kicked by",
  "groupCreatedBy": "Group created by",
  "addedYouToNewBroadcast": "Added you to new broadcast",
  "download": "Download",
  "copy": "Copy",
  "info": "Info",
  "share": "Share",
  "forward": "Forward",
  "reply": "Reply",
  "deleteFromAll": "Delete from all",
  "deleteFromMe": "Delete from me",
  "downloading": "Downloading...",
  "fileHasBeenSavedTo": "File has been saved to",
  "online": "Online",
  "youDontHaveAccess": "You don't have access",
  "replyToYourSelf": "Reply to your self",
  "repliedToYourSelf": "Replied to your self",
  "audioCall": "Audio call",
  "ring": "Ring",
  "canceled": "Canceled",
  "timeout": "Timeout",
  "rejected": "Rejected",
  "finished": "Finished",
  "inCall": "In call",
  "sessionEnd": "Session end",
  "yesterday": "Yesterday",
  "today": "Today",
  "textFieldHint": "Type a message ...",
  "files": "Files",
  "location": "Location",
  "shareMediaAndLocation": "Share media and location",
  "thereIsVideoSizeBiggerThanAllowedSize": "There is video size bigger than allowed size",
  "thereIsFileHasSizeBiggerThanAllowedSize": "There is file has size bigger than allowed size",
  "makeCall": "Make call",
  "areYouWantToMakeVideoCall": "Are you want to make video call?",
  "areYouWantToMakeVoiceCall": "Are you want to make voice call?",
  "vMessagesInfoTrans": "Messages info",
  "star": "Star",
  "minutes": "Minutes",
  "sendMessage": "Send message",
  "deleteUser": "Delete user",
  "actions": "Actions",
  "youAreAboutToDeleteThisUserFromYourList": "You are about to delete this user from your list",
  "updateBroadcastTitle": "Update broadcast title",
  "usersAddedSuccessfully": "Users added successfully",
  "broadcastSettings": "Broadcast settings",
  "addParticipants": "Add Participants",
  "broadcastParticipants": "Broadcast Participants",
  "updateGroupDescription": "Update group description",
  "updateGroupTitle": "Update group title",
  "groupSettings": "Group settings",
  "description": "Description",
  "muteNotifications": "Mute notifications",
  "groupParticipants": "Group Participants",
  "blockUser": "Block user",
  "areYouSureToBlock": "Are you sure to block",
  "userPage": "User page",
  "starMessage": "Star message",
  "showMedia": "Show media",
  "reportUser": "Report user",
  "groupName": "group name",
  "changeSubject": "Change subject",
  "titleIsRequired": "Title is required",
  "createBroadcast": "Create Broadcast",
  "broadcastName": "Broadcast name",
  "createGroup": "Create Group",
  "forgetPassword": "Forget Password",
  "globalSearch": "Global Search",
  "dismissesToMember": "Dismisses to member",
  "setToAdmin": "Set to admin",
  "kickMember": "Kick member",
  "youAreAboutToDismissesToMember": "You are about to dismisses to member",
  "youAreAboutToKick": "You are about to kick",
  "groupMembers": "Group Members",
  "tapForPhoto": "Tap for photo",
  "weHighRecommendToDownloadThisUpdate": "We high recommend to download this update",
  "newGroup": "New group",
  "newBroadcast": "New broadcast",
  "starredMessage": "Starred message",
  "settings": "Settings",
  "chats": "CHATS",
  "recentUpdates": "Recent updates",
  "startChat": "Start chat",
  "newUpdateIsAvailable": "New update is available",
  "emailNotValid": "Email not valid",
  "passwordMustHaveValue": "Password must have value",
  "error": "Error",
  "password": "Password",
  "login": "Login",
  "needNewAccount": "Need new account?",
  "register": "Register",
  "nameMustHaveValue": "Name must have value",
  "passwordNotMatch": "Password not match",
  "name": "Name",
  "email": "Email",
  "confirmPassword": "Confirm password",
  "alreadyHaveAnAccount": "Already have an account?",
  "logOut": "Log out",
  "back": "Back",
  "sendCodeToMyEmail": "Send code to my email",
  "invalidLoginData": "Invalid login data",
  "userEmailNotFound": "User email not found",
  "yourAccountBlocked": "Your account has been baned",
  "yourAccountDeleted": "Your account has been deleted",
  "userAlreadyRegister": "User already register",
  "codeHasBeenExpired": "Code has been expired",
  "invalidCode": "Invalid code",
  "whileAuthCanFindYou": "While authentication cannot find you",
  "userRegisterStatusNotAcceptedYet": "User register status not accepted yet",
  "deviceHasBeenLogoutFromAllDevices": "Device has been logout from all devices",
  "userDeviceSessionEndDeviceDeleted": "User device session end device deleted",
  "noCodeHasBeenSendToYouToVerifyYourEmail": "No code has been send to you to verify your email",
  "roomAlreadyInCall": "Room already in call",
  "peerUserInCallNow": "User in call now",
  "callNotAllowed": "Call not allowed",
  "peerUserDeviceOffline": "Peer user device offline",
  "emailMustBeValid": "Email must be valid",
  "wait2MinutesToSendMail": "Wait 2 minutes to send mail",
  "codeMustEqualToSixNumbers": "Code must equal to six numbers",
  "newPasswordMustHaveValue": "New password must have value",
  "confirmPasswordMustHaveValue": "Confirm password must have value",
  "-----------------": "------------",
  }

  this is the second part
  {
  "congregationsYourAccountHasBeenAccepted": "Congregations your account has been accepted",
     "yourAccountIsUnderReview": "Your account is under review",
     "waitingList": "Waiting List",
     "welcome": "Welcome",
     "retry": "Retry",
     "deleteMember": "Delete member",
     "profile": "Profile",
     "broadcastInfo": "Broadcast info",
     "updateTitle": "Update title",
     "members": "Members",
     "addMembers": "Add Members",
     "success": "Success",
     "media": "Media",
     "docs": "Docs",
     "links": "Links",
     "soon": "Soon",
     "unStar": "Un star",
     "updateGroupDescriptionWillUpdateAllGroupMembers": "Update group description will update all group members",
     "updateNickname": "Update nickname",
     "groupInfo": "Group info",
     "youNotParticipantInThisGroup": "You not participant in this group",
     "search": "Search",
     "mediaLinksAndDocs": "Media, Links, and Docs",
     "starredMessages": "Starred Messages",
     "nickname": "Nickname",
     "none": "None",
     "yes": "Yes",
     "no": "No",
     "exitGroup": "Exit Group",
     "clickToAddGroupDescription": "Click to add group description",
     "unBlockUser": "Un block user",
     "areYouSureToUnBlock": "Are you sure to un block",
     "contactInfo": "Contact info",
     "audio": "Audio",
     "video": "Video",
     "hiIamUse": "Hi iam use",
     "on": "On",
     "off": "Off",
     "unBlock": "Un Block",
     "block": "Block",
     "chooseAtLestOneMember": "Choose at lest one member",
     "close": "Close",
     "next": "Next",
     "appMembers": "App members",
     "create": "Create",
     "upgradeToAdmin": "Upgrade to admin",
     "update": "Update",
     "deleteChat": "Delete chat",
     "clearChat": "Clear chat",
     "showHistory": "Show history",
     "groupIcon": "Group icon",
     "tapToSelectAnIcon": "Tap to select an icon",
     "groupDescription": "Group description",
     "more": "More",
     "messageInfo": "Message info",
     "successfullyDownloadedIn": "Successfully downloaded in",
     "delivered": "Delivered",
     "read": "Read",
     "orLoginWith": "or login with",
     "resetPassword": "Reset password",
     "otpCode": "OTP Code",
     "newPassword": "New password",
     "areYouSure": "Are you sure?",
     "broadcastMembers": "Broadcast Members",
     "phone": "Phone",
     "users": "Users",
     "calls": "Calls",
     "yourAreAboutToLogoutFromThisAccount": "Your are about to logout from this account",
     "noUpdatesAvailableNow": "No updates available now",
     "dataPrivacy": "Data privacy",
     "allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself": "All data has been backup you don\\'t want need to manage save the data by your self! if you logout and login again you will see all chats same for web version",
     "account": "Account",
     "linkedDevices": "Linked Devices",
     "storageAndData": "Storage and Data",
     "tellAFriend": "Tell a friend",
     "help": "Help",
     "blockedUsers": "Blocked users",
     "inAppAlerts": "In app alerts",
     "language": "Language",
     "adminNotification": "Admin notification",
     "checkForUpdates": "Check for updates",
     "linkByQrCode": "Link By Qr Code",
     "deviceStatus": "Device status",
     "desktopAndOtherDevices": "Desktop, and other devices",
     "linkADeviceSoon": "Link a Device (Soon)",
     "lastActiveFrom": "Last active from",
     "tapADeviceToEditOrLogOut": "Tap a device to edit or log out.",
     "contactUs": "Contact Us",
     "supportChatSoon": "Support chat (Soon)",
     "updateYourName": "Update your name",
     "updateYourBio": "Update your bio",
     "edit": "Edit",
     "about": "About",
     "oldPassword": "Old password",
     "deleteMyAccount": "Delete my account",
     "passwordHasBeenChanged": "Password has been changed",
     "logoutFromAllDevices": "Logout from all devices?",
     "updateYourPassword": "Update your password",
     "enterNameAndAddOptionalProfilePicture": "Enter your name and add an optional profile picture",
     "privacyPolicy": "Privacy policy",
     "chat": "Chat",
     "send": "Send",
     "reportHasBeenSubmitted": "Your report has been submitted",
     "offline": "Offline",
     "harassmentOrBullyingDescription": "Harassment or Bullying: This option allows users to report individuals who are targeting them or others with harassing messages, threats, or other forms of bullying.",
     "spamOrScamDescription": "Spam or Scam: This option would be for users to report accounts that are sending spam messages, unsolicited advertisements, or are attempting to scam others.",
     "areYouSureToReportUserToAdmin": "Are you sure to submit report about this user to the admin?",
     "groupWith": "Group with",
     "inappropriateContentDescription": "Inappropriate Content: Users can select this option to report any sexually explicit material, hate speech, or other content that violates community standards.",
     "otherCategoryDescription": "Other: This catch-all category can be used for violations that don't easily fit into the above categories. It might be helpful to include a text box for users to provide additional details.",
     "explainWhatHappens": "Explain here what happens",
     "loginAgain": "Login again!",
     "yourSessionIsEndedPleaseLoginAgain": "Your session is ended please login again!",
     "aboutToBlockUserWithConsequences": "You are about to block this user. You can't send him chats and can't add him to groups or broadcast!",
     "youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList": "You are about to delete your account your account will not appears again in the users list",
     "admin": "Admin",
     "member": "Member",
     "creator": "Creator",
     "currentDevice": "Current device",
     "visits": "Visits",
     "chooseRoom": "Choose room",
     "deleteThisDeviceDesc": "Deleting this device means instantly logout this device",
     "youAreAboutToUpgradeToAdmin": "You are about to upgrade to admin"
   }