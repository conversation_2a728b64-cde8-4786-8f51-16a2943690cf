name: v_chat_firebase_fcm
description: v chat firebase notifiactions push implementations for android and ios using firebase messaging
version: 1.2.1
homepage: https://v-chat-sdk.github.io/vchat-v2-docs/docs/intro
issue_tracker: https://github.com/hatemragab/v_chat_sdk/issues
repository: https://github.com/hatemragab/v_chat_sdk/tree/master/push_providers/v_chat_one_signal
publish_to: none
environment:
  sdk: '>=2.17.0 <4.0.0'
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  v_chat_sdk_core:
    path: ../v_chat_sdk_core
  firebase_core: ^3.8.0
  firebase_messaging: ^15.1.5
  eraser: ^3.0.0
  app_badge_plus: ^1.1.6
  http: ^1.2.2
  v_platform: ^2.1.4
  shared_preferences: ^2.3.3
  encrypt: 5.0.1

dev_dependencies:
  lints: ^5.0.0

flutter:

platforms:
  android:
  ios:
  web:
  windows:
  macos:
  linux:
