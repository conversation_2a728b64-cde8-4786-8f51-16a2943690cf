// // Copyright 2023, the hatemragab project author.
// // All rights reserved. Use of this source code is governed by a
// // MIT license that can be found in the LICENSE file.
//
// import 'package:flutter/material.dart';
// import 'package:v_chat_message_page/src/theme/theme.dart';
//
// class DirectionItemHolder extends StatelessWidget {
//   final Widget child;
//   final bool isMeSender;
//
//   const DirectionItemHolder({
//     Key? key,
//     required this.child,
//     required this.isMeSender,
//   }) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return context.vMessageTheme.messageItemHolder(
//       context,
//       isMeSender,
//       child,
//     );
//   }
// }
