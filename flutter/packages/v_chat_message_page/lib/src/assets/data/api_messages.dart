// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

final fakeApiMessages = [
  {
    "_id": "1",
    "sId": "63ac6118079aac2880035ed3",
    "sName": "user1",
    "sImg": "default_user_chat_image.png",
    "plm": "Other",
    "rId": "rid1",
    "c": "first message",
    "mT": VMessageType.text.name,
    "msgAtt": null,
    "rTo": null,
    "sAt": null,
    "dAt": null,
    "forId": null,
    "dltAt": null,
    "pBId": null,
    "lId": "65a285ce-ed5f-402c-ae28-c65ce361e592",
    "linkAtt": null,
    "peerData": null,
    "createdAt": "2022-12-26T19:32:26.613Z",
    "updatedAt": "2022-12-26T19:32:26.613Z",
    "isStared": false
  },
  {
    "_id": "2",
    "sId": "63ac6eea079aac2880035f8d",
    "sName": "user1",
    "sImg": "default_user_chat_image.png",
    "plm": "Other",
    "rId": "rid1",
    "c": "second message",
    "mT": "text",
    "msgAtt": null,
    "rTo": null,
    "sAt": null,
    "dAt": null,
    "forId": null,
    "dltAt": null,
    "pBId": null,
    "lId": "65a285ce-ed5f-402c-ae28-c65ce361e593",
    "linkAtt": null,
    "peerData": null,
    "createdAt": "2022-12-26T19:35:26.613Z",
    "updatedAt": "2022-12-26T19:35:26.613Z",
    "isStared": false
  },
];
