// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'local_messages.dart';

final fakeLocalRooms = [
  {
    "tb_r_id": "rid1",
    "tb_r_room_type": "s",
    "tb_r_title": "user 2",
    "tb_r_img": "default_user_image.png",
    "tb_r_is_archived": false,
    "tb_r_is_muted": false,
    "tb_r_peer_id": "user2Id",
    "tb_r_peer_identifier": "<EMAIL>",
    "tb_r_blocker_id": null,
    "tb_r_title_en": "user 2",
    "tb_r_un_counter": 0,
    "tb_r_nick_name": null,
    "tb_r_created_at": "2022-12-28T14:39:32Z",
    ...fakeLocalMessages.first
  }
];
