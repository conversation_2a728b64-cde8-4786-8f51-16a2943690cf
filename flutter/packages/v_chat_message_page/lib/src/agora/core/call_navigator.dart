// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:s_translation/generated/l10n.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_message_page/src/agora/pages/pick_up/pick_up.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_platform/v_platform.dart';

import '../../../v_chat_message_page.dart';
import '../pages/call/call_page.dart';

final vDefaultCallNavigator = VCallNavigator(
  toPickUp: (context) async {
    // if (!VPlatforms.isMobile) return;
    // context.toPage(PickUp(
    //   callModel: callModel,
    //   localization: VCallLocalization.fromEnglish(),
    // ));
  },
  toCall: (context, dto) async {
    debugPrint(
        'LOG::CallNavigator.toCall called with dto: isCaller=${dto.isCaller}, callId=${dto.callId}, roomId=${dto.roomId}');
    if (!VPlatforms.isMobile) {
      debugPrint('LOG::Not mobile platform, returning');
      return;
    }
    final micRes = await [Permission.microphone].request();
    debugPrint(
        'LOG::Microphone permission result: ${micRes[Permission.microphone]}');
    if (dto.isVideoEnable) {
      final cameraRes = await [Permission.camera].request();
      debugPrint(
          'LOG::Camera permission result: ${cameraRes[Permission.camera]}');
      if (cameraRes[Permission.camera] != PermissionStatus.granted) {
        debugPrint('LOG::Camera permission denied, showing error');
        VAppAlert.showErrorSnackBar(
          message: S.of(context).microphoneAndCameraPermissionMustBeAccepted,
          context: context,
        );
        return;
      }
    }
    if (micRes[Permission.microphone] != PermissionStatus.granted) {
      debugPrint('LOG::Microphone permission denied, showing error');
      VAppAlert.showErrorSnackBar(
        message: S.of(context).microphonePermissionMustBeAccepted,
        context: context,
      );
      return;
    }
    debugPrint('LOG::Permissions granted, navigating to VCallPage');
    context.toPage(
      VCallPage(
        dto: dto,
      ),
    );
    debugPrint('LOG::VCallPage navigation completed');
  },
);
