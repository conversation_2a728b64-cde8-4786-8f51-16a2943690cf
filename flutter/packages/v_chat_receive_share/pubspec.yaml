name: v_chat_receive_share
description: v chat receive share will make your app support share from other apps
version: 1.3.0
homepage: https://v-chat-sdk.github.io/vchat-v2-docs/docs/intro
issue_tracker: https://github.com/hatemragab/v_chat_sdk/issues
repository: https://github.com/hatemragab/v_chat_sdk/tree/master/v_shared_packages/v_chat_receive_share
publish_to: none
environment:
  sdk: '>=2.17.0 <4.0.0'
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  v_chat_sdk_core:
    path: ../v_chat_sdk_core
  share_handler: ^0.0.21
  v_chat_media_editor:
    path: ../v_chat_media_editor
  v_platform: ^2.1.4
  intl: ^0.20.2
dev_dependencies:
  flutter_lints: ^5.0.0
  lints: ^5.0.0


flutter:

platforms:
  android:
  ios:
  web:
  windows:
  macos:
