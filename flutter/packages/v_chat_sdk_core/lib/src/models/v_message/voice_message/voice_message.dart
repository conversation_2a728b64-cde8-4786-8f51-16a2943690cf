// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:convert';

import 'package:chopper/chopper.dart';
import 'package:v_chat_sdk_core/src/local_db/tables/message_table.dart';
import 'package:v_chat_sdk_core/src/utils/v_message_constants.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

class VVoiceMessage extends VBaseMessage {
  final VMessageVoiceData data;

  VVoiceMessage({
    required super.id,
    required super.senderId,
    required super.linkAtt,
    required super.emitStatus,
    required super.senderName,
    required super.senderImageThumb,
    required super.platform,
    required super.roomId,
    required super.content,
    required super.contentTr,
    required super.messageType,
    required super.localId,
    required super.createdAt,
    required super.isEncrypted,
    required super.updatedAt,
    required super.replyTo,
    required super.seenAt,
    required super.deliveredAt,
    required super.forwardId,
    required super.allDeletedAt,
    required super.parentBroadcastId,
    required super.isStared,
    required this.data,
  });

  VVoiceMessage.fromRemoteMap(super.map)
      : data = VMessageVoiceData.fromMap(
          map['msgAtt'] as Map<String, dynamic>,
        ),
        super.fromRemoteMap();

  VVoiceMessage.fromLocalMap(super.map)
      : data = VMessageVoiceData.fromMap(
          jsonDecode(map[MessageTable.columnAttachment] as String)
              as Map<String, dynamic>,
        ),
        super.fromLocalMap();

  // @override
  // Map<String, dynamic> toRemoteMap() {
  //   return {...super.toRemoteMap(), 'msgAtt': voiceUrlAttachment.toMap()};
  // }

  @override
  Map<String, dynamic> toLocalMap({
    bool withOutConTr = false,
    bool withOutIsDownload = false,
  }) {
    return {
      ...super.toLocalMap(),
      MessageTable.columnAttachment: jsonEncode(data.toMap()),
    };
  }

  @override
  List<PartValue> toListOfPartValue() {
    return [
      ...super.toListOfPartValue(),
      PartValue(
        'attachment',
        jsonEncode(data.toMap()),
      ),
    ];
  }

  VVoiceMessage.buildMessage({
    required super.roomId,
    required this.data,
    required String content,
    super.forwardId,
    super.broadcastId,
    super.replyTo,
    super.isOneSeen,
  }) : super.buildMessage(
          isEncrypted: false,
          linkAtt: null,
          content: "${VMessageConstants.thisContentIsVoice} $content",
          messageType: VMessageType.voice,
        );
}
