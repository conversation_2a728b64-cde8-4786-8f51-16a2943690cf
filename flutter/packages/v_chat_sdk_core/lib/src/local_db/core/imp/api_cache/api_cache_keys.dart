// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

abstract class ApiCacheKeys {
  static const mySingleInfo = "api/single/id/my-info/";
  static const myGroupInfo = "api/group/id/my-info/";
  static const myBroadcastInfo = "api/broadcast/id/my-info/";
  static const groupIdInfo = "api/group/id/info/";
  static const singleIdInfo = "api/single/id/info/";
  static const usersSearch = "api/users/search";
}
