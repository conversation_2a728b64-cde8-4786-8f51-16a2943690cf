# V Chat SDK Core Package

Welcome to the V Chat SDK Core Package, a comprehensive and potent engine for integrating chat functionality into your application. Built to cater to varying needs, whether your project involves a simplistic chat interface or a complex, fully-featured communication platform, our SDK can flexibly adapt.

## Key Features

- **HTTP and Web Socket Support:** The SDK is equipped with robust networking capabilities, with support for both HTTP and WebSocket protocols, catering to the demands of real-time communication.
- **Internationalization (i18n):** The SDK inherently supports multiple languages and acknowledges regional differences, making it capable of serving a diverse, global user base.
- **Local Storage:** With the capacity to preserve chat histories and user data on the device, the SDK ensures a continuous, seamless user experience.
- **Modular Design:** The SDK is intentionally designed to be highly modular. You can cherry-pick the components you need, eliminating unnecessary elements. This design philosophy extends to our core package as well, offering the tools to create a completely custom chat application.

## The V Chat SDK Ecosystem

V Chat SDK is not just a standalone tool; it's part of a broader ecosystem of packages designed to accelerate your chat app development process. These packages are built to work seamlessly together with the core SDK, enabling you to craft your desired chat experience faster and more conveniently.

For a deep dive into each package and how they complement the core SDK, please refer to our detailed [documentation](https://v-chat-sdk.github.io/vchat-v2-docs/docs/intro/).

## Getting Started

To get started with the V Chat SDK:

1. **Installation:** Install the package to your project using your preferred package manager.
2. **Configuration:** Follow our setup instructions for configuring the SDK, available in our [documentation](https://v-chat-sdk.github.io/vchat-v2-docs/docs/flutter/core_package).
3. **Usage:** Utilize the modules and APIs provided by the SDK to implement chat functionality according to your requirements. Our [API documentation](https://v-chat-sdk.github.io/vchat-v2-docs/docs/flutter/core_package) provides extensive guides for this.

## Documentation

We provide extensive documentation, complete with feature guides, module details, API usage guides, code samples, examples, and step-by-step tutorials. Explore our complete [documentation](https://v-chat-sdk.github.io/vchat-v2-docs/docs/intro/) to get started.

## Support

For issues, bugs, feature requests, or queries, please visit our [issues page](https://github.com/hatemragab/v_chat_sdk/issues) to report or find more information.

---

Remember, it's always beneficial to use the latest version of the V Chat SDK to benefit from our latest features and improvements.

---

The V Chat SDK Core Package is proudly developed and maintained by the V Chat Team.
