## Share Extension Implemented

### 1. AndroidManifest.xml
- Added an intent filter to handle `text/plain` data, allowing the app to receive shared links and other text-based content.
- Changed the app label to `@super_up_app` as requested.

### 2. v_chat_receive_share.dart
- Modified the `_handleOnNewShare` function to process the `content` field of the `SharedMedia` object.
- The app now creates a `VTextMessage` when text content is shared and forwards it to the selected chat rooms.

### 3. main.dart
- Added the `vInitReceiveShareHandler()` function call to the `main` function to initialize the share handler.
- Imported the `v_chat_receive_share` package.

### 4. MainActivity.kt
- Created a `MainActivity.kt` file to ensure the app's lifecycle and intents are handled correctly on Android.

### 5. AndroidManifest.xml
- Updated `AndroidManifest.xml` to reference the new `MainActivity.kt` file.

### 6. v_chat_receive_share.dart
- Added a delay to the `_handleOnNewShare` function in `v_chat_receive_share.dart` to allow the app to initialize before navigating to the "Choose room" page.
